<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>东京商城</title>

  <style>
    *{
      margin: 0;
      padding: 0;
    }
    .page{
      position: relative;
      width: 100%;
      height: 100vh;
      max-width: 540px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url('static/pagebg.png') center center/cover no-repeat;
    }
    .logo{
      width: 40vw;
      max-width: 260px;
      display: block;
      margin: 0 auto;
    }
    .banner{
      width: 82vw;
      display: block;
      margin: 4vh auto 0;
      max-width: 306px;
    }
    .btn{
      display: block;
      margin: 20vh auto 0;
      background: #FFCC7A;
      color: #C23C3D;
      font-size: 20px;
      height: 50px;
      font-weight: 600;
      border-radius: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
    }
  </style>
</head>

<body>
  <div class="page">
    <div>
      <img src="static/logo.png" class="logo">
      <img src="static/banner.png" class="banner">
      <a href="" class="btn" id="btn">立即下载</a>
    </div>
  </div>
  <script>
    if (/(Android)/i.test(navigator.userAgent)) {
      var list = [
        'https://appdown0302.s3.ap-northeast-3.amazonaws.com/download/shop.apk',
        'https://apptioa.s3.ap-northeast-1.amazonaws.com/download/shop.apk',
        'https://prodmall.s3.ap-northeast-1.amazonaws.com/download/shop.apk'
      ]
      var index = Math.floor((Math.random() * list.length));
      document.getElementById("btn").href = list[index];
    } else {
      fetch('https://qtapi.bszx123.com/api/front/appUpgrade', {
        'method': 'get',
        'headers': { 'content-type': 'application/json; charset=UTF-8'}
      }).then(res => {return res.json()}).then(data => {
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          document.getElementById("btn").href = data.data.appIOSUrl;
        } 
        // else if (/(Android)/i.test(navigator.userAgent)) {
        //   document.getElementById("btn").href = data.data.appAndroidUrl;
        // }
      });
    }
</script>
</body>

</html>