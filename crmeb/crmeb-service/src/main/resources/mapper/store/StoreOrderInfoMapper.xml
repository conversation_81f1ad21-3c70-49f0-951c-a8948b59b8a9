<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.StoreOrderInfoDao">

    <select id="getSalesNumByDateAndProductId" resultType="java.lang.Integer">
        select IFNULL(sum(pay_num), 0) as pay_num from eb_store_order_info
        where order_id in (SELECT id FROM `eb_store_order` where paid = 1 and date_format(create_time, '%Y-%m-%d') = #{date})
        and product_id = #{proId}
    </select>

    <select id="getSalesByDateAndProductId" resultType="java.math.BigDecimal">
        select IFNULL(sum(vip_price), 0) as price from eb_store_order_info
        where order_id in (SELECT id FROM `eb_store_order` where paid = 1 and date_format(create_time, '%Y-%m-%d') = #{date})
        and product_id = #{proId}
    </select>

</mapper>
