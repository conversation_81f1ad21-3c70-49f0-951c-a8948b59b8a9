<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.UserRechargeDao">
<!--    根据类型获取该类型充值金额-->
    <select id="getSumByType" resultType="java.math.BigDecimal" parameterType="com.zbkj.common.vo.DateLimitUtilVo">
        select sum(price) as price from eb_user_recharge
        where paid = 1
        <if test="dateLimitUtilVo.startTime != null and dateLimitUtilVo.startTime != '' ">
             and create_time &gt;= #{dateLimitUtilVo.startTime}
        </if>
        <if test="dateLimitUtilVo.endTime != null and dateLimitUtilVo.endTime != '' ">
            and create_time &lt;= #{dateLimitUtilVo.endTime}
        </if>
        <if test="groupId != null and groupId != '' ">
            and group_id = #{groupId}
        </if>
    </select>
<!--    获取退款总额-->
    <select id="getSumByRefund" resultType="java.math.BigDecimal">
        select sum(refund_price) as price from eb_user_recharge
        where  refund_price > 0 and paid = 1
    </select>
</mapper>
