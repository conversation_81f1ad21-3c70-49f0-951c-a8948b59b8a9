package com.zbkj.service.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.constants.TaskConstants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.combination.StoreCombination;
import com.zbkj.common.model.combination.StorePink;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.model.finance.UserRecharge;
import com.zbkj.common.model.order.StoreOrder;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.wechat.WechatPayInfo;
import com.zbkj.common.utils.*;
import com.zbkj.common.vo.*;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 订单支付回调 CallbackService 实现类
 */
@Service
@Slf4j
public class CallbackServiceImpl implements CallbackService {

    private static final Logger logger = LoggerFactory.getLogger(CallbackServiceImpl.class);

    @Autowired
    private RechargePayService rechargePayService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private StoreCombinationService storeCombinationService;

    @Autowired
    private StorePinkService storePinkService;

    @Autowired
    private WechatPayInfoService wechatPayInfoService;

    @Autowired
    private UserExtractService userExtractService;

    /**
     * 微信支付回调
     */
    @Override
    public String weChat(String xmlInfo) {
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        if (StrUtil.isBlank(xmlInfo)) {
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[xmlInfo is blank]]></return_msg>");
            sb.append("</xml>");
            logger.error("wechat callback error : " + sb.toString());
            return sb.toString();
        }

        try {
            HashMap<String, Object> map = WxPayUtil.processResponseXml(xmlInfo);
            // 通信是否成功
            String returnCode = (String) map.get("return_code");
            if (!returnCode.equals(Constants.SUCCESS)) {
                sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                sb.append("</xml>");
                logger.error("wechat callback error : wx pay return code is fail returnMsg : " + map.get("return_msg"));
                return sb.toString();
            }
            // 交易是否成功
            String resultCode = (String) map.get("result_code");
            if (!resultCode.equals(Constants.SUCCESS)) {
                sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                sb.append("</xml>");
                logger.error("wechat callback error : wx pay result code is fail");
                return sb.toString();
            }

            //解析xml
            CallbackVo callbackVo = CrmebUtil.mapToObj(map, CallbackVo.class);
            AttachVo attachVo = JSONObject.toJavaObject(JSONObject.parseObject(callbackVo.getAttach()), AttachVo.class);

            //判断openid
            User user = userService.getById(attachVo.getUserId());
            if (ObjectUtil.isNull(user)) {
                //用户信息错误
                throw new CrmebException("用户信息错误！");
            }

            //根据类型判断是订单或者充值
            if (!Constants.SERVICE_PAY_TYPE_ORDER.equals(attachVo.getType()) && !Constants.SERVICE_PAY_TYPE_RECHARGE.equals(attachVo.getType())) {
                logger.error("wechat pay err : 未知的支付类型==》" + callbackVo.getOutTradeNo());
                throw new CrmebException("未知的支付类型！");
            }
            // 订单
            if (Constants.SERVICE_PAY_TYPE_ORDER.equals(attachVo.getType())) {
                StoreOrder orderParam = new StoreOrder();
                orderParam.setOutTradeNo(callbackVo.getOutTradeNo());
                orderParam.setUid(attachVo.getUserId());

                StoreOrder storeOrder = storeOrderService.getInfoByEntity(orderParam);
                if (ObjectUtil.isNull(storeOrder)) {
                    logger.error("wechat pay error : 订单信息不存在==》" + callbackVo.getOutTradeNo());
                    throw new CrmebException("wechat pay error : 订单信息不存在==》" + callbackVo.getOutTradeNo());
                }
                if (storeOrder.getPaid()) {
                    logger.error("wechat pay error : 订单已处理==》" + callbackVo.getOutTradeNo());
                    sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                    sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                    sb.append("</xml>");
                    return sb.toString();
                }
                WechatPayInfo wechatPayInfo = wechatPayInfoService.getByNo(storeOrder.getOutTradeNo());
                if (ObjectUtil.isNull(wechatPayInfo)) {
                    logger.error("wechat pay error : 微信订单信息不存在==》" + callbackVo.getOutTradeNo());
                    throw new CrmebException("wechat pay error : 微信订单信息不存在==》" + callbackVo.getOutTradeNo());
                }
                wechatPayInfo.setIsSubscribe(callbackVo.getIsSubscribe());
                wechatPayInfo.setBankType(callbackVo.getBankType());
                wechatPayInfo.setCashFee(callbackVo.getCashFee());
                wechatPayInfo.setCouponFee(callbackVo.getCouponFee());
                wechatPayInfo.setTransactionId(callbackVo.getTransactionId());
                wechatPayInfo.setTimeEnd(callbackVo.getTimeEnd());

                // 添加支付成功redis队列
                Boolean execute = transactionTemplate.execute(e -> {
                    storeOrder.setPaid(true);
                    storeOrder.setPayTime(DateUtil.nowDateTime());
                    storeOrderService.updateById(storeOrder);
                    if (storeOrder.getUseIntegral() > 0) {
                        userService.updateIntegral(user, storeOrder.getUseIntegral(), "sub");
                    }
                    wechatPayInfoService.updateById(wechatPayInfo);

                    // 处理拼团
                    if (storeOrder.getCombinationId() > 0) {
                        // 判断拼团团长是否存在
                        StorePink headPink = new StorePink();
                        Integer pinkId = storeOrder.getPinkId();
                        if (pinkId > 0) {
                            headPink = storePinkService.getById(pinkId);
                            if (ObjectUtil.isNull(headPink) || headPink.getIsRefund().equals(true) || headPink.getStatus() == 3) {
                                pinkId = 0;
                            }
                        }
                        StoreCombination storeCombination = storeCombinationService.getById(storeOrder.getCombinationId());
                        // 如果拼团人数已满，重新开团
                        if (pinkId > 0) {
                            Integer count = storePinkService.getCountByKid(pinkId);
                            if (count >= storeCombination.getPeople()) {
                                pinkId = 0;
                            }
                        }
                        // 生成拼团表数据
                        StorePink storePink = new StorePink();
                        storePink.setUid(user.getUid());
                        storePink.setAvatar(user.getAvatar());
                        storePink.setNickname(user.getNickname());
                        storePink.setOrderId(storeOrder.getOrderId());
                        storePink.setOrderIdKey(storeOrder.getId());
                        storePink.setTotalNum(storeOrder.getTotalNum());
                        storePink.setTotalPrice(storeOrder.getTotalPrice());
                        storePink.setCid(storeCombination.getId());
                        storePink.setPid(storeCombination.getProductId());
                        storePink.setPeople(storeCombination.getPeople());
                        storePink.setPrice(storeCombination.getPrice());
                        Integer effectiveTime = storeCombination.getEffectiveTime();// 有效小时数
                        DateTime dateTime = cn.hutool.core.date.DateUtil.date();
                        storePink.setAddTime(dateTime.getTime());
                        if (pinkId > 0) {
                            storePink.setStopTime(headPink.getStopTime());
                        } else {
                            DateTime hourTime = cn.hutool.core.date.DateUtil.offsetHour(dateTime, effectiveTime);
                            long stopTime = hourTime.getTime();
                            if (stopTime > storeCombination.getStopTime()) {
                                stopTime = storeCombination.getStopTime();
                            }
                            storePink.setStopTime(stopTime);
                        }
                        storePink.setKId(pinkId);
                        storePink.setIsTpl(false);
                        storePink.setIsRefund(false);
                        storePink.setStatus(1);
                        storePinkService.save(storePink);
                        // 如果是开团，需要更新订单数据
                        storeOrder.setPinkId(storePink.getId());
                        storeOrderService.updateById(storeOrder);
                    }

                    return Boolean.TRUE;
                });
                if (!execute) {
                    logger.error("wechat pay error : 订单更新失败==》" + callbackVo.getOutTradeNo());
                    sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                    sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                    sb.append("</xml>");
                    return sb.toString();
                }
                redisUtil.lPush(TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER, storeOrder.getOrderId());
            }
            // 充值
            if (Constants.SERVICE_PAY_TYPE_RECHARGE.equals(attachVo.getType())) {
                UserRecharge userRecharge = new UserRecharge();
                userRecharge.setOrderId(callbackVo.getOutTradeNo());
                userRecharge.setUid(attachVo.getUserId());
                userRecharge = userRechargeService.getInfoByEntity(userRecharge);
                if (ObjectUtil.isNull(userRecharge)) {
                    throw new CrmebException("没有找到订单信息");
                }
                if (userRecharge.getPaid() == 1) {
                    sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                    sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                    sb.append("</xml>");
                    return sb.toString();
                }
                // 支付成功处理
                Boolean rechargePayAfter = rechargePayService.paySuccess(userRecharge);
                if (!rechargePayAfter) {
                    logger.error("wechat pay error : 数据保存失败==》" + callbackVo.getOutTradeNo());
                    throw new CrmebException("wechat pay error : 数据保存失败==》" + callbackVo.getOutTradeNo());
                }
            }
            sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
            sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        } catch (Exception e) {
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[").append(e.getMessage()).append("]]></return_msg>");
            logger.error("wechat pay error : 业务异常==》" + e.getMessage());
        }
        sb.append("</xml>");
        logger.error("wechat callback response : " + sb.toString());
        return sb.toString();
    }

    /**
     * 微信退款回调
     *
     * @param xmlInfo 微信回调json
     * @return MyRecord
     */
    @Override
    public String weChatRefund(String xmlInfo) {
        MyRecord notifyRecord = new MyRecord();
        MyRecord refundRecord = refundNotify(xmlInfo, notifyRecord);
        if ("fail".equals(refundRecord.getStr("status"))) {
            logger.error("微信退款回调失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }

        if (!refundRecord.getBoolean("isRefund")) {
            logger.error("微信退款回调失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }
        String outRefundNo = notifyRecord.getStr("out_refund_no");
        StoreOrder storeOrder = storeOrderService.getByOderId(outRefundNo);
        if (ObjectUtil.isNull(storeOrder)) {
            logger.error("微信退款订单查询失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }
        if (storeOrder.getRefundStatus() == 2) {
            logger.warn("微信退款订单已确认成功==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }
        storeOrder.setRefundStatus(2);
        boolean update = storeOrderService.updateById(storeOrder);
        if (update) {
            // 退款task
            redisUtil.lPush(Constants.ORDER_TASK_REDIS_KEY_AFTER_REFUND_BY_USER, storeOrder.getId());
        } else {
            logger.warn("微信退款订单更新失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
        }
        return refundRecord.getStr("returnXml");
    }

    @Override
    public Boolean apiPay(AiPayPaymentCallback callbackVo) {
        try {
            logger.error("aipay error : 支付回调日志==》" + JSONObject.toJSONString(callbackVo));
            //只处理成功的订单
            if (!"COMPLETED".equals(callbackVo.getStatus())) {
                return false;
            }
            StringBuilder stringBuilder = new StringBuilder();
            String aiMerchantId = systemConfigService.getValueByKey("aiMerchantId");
            String aiApiKey = systemConfigService.getValueByKey("aiApiKey");
            stringBuilder.append("merchantId=" + aiMerchantId);
            stringBuilder.append("&orderNo=" + callbackVo.getOrderNo());
            stringBuilder.append("&outOrderNo=" + callbackVo.getOutOrderNo());
            stringBuilder.append("&apiKey=" + aiApiKey);
            String localSign = DigestUtils.md5Hex(stringBuilder.toString());
            if (!localSign.equals(callbackVo.getSign())) {
                logger.error("aipay error : 签证认证失败==》{0}" + callbackVo.getOutOrderNo(), JSONObject.toJSONString(callbackVo));
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callbackVo.getOutOrderNo()) || paymentOrder(callbackVo.getOutOrderNo());
        } catch (Exception e) {
            logger.error("aipay pay error : 发生异常==》orderNo:{}", callbackVo.getOutOrderNo(), e);
        }
        return false;
    }

    @Override
    public Boolean mPay(MPayPaymentCallback callbackVo, HttpServletRequest httpServletRequest) {
        try {
            // 记录支付回调接收到的数据
            logger.info("mPay: 支付回调数据 - {}", JSONObject.toJSONString(callbackVo));

            // 获取客户端 IP
            String ip = CrmebUtil.getClientIp(httpServletRequest);
            String mpRequestIP = systemConfigService.getValueByKey("mpRequestIP");

            // IP 白名单检查
            if (StringUtils.isNotEmpty(mpRequestIP) &&
                    !Arrays.asList(mpRequestIP.split(",")).contains(ip.trim())) {
                logger.error("mPay: 未知 IP 请求被拒绝 - IP: {}, 回调数据: {}", ip, JSONObject.toJSONString(callbackVo));
                return false;
            }

            // 检查订单状态，仅处理成功订单
            if ("false".equals(callbackVo.getStatus())) {
                logger.error("mPay: 订单状态为失败，跳过处理 - 订单号: {}", callbackVo.getMerchOrderId());
                return false;
            }

            // 构建签名数据
            Map<String, Object> formInfo = new TreeMap<>();
            formInfo.put("merchOrderId", callbackVo.getMerchOrderId());
            formInfo.put("status", callbackVo.getStatus());
            formInfo.put("amount", callbackVo.getAmount());

            // 生成签名字符串
            String mpMd5Key = systemConfigService.getValueByKey("mpMd5Key");
            String signString = formInfo.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&")) + mpMd5Key;

            // 计算 MD5 签名
            String md5Hex = DigestUtils.md5Hex(signString);
            if (!md5Hex.equals(callbackVo.getSign())) {
                logger.error("mPay: 签名验证失败 - 订单号: {}, 回调数据: {}",
                        callbackVo.getMerchOrderId(), JSONObject.toJSONString(callbackVo));
                return false;
            }

            // 处理充值订单或支付订单
            boolean success = rechargeOrder(callbackVo.getMerchOrderId()) ||
                    paymentOrder(callbackVo.getMerchOrderId());
            if (!success) {
                logger.error("mPay: 订单处理失败 - 订单号: {}", callbackVo.getMerchOrderId());
                return false;
            }

            logger.info("mPay: 订单处理成功 - 订单号: {}", callbackVo.getMerchOrderId());
            return true;
        } catch (Exception e) {
            logger.error("mPay: 发生异常 - 订单号: {}, 错误: {}", callbackVo.getMerchOrderId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean fPay(FPayPaymentCallback callbackVo, HttpServletRequest httpServletRequest) {
        try {
            logger.error("fPay error : 支付回调日志==》" + JSONObject.toJSONString(callbackVo));
            String ip = CrmebUtil.getClientIp(httpServletRequest);
            String mpRequestIP = systemConfigService.getValueByKey("fpRequestIP");
            //白名单IP检测
            if (StringUtils.isNotEmpty(mpRequestIP) && !Arrays.stream(mpRequestIP.split(",")).anyMatch(allowedIP -> allowedIP.contains(ip))) {
                logger.error("fpay error : 未知的IP请求被拒绝==》" + ip + "=" + JSONObject.toJSONString(callbackVo));
                return false;
            }
            logger.error("fpay error : 支付回调日志==》" + JSONObject.toJSONString(callbackVo));
            //只处理成功的订单
            if (callbackVo.getState() != 2) {
                return false;
            }

            Map<String, Object> map = new HashMap<>();
            map.put("merchantid", callbackVo.getMerchantid());
            map.put("id", callbackVo.getId());
            map.put("orderid", callbackVo.getOrderid());
            map.put("amount", callbackVo.getAmount());
            map.put("state", callbackVo.getState());
            map.put("charge", callbackVo.getCharge());
            map.put("addtime", callbackVo.getAddtime());
            map.put("endtime", callbackVo.getEndtime());

            String fpApiKey = systemConfigService.getValueByKey("fpApiKey");

            String string = CommonUtil.generateSignature(map) + fpApiKey;
            String sign = DigestUtils.md5Hex(string).toUpperCase();
            if (!sign.equals(callbackVo.getSign())) {
                logger.error("fpay error : 签证认证失败==》{0}" + callbackVo.getMerchantid(), JSONObject.toJSONString(callbackVo));
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callbackVo.getOrderid()) || paymentOrder(callbackVo.getOrderid());
        } catch (Exception e) {
            logger.error("fpay pay error : 发生异常==》orderNo:{}", callbackVo.getMerchantid(), e);
        }
        return false;
    }

    @Override
    public Boolean fPayAgent(FPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.error("fPayAgent error : 代付回调日志==》" + JSONObject.toJSONString(callback));
            String ip = CrmebUtil.getClientIp(httpServletRequest);
            String mpRequestIP = systemConfigService.getValueByKey("fpRequestIP");
            //白名单IP检测
            if (StringUtils.isNotEmpty(mpRequestIP) && !Arrays.stream(mpRequestIP.split(",")).anyMatch(allowedIP -> allowedIP.contains(ip))) {
                logger.error("fPayAgent error : 未知的IP请求被拒绝==》" + ip + "=" + JSONObject.toJSONString(callback));
                return false;
            }
            logger.error("fPayAgent error : 代付回调日志==》" + JSONObject.toJSONString(callback));

            Map<String, Object> map = new HashMap<>();
            map.put("merchantid", callback.getMerchantid());
            map.put("id", callback.getId());
            map.put("orderid", callback.getOrderid());
            map.put("amount", callback.getAmount());
            map.put("address", callback.getAddress());
            map.put("state", callback.getState());
            map.put("charge", callback.getCharge());
            map.put("addtime", callback.getAddtime());
            map.put("endtime", callback.getEndtime());
            map.put("notify_url", callback.getNotify_url());

            String fpApiKey = systemConfigService.getValueByKey("fpApiKey");

            String string = CommonUtil.generateSignature(map) + fpApiKey;
            String sign = DigestUtils.md5Hex(string).toUpperCase();
            if (!sign.equals(callback.getSign())) {
                logger.error("fPayAgent error : 签证认证失败==》" + sign);
                return false;
            }

            //成功的2  人工审核成功 3  人工审核失败 4
            UserExtract userExtract = userExtractService.getByOrderId(callback.getOrderid());
            if (callback.getState() == 4) {
                userExtract.setStatus(4);
                userExtract.setMark("人工审核失败");
                userExtractService.updateById(userExtract);
                return true;
            }
            return withdrawalOrder(userExtract,callback);

        } catch (Exception e) {
            logger.error("fPayAgent error : 发生异常==》orderNo:{}", callback.getMerchantid(), e);
        }
        return false;
    }

    @Override
    public Boolean wanbPay(WanbPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.error("wanbPay error : 支付回调日志==》" + JSONObject.toJSONString(callback));
            String ip = CrmebUtil.getClientIp(httpServletRequest);
            String mpRequestIP = systemConfigService.getValueByKey("wbRequestIP");
            //白名单IP检测
            if (StringUtils.isNotEmpty(mpRequestIP) && !Arrays.stream(mpRequestIP.split(",")).anyMatch(allowedIP -> allowedIP.contains(ip))) {
                logger.error("wanbpay error : 未知的IP请求被拒绝==》" + ip + "=" + JSONObject.toJSONString(callback));
                return false;
            }
            logger.error("wanbpay error : 支付回调日志==》" + JSONObject.toJSONString(callback));
            //只处理成功的订单
            if (callback.getState() != 4) {
                return false;
            }
            String ApiKey = systemConfigService.getValueByKey("wbApiKey");
            String MerchantId = systemConfigService.getValueByKey("wbMerchantId");
            String sign = DigestUtils.md5Hex(MerchantId + callback.getAmount() + callback.getOrderid() + callback.getNotifyurl() + ApiKey);
            if (!sign.equals(callback.getSign())) {
                logger.error("wanbpay error : 签证认证失败==》{0}" + callback.getOrderid(), JSONObject.toJSONString(callback));
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callback.getOrderid()) || paymentOrder(callback.getOrderid());
        } catch (Exception e) {
            logger.error("wanbpay pay error : 发生异常==》orderNo:{}", callback.getOrderid(), e);
        }
        return false;
    }

    @Override
    public Boolean aaPay(AAPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        logger.info("aapay支付回调日志，callback={}", JSON.toJSONString(callback));
        String key = systemConfigService.getValueByKey("aaKey");
        AAPayPaymentCallback.Data callbackData = callback.getData();
        String jsonString = JSON.toJSONString(callbackData);
        String hmacSHA256Signature2 = CrmebUtil.generateHmacSHA256Signature2(jsonString, key);
        hmacSHA256Signature2 = hmacSHA256Signature2.replace('/', '_').replace('+','-');

        if (!Objects.equals(callback.getSign(), hmacSHA256Signature2)) {
            logger.error("aapay签证认证失败，callbackData={}，预期签名={}，实际签名={}",
                    JSON.toJSONString(callbackData),
                    hmacSHA256Signature2,
                    callback.getSign());
            return false;
        }
        if (callbackData.getOrder_status() != 2) {
            logger.error("aapay订单状态不正确，order_status={}，预期值为2", callbackData.getOrder_status());
            return false;
        }
        if (!callbackData.getUser_order_id().contains(Constants.SERVICE_PAY_TYPE_RECHARGE)) {
            logger.error("aapay订单类型不是充值类型，user_order_id={}", callbackData.getUser_order_id());
            return false;
        }
        UserRecharge userRecharge = userRechargeService.getInfoByOrderId(callbackData.getUser_order_id());
        if (userRecharge == null || userRecharge.getPaid() == 1) {
            logger.error("aapay充值记录不存在或已支付，user_order_id={}，userRecharge={}",
                    callbackData.getUser_order_id(),
                    JSON.toJSONString(userRecharge));
            return false;
        }
        BigDecimal callbackPrice = new BigDecimal(callbackData.getCurrency_receipt_money()).setScale(2, RoundingMode.HALF_UP);
        userRecharge.setPrice(callbackPrice);
        userRechargeService.updateById(userRecharge);
        //调整成按三方回调金额修改订单金额入帐。
        log.info("aapay已更新为按三方回调金额修改订单金额入帐==》订单号={}，原充值金额={}，三方回调金额={}，转换后金额={}",
                callbackData.getUser_order_id(),
                userRecharge.getPrice(),
                callbackData.getCurrency_receipt_money(),
                callbackPrice);
        //充值订单 or 支付订单 处理
        return rechargeOrder(callbackData.getUser_order_id()) || paymentOrder(callbackData.getUser_order_id());
    }

    @Override
    public Boolean cbPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("cbpay，支付回调日志={}" , JSONObject.toJSONString(callback));
            //只处理成功的订单
            if (!Objects.equals(callback.getStatus(), "3")) {
                return false;
            }
            String key = systemConfigService.getValueByKey("cbKey");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(callback.getOrderCode() + "&")
                    .append(callback.getAmount() + "&")
                    .append(callback.getUserCode() + "&")
                    .append(callback.getStatus() + "&")
                    .append(key);
            String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();
            if (!Objects.equals(sign, callback.getSign())) {
                logger.error("cbpay，验签失败，callback={}", JSONObject.toJSONString(callback));
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callback.getOrderCode()) || paymentOrder(callback.getOrderCode());
        } catch (Exception e) {
            logger.error("cbpay，发生异常，callback={}", JSONObject.toJSONString(callback), e);
        }
        return false;
    }

    @Override
    public Boolean kdPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("kdpay，支付回调日志={}" , JSONObject.toJSONString(callback));
            //只处理成功的订单
            if (!Objects.equals(callback.getStatus(), "3")) {
                return false;
            }
            String key = systemConfigService.getValueByKey("kdKey");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(callback.getOrderCode() + "&")
                    .append(callback.getAmount() + "&")
                    .append(callback.getUserCode() + "&")
                    .append(callback.getStatus() + "&")
                    .append(key);
            String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();
            if (!Objects.equals(sign, callback.getSign())) {
                logger.error("kdpay，验签失败，callback={}", JSONObject.toJSONString(callback));
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callback.getOrderCode()) || paymentOrder(callback.getOrderCode());
        } catch (Exception e) {
            logger.error("kdpay，发生异常，callback={}", JSONObject.toJSONString(callback), e);
        }
        return false;
    }

    @Override
    public Boolean kdAgentPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("kd代付，回调日志={}", JSONObject.toJSONString(callback));
            String key = systemConfigService.getValueByKey("kdKey");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(callback.getOrderCode() + "&")
                    .append(callback.getCustomerOrderCode() + "&")
                    .append(callback.getAmount() + "&")
                    .append(callback.getUserCode() + "&")
                    .append(callback.getStatus() + "&")
                    .append(key);
            String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();
            if (!Objects.equals(sign, callback.getSign())) {
                logger.error("kd代付，验签失败，callback={}", JSONObject.toJSONString(callback));
                return false;
            }
            UserExtract userExtract = userExtractService.getByOrderId(callback.getCustomerOrderCode());
            //只处理成功的订单
            if (!Objects.equals(callback.getStatus(), "2")) {
                logger.error("kd代付，回调状态錯誤，callback={}", JSONObject.toJSONString(callback));
                return false;
            }

            return withdrawalOrder(userExtract,callback);
        } catch (Exception e) {
            logger.error("kd代付，发生异常，callback={}", JSONObject.toJSONString(callback), e);
        }
        return false;
    }

    @Override
    public Boolean cbAgentPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("cb代付，回调日志={}", JSONObject.toJSONString(callback));
            String key = systemConfigService.getValueByKey("cbKey");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(callback.getOrderCode() + "&")
                    .append(callback.getCustomerOrderCode() + "&")
                    .append(callback.getAmount() + "&")
                    .append(callback.getUserCode() + "&")
                    .append(callback.getStatus() + "&")
                    .append(key);
            String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();
            if (!Objects.equals(sign, callback.getSign())) {
                logger.error("cb代付，验签失败，callback={}", JSONObject.toJSONString(callback));
                return false;
            }
            UserExtract userExtract = userExtractService.getByOrderId(callback.getCustomerOrderCode());
            //只处理成功的订单
            if (!Objects.equals(callback.getStatus(), "2")) {
                logger.error("cb代付，回调状态錯誤，callback={}", JSONObject.toJSONString(callback));
                return false;
            }

            return withdrawalOrder(userExtract,callback);
        } catch (Exception e) {
            logger.error("cb代付，发生异常，callback={}", JSONObject.toJSONString(callback), e);
        }
        return false;
    }

    @Override
    public Boolean bqAgentPay(BQPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("bq 代付回调日志==》" + JSONObject.toJSONString(callback));
            UserExtract userExtract = userExtractService.getByOrderId(callback.getOut_trade_no());
            if (userExtract == null) {
                return false;
            }
            String key = systemConfigService.getValueByKey("bqKey");
            Map<String, Object> orderData = new HashMap<>();
            orderData.put("mchid", callback.getMchid());
            orderData.put("out_trade_no", callback.getOut_trade_no());
            orderData.put("amount", callback.getAmount());
            orderData.put("transaction_id", callback.getTransaction_id());
            orderData.put("refCode", callback.getRefCode());
            orderData.put("refMsg", callback.getRefMsg());

            String stringJoin = CommonUtil.generateSignature(orderData) + "&key=" + key;
            String sign = DigestUtils.md5Hex(stringJoin.toString()).toUpperCase();
            if (!sign.equals(callback.getSign())) {
                logger.error("bq 代付签证认证失败:{0}" + callback.getOut_trade_no(), JSONObject.toJSONString(callback));
                return false;
            }
            if ("4".equals(callback.getRefCode()) || "5".equals(callback.getRefCode())) {
                userExtract.setStatus(4);
                userExtract.setMark("代付失败");
                userExtractService.updateById(userExtract);
                return true;
            } else if ("3".equals(callback.getRefCode())) {
                return withdrawalOrder(userExtract,callback);
            }
        } catch (Exception e) {
            logger.error("BQ代付发生异常==》orderNo:{}", callback.getOut_trade_no(), e);
        }
        return false;
    }

    public static void main(String[] args) {
        String json = "appId=20511521&appOrderNo=bd0b58d6898cf18ef7dfed5640946df8&orderAmt=100.00&orderFee=3.10&orderNo=ZUAN191354527005882851&orderStatus=02&orderTime=20240819135452&key=9B8BCB1CC65C6B599A9021991235BC0D";
        String sign = DigestUtils.md5Hex(json.toString()).toUpperCase();
        System.out.println(sign);
    }

    @Override
    public Boolean dmAgentPay(DMPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("钻石代付回调日志==》" + JSONObject.toJSONString(callback));
            UserExtract userExtract = userExtractService.getByOrderId(callback.getAppOrderNo());
            if (userExtract == null) {
                return false;
            }
            String orderAmt = String.format("%.2f", callback.getOrderAmt());
            String orderFee = String.format("%.2f", callback.getOrderFee());


            String key = systemConfigService.getValueByKey("dmKey");
            Map<String, Object> orderData = new HashMap<>();
            orderData.put("appOrderNo", callback.getAppOrderNo());
            orderData.put("orderNo", callback.getOrderNo());
            orderData.put("orderTime", callback.getOrderTime());
            orderData.put("appId", callback.getAppId());
            orderData.put("orderAmt",orderAmt);
            orderData.put("orderFee", orderFee);
            orderData.put("orderStatus", callback.getOrderStatus());
            String stringJoin = CommonUtil.generateSignature(orderData) + "&key=" + key;

            String sign = DigestUtils.md5Hex(stringJoin.toString()).toUpperCase();
            if (!sign.equals(callback.getSign())) {
                logger.error("钻石代付签证认证失败:{}", JSONObject.toJSONString(callback));
                return false;
            }
            if ("99".equals(callback.getOrderStatus())) {
                userExtract.setStatus(4);
                userExtract.setMark("代付失败");
                userExtractService.updateById(userExtract);
                return true;
            } else if ("02".equals(callback.getOrderStatus())) {
                return withdrawalOrder(userExtract,callback);
            }
        } catch (Exception e) {
            logger.error("钻石代付发生异常==》orderNo:{}", callback.getOrderNo(), e);
        }
        return false;
    }

    @Override
    public Boolean bdtAgentPay(BDTPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("八达通代付回调日志==》" + JSONObject.toJSONString(callback));
            UserExtract userExtract = userExtractService.getByOrderId(callback.getMerchantUniqueOrderId());
            if (userExtract == null) {
                return false;
            }
            String key = systemConfigService.getValueByKey("bdtKey");
            Map<String, Object> orderData = new HashMap<>();
            orderData.put("merchantId", callback.getMerchantId());
            orderData.put("merchantUniqueOrderId", callback.getMerchantUniqueOrderId());
            orderData.put("finishTime", callback.getFinishTime());
            orderData.put("solt", callback.getSolt());
            orderData.put("status", callback.getStatus());
            orderData.put("amount", callback.getAmount());
            String stringJoin = CommonUtil.generateSignature(orderData) + key;
            String sign = DigestUtils.md5Hex(stringJoin.toString()).toLowerCase();
            if (!sign.equals(callback.getSign())) {
                logger.error("八达通代付签证认证失败:{}", JSONObject.toJSONString(callback));
                return false;
            }if ("4".equals(callback.getStatus())) {
                userExtract.setStatus(0);
                userExtract.setMark("代付驳回");
                userExtractService.updateById(userExtract);
                return true;
            }else if ("3".equals(callback.getStatus())) {
                userExtract.setStatus(4);
                userExtract.setMark("代付失败");
                userExtractService.updateById(userExtract);
                return true;
            } else if ("2".equals(callback.getStatus())) {
                return withdrawalOrder(userExtract,callback);
            }
        } catch (Exception e) {
            logger.error("八达通代代付发生异常==》orderNo:{}", callback.getMerchantUniqueOrderId(), e);
        }
        return false;
    }

    public Boolean aaAgentPay(AAAgentPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        logger.info("aapay代付回调日志，callback={}", JSON.toJSONString(callback));
        String key = systemConfigService.getValueByKey("aaKey");
        AAAgentPayPaymentCallback.Data callbackData = callback.getData();
        String jsonString = JSON.toJSONString(callbackData);
        String hmacSHA256Signature2 = CrmebUtil.generateHmacSHA256Signature2(jsonString, key);
        hmacSHA256Signature2 = hmacSHA256Signature2.replace('/', '_').replace('+','-');
        if (!Objects.equals(callback.getSign(), hmacSHA256Signature2)) {
            logger.error("aa代付，验签失败，callback={}，预期签名={}，实际签名={}",
                    JSONObject.toJSONString(callback),
                    hmacSHA256Signature2,
                    callback.getSign());
            return false;
        }
        UserExtract userExtract = userExtractService.getByOrderId(callbackData.getUser_withdrawal_id());
        // 只处理成功的订单
        if (callbackData.getWithdrawal_status() != 3) {
            logger.error("aa代付订单状态不正确，withdrawal_status={}，预期值为3，user_withdrawal_id={}",
                    callbackData.getWithdrawal_status(),
                    callbackData.getUser_withdrawal_id());
            return false;
        }
        return withdrawalOrder(userExtract,callback);
    }

    @Override
    public Boolean xyAgentPay(XYPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("新鹰代付回调日志==》" + JSONObject.toJSONString(callback));
            UserExtract userExtract = userExtractService.getByOrderId(callback.getOut_trade_no());
            if (userExtract == null) {
                return false;
            }
            String key = systemConfigService.getValueByKey("xyKey");
            Map<String, Object> orderData = new HashMap<>();
            orderData.put("mchid", callback.getMchid());
            orderData.put("out_trade_no", callback.getOut_trade_no());
            orderData.put("amount", callback.getAmount());
            orderData.put("successdate", callback.getSuccessdate());
            orderData.put("code", callback.getCode());
            orderData.put("msg", callback.getMsg());

            String stringJoin = CommonUtil.generateSignature(orderData) + "&key="+key;
            String sign = DigestUtils.md5Hex(stringJoin.toString()).toUpperCase();

            if(callback.getCode() ==2 || callback.getCode() == 3 ||  callback.getCode() == 5 || callback.getCode() == 6){
                //again
                return false;
            }
            if (!sign.equals(callback.getSign())) {
                logger.error("新鹰代付签证认证失败:{}", JSONObject.toJSONString(callback));
                return false;
            }
            if (callback.getCode() == 4){
                userExtract.setStatus(4);
                userExtract.setMark("代付驳回");
                userExtractService.updateById(userExtract);
                return true;
            }
            if (callback.getCode() == 1) {
                return withdrawalOrder(userExtract,callback);
            }
        } catch (Exception e) {
            logger.error("新鹰代付发生异常==》orderNo:{}", callback.getOut_trade_no(), e);
        }
        return false;
    }

    @Override
    public Boolean toPay(ToPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("topay支付，订单号={}，callback={}",
                    callback.getOrderid(),
                    JSONObject.toJSONString(callback));

            String key = systemConfigService.getValueByKey("toKey");
            String sign = DigestUtil.md5Hex(String.format("%s%s", callback.getSign(), key));

            if (!Objects.equals(sign, callback.getRetsign())) {
                logger.error("topay签证认证失败==》订单号={}，callback={}",
                        callback.getOrderid(),
                        JSONObject.toJSONString(callback));
                return false;
            }
            //只处理成功的订单
            if (!Objects.equals(callback.getState(), "4")) {
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callback.getOrderid()) || paymentOrder(callback.getOrderid());
        } catch (Exception e) {
            logger.error("topay发生异常==》订单号={}，callback={}",
                    callback.getOrderid(),
                    JSONObject.toJSONString(callback),
                    e);
            return false;
        }
    }

    @Override
    public Boolean okPay(OkPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("okpay支付，订单号={}，callback={}",
                    callback.getOrderid(),
                    JSONObject.toJSONString(callback));

            String key = systemConfigService.getValueByKey("okKey");
            String sign = DigestUtil.md5Hex(String.format("%s%s", callback.getSign(), key));

            if (!Objects.equals(sign, callback.getRetsign())) {
                logger.error("okpay签证认证失败==》订单号={}，callback={}",
                        callback.getOrderid(),
                        JSONObject.toJSONString(callback));
                return false;
            }
            //只处理成功的订单
            if (!Objects.equals(callback.getState(), "4")) {
                return false;
            }
            //充值订单 or 支付订单 处理
            return rechargeOrder(callback.getOrderid()) || paymentOrder(callback.getOrderid());
        } catch (Exception e) {
            logger.error("okpay发生异常==》订单号={}，callback={}",
                    callback.getOrderid(),
                    JSONObject.toJSONString(callback),
                    e);
            return false;
        }
    }

    @Override
    public Boolean toAgentPay(ToAgentCallback callback, HttpServletRequest httpServletRequest) {
            try {
                logger.info("to代付回调，订单号={}，callback={}",
                        callback.getOrderid(),
                        JSONObject.toJSONString(callback));

                String merchantId = systemConfigService.getValueByKey("toMerchantId");
                String key = systemConfigService.getValueByKey("toKey");
                String sign = DigestUtil.md5Hex(String.format("%s%s%s%s", merchantId, callback.getOrderid(), callback.getAmount(), key));

                if (!Objects.equals(sign, callback.getSign())) {
                    logger.error("to代付签证认证失败==》订单号={}，callback={}",
                            callback.getOrderid(),
                            JSONObject.toJSONString(callback));
                    return false;
                }
                UserExtract userExtract = userExtractService.getByOrderId(callback.getOrderid());
                //只处理成功的订单
                if (!Objects.equals(callback.getState(), "4")) {
                    log.error("to代付订单状态失败==》订单号={}，callback={}",
                            callback.getOrderid(),
                            JSONObject.toJSONString(callback));
                    return false;
                }
                return withdrawalOrder(userExtract,callback);
            } catch (Exception e) {
                logger.error("to代付发生异常==》订单号={}，callback={}",
                        callback.getOrderid(),
                        JSONObject.toJSONString(callback),
                        e);
                return false;
            }
    }

    @Override
    public Boolean okAgentPay(OkAgentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("ok代付回调，订单号={}，callback={}",
                    callback.getOrderid(),
                    JSONObject.toJSONString(callback));

            String merchantId = systemConfigService.getValueByKey("okMerchantId");
            String key = systemConfigService.getValueByKey("okKey");
            String sign = DigestUtil.md5Hex(String.format("%s%s%s%s", merchantId, callback.getOrderid(), callback.getAmount(), key));

            if (!Objects.equals(sign, callback.getSign())) {
                logger.error("ok代付签证认证失败==》订单号={}，callback={}",
                        callback.getOrderid(),
                        JSONObject.toJSONString(callback));
                return false;
            }
            UserExtract userExtract = userExtractService.getByOrderId(callback.getOrderid());
            //只处理成功的订单
            if (!Objects.equals(callback.getState(), "4")) {
                log.error("ok代付订单状态失败==》订单号={}，callback={}",
                        callback.getOrderid(),
                        JSONObject.toJSONString(callback));
                return false;
            }
            return withdrawalOrder(userExtract,callback);
        } catch (Exception e) {
            logger.error("ok代付发生异常==》订单号={}，callback={}",
                    callback.getOrderid(),
                    JSONObject.toJSONString(callback),
                    e);
            return false;
        }
    }

    @Override
    public Boolean mAgentPay(MPayPaymentCallback callback, HttpServletRequest httpServletRequest) {
        try {
            logger.info("m代付回调，订单号={}，callback={}",
                    callback.getMerchOrderId(),
                    JSONObject.toJSONString(callback));
            String ip = CrmebUtil.getClientIp(httpServletRequest);
            String mpRequestIP = systemConfigService.getValueByKey("mpRequestIP");
            String mpMd5Key = systemConfigService.getValueByKey("mpMd5Key");
            //白名单IP检测
            if (StringUtils.isNotEmpty(mpRequestIP) && Arrays.stream(mpRequestIP.split(",")).noneMatch(allowedIP -> allowedIP.contains(ip))) {
                log.error("m代付 error : 未知的IP请求被拒绝==》IP={}，callback={}", ip, JSONObject.toJSONString(callback));
                return false;
            }
            // 除sign以外的信息
            Map<String, Object> formInfo = new TreeMap<>();
            formInfo.put("merchOrderId", callback.getMerchOrderId());
            formInfo.put("status", callback.getStatus());
            formInfo.put("amount", callback.getAmount());
            // 将键值对按照键的名称正序排序
            Map<String, Object> sortedInfo = new TreeMap<>(formInfo);
            StringBuilder stringBuilder = new StringBuilder();
            // 排序后的键值对
            for (Map.Entry<String, Object> entry : sortedInfo.entrySet()) {
                stringBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                stringBuilder.append("&");
            }
            // 删除最后一个多余的 "&"
            if (stringBuilder.length() > 0) {
                stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            }
            stringBuilder.append(mpMd5Key);
            String md5Hex = DigestUtils.md5Hex(stringBuilder.toString());
            if (!Objects.equals(md5Hex, callback.getSign())) {
                logger.error("m代付签证认证失败==》订单号={}，callback={}",
                        callback.getMerchOrderId(),
                        JSONObject.toJSONString(callback));
                return false;
            }
            UserExtract userExtract = userExtractService.getByOrderId(callback.getMerchOrderId());
            //只处理成功的订单
            if (!Objects.equals(callback.getStatus(), "finish")) {
                return false;
            }
            return withdrawalOrder(userExtract, callback);
        } catch (Exception e) {
            logger.error("m代付发生异常==》订单号={}，callback={}",
                    callback.getMerchOrderId(),
                    JSONObject.toJSONString(callback),
                    e);
            return false;
        }
    }

    /**
     * 支付订单
     *
     * @param orderNo
     * @return
     */
    private Boolean paymentOrder(String orderNo) {
        if (!orderNo.contains(Constants.SERVICE_PAY_TYPE_ORDER)) {
            return false;
        }
        // 支付订单
        StoreOrder storeOrder = storeOrderService.getByOderId(orderNo);
        if (ObjectUtil.isNull(storeOrder)) {
            logger.error("aipay error : 订单信息不存在==》" + orderNo);
            return false;
        }
        if (storeOrder.getPaid()) {
            logger.error("aipay error : 订单已处理==》" + orderNo);
            return false;
        }

        User user = userService.getById(storeOrder.getUid());
        if (ObjectUtil.isNull(user)) {
            //用户信息错误
            return false;
        }
        // 添加支付成功redis队列
        Boolean execute = transactionTemplate.execute(e -> {
            storeOrder.setPaid(true);
            storeOrder.setPayTime(DateUtil.nowDateTime());
            storeOrderService.updateById(storeOrder);
            if (storeOrder.getUseIntegral() > 0) {
                userService.updateIntegral(user, storeOrder.getUseIntegral(), "sub");
            }
            // 处理拼团
            if (storeOrder.getCombinationId() > 0) {
                // 判断拼团团长是否存在
                StorePink headPink = new StorePink();
                Integer pinkId = storeOrder.getPinkId();
                if (pinkId > 0) {
                    headPink = storePinkService.getById(pinkId);
                    if (ObjectUtil.isNull(headPink) || headPink.getIsRefund().equals(true) || headPink.getStatus() == 3) {
                        pinkId = 0;
                    }
                }
                StoreCombination storeCombination = storeCombinationService.getById(storeOrder.getCombinationId());
                // 如果拼团人数已满，重新开团
                if (pinkId > 0) {
                    Integer count = storePinkService.getCountByKid(pinkId);
                    if (count >= storeCombination.getPeople()) {
                        pinkId = 0;
                    }
                }
                // 生成拼团表数据
                StorePink storePink = new StorePink();
                storePink.setUid(user.getUid());
                storePink.setAvatar(user.getAvatar());
                storePink.setNickname(user.getNickname());
                storePink.setOrderId(storeOrder.getOrderId());
                storePink.setOrderIdKey(storeOrder.getId());
                storePink.setTotalNum(storeOrder.getTotalNum());
                storePink.setTotalPrice(storeOrder.getTotalPrice());
                storePink.setCid(storeCombination.getId());
                storePink.setPid(storeCombination.getProductId());
                storePink.setPeople(storeCombination.getPeople());
                storePink.setPrice(storeCombination.getPrice());
                Integer effectiveTime = storeCombination.getEffectiveTime();// 有效小时数
                DateTime dateTime = cn.hutool.core.date.DateUtil.date();
                storePink.setAddTime(dateTime.getTime());
                if (pinkId > 0) {
                    storePink.setStopTime(headPink.getStopTime());
                } else {
                    DateTime hourTime = cn.hutool.core.date.DateUtil.offsetHour(dateTime, effectiveTime);
                    long stopTime = hourTime.getTime();
                    if (stopTime > storeCombination.getStopTime()) {
                        stopTime = storeCombination.getStopTime();
                    }
                    storePink.setStopTime(stopTime);
                }
                storePink.setKId(pinkId);
                storePink.setIsTpl(false);
                storePink.setIsRefund(false);
                storePink.setStatus(1);
                storePinkService.save(storePink);
                // 如果是开团，需要更新订单数据
                storeOrder.setPinkId(storePink.getId());
                storeOrderService.updateById(storeOrder);
            }

            return Boolean.TRUE;
        });
        if (!execute) {
            logger.error("aipay error : 订单更新失败==》" + orderNo);
            return false;
        }
        redisUtil.lPush(TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER, storeOrder.getOrderId());
        return true;
    }

    /**
     * 充值订单处理
     *
     * @param orderNo 订单号
     * @return 处理是否成功
     */
    private Boolean rechargeOrder(String orderNo) {
        // 检查订单号是否为充值订单
        if (!orderNo.contains(Constants.SERVICE_PAY_TYPE_RECHARGE)) {
            logger.error("rechargeOrder: 非充值订单 - 订单号: {}", orderNo);
            return false;
        }

        // 查询充值订单信息
        UserRecharge userRecharge = userRechargeService.getInfoByOrderId(orderNo);
        if (userRecharge == null) {
            logger.error("rechargeOrder: 充值订单不存在 - 订单号: {}", orderNo);
            return false;
        }
        if (userRecharge.getPaid() == 1) {
            logger.error("rechargeOrder: 订单已支付，跳过处理 - 订单号: {}", orderNo);
            return false;
        }

        // 处理支付成功逻辑
        Boolean rechargePayAfter = rechargePayService.paySuccess(userRecharge);
        if (!rechargePayAfter) {
            logger.error("rechargeOrder: 数据保存失败 - 订单号: {}", orderNo);
            return false;
        }

        // 发送支付成功后的奖励
        rechargePayService.paySuccessSendBonus(userRecharge);
        logger.info("rechargeOrder: 充值订单处理成功 - 订单号: {}", orderNo);
        return true;
    }

    /**
     * 支付订单回调通知
     *
     * @return MyRecord
     */
    private MyRecord refundNotify(String xmlInfo, MyRecord notifyRecord) {
        MyRecord refundRecord = new MyRecord();
        refundRecord.set("status", "fail");
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        if (StrUtil.isBlank(xmlInfo)) {
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[xmlInfo is blank]]></return_msg>");
            sb.append("</xml>");
            logger.error("wechat refund callback error : " + sb.toString());
            return refundRecord.set("returnXml", sb.toString()).set("errMsg", "xmlInfo is blank");
        }

        Map<String, String> respMap;
        try {
            respMap = WxPayUtil.xmlToMap(xmlInfo);
        } catch (Exception e) {
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[").append(e.getMessage()).append("]]></return_msg>");
            sb.append("</xml>");
            logger.error("wechat refund callback error : " + e.getMessage());
            return refundRecord.set("returnXml", sb.toString()).set("errMsg", e.getMessage());
        }

        notifyRecord.setColums(_strMap2ObjMap(respMap));
        // 这里的可以应该根据小程序还是公众号区分
        String return_code = respMap.get("return_code");
        if (return_code.equals(Constants.SUCCESS)) {
            String appid = respMap.get("appid");
            String signKey = getSignKey(appid);
            // 解码加密信息
            String reqInfo = respMap.get("req_info");
            System.out.println("encodeReqInfo==>" + reqInfo);
            try {
                String decodeInfo = decryptToStr(reqInfo, signKey);
                Map<String, String> infoMap = WxPayUtil.xmlToMap(decodeInfo);
                notifyRecord.setColums(_strMap2ObjMap(infoMap));

                String refund_status = infoMap.get("refund_status");
                refundRecord.set("isRefund", refund_status.equals(Constants.SUCCESS));
            } catch (Exception e) {
                refundRecord.set("isRefund", false);
                logger.error("微信退款回调异常，e==》" + e.getMessage());
            }
        } else {
            notifyRecord.set("return_msg", respMap.get("return_msg"));
            refundRecord.set("isRefund", false);
        }
        sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
        sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        sb.append("</xml>");
        return refundRecord.set("returnXml", sb.toString()).set("status", "ok");
    }

    private String getSignKey(String appid) {
        String publicAppid = systemConfigService.getValueByKey(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_ID);
        String miniAppid = systemConfigService.getValueByKey(Constants.CONFIG_KEY_PAY_ROUTINE_APP_ID);
        String appAppid = systemConfigService.getValueByKey(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_APP_ID);
        String signKey = "";
        if (StrUtil.isBlank(publicAppid) && StrUtil.isBlank(miniAppid) && StrUtil.isBlank(appAppid)) {
            throw new CrmebException("pay_weixin_appid或pay_routine_appid不能都为空");
        }
        if (StrUtil.isNotBlank(publicAppid) && appid.equals(publicAppid)) {
            signKey = systemConfigService.getValueByKey(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_KEY);
        }
        if (StrUtil.isNotBlank(miniAppid) && appid.equals(miniAppid)) {
            signKey = systemConfigService.getValueByKey(Constants.CONFIG_KEY_PAY_ROUTINE_APP_KEY);
        }
        if (StrUtil.isNotBlank(appAppid) && appid.equals(appAppid)) {
            signKey = systemConfigService.getValueByKey(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_APP_KEY);
        }
        return signKey;
    }

    /**
     * java自带的是PKCS5Padding填充，不支持PKCS7Padding填充。
     * 通过BouncyCastle组件来让java里面支持PKCS7Padding填充
     * 在加解密之前加上：Security.addProvider(new BouncyCastleProvider())，
     * 并给Cipher.getInstance方法传入参数来指定Java使用这个库里的加/解密算法。
     */
    public static String decryptToStr(String reqInfo, String signKey) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
//        byte[] decodeReqInfo = Base64.decode(reqInfo);
        byte[] decodeReqInfo = base64DecodeJustForWxPay(reqInfo).getBytes(StandardCharsets.ISO_8859_1);
        SecretKeySpec key = new SecretKeySpec(SecureUtil.md5(signKey).toLowerCase().getBytes(), "AES");
        Cipher cipher;
        cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
        cipher.init(Cipher.DECRYPT_MODE, key);
        return new String(cipher.doFinal(decodeReqInfo), StandardCharsets.UTF_8);
    }

    private static final List<String> list = new ArrayList<>();

    static {
        list.add("total_fee");
        list.add("cash_fee");
        list.add("coupon_fee");
        list.add("coupon_count");
        list.add("refund_fee");
        list.add("settlement_refund_fee");
        list.add("settlement_total_fee");
        list.add("cash_refund_fee");
        list.add("coupon_refund_fee");
        list.add("coupon_refund_count");
    }

    private Map<String, Object> _strMap2ObjMap(Map<String, String> params) {
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (list.contains(entry.getKey())) {
                try {
                    map.put(entry.getKey(), Integer.parseInt(entry.getValue()));
                } catch (NumberFormatException e) {
                    map.put(entry.getKey(), 0);
                    logger.error("字段格式错误，key==》" + entry.getKey() + ", value==》" + entry.getValue());
                }
                continue;
            }

            map.put(entry.getKey(), entry.getValue());
        }
        return map;
    }

    /**
     * 仅仅为微信解析密文使用
     *
     * @param source 待解析密文
     * @return 结果
     */
    public static String base64DecodeJustForWxPay(final String source) {
        String result = "";
        final Base64.Decoder decoder = Base64.getDecoder();
        try {
            result = new String(decoder.decode(source), "ISO-8859-1");
        } catch (final UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }

    private <T> Boolean withdrawalOrder(UserExtract userExtract, T callback) {
        if (userExtract == null) {
            logger.error("回调无效的订单==》userExtract={}，callbackObj={}",
                    JSONObject.toJSONString(userExtract),
                    JSONObject.toJSONString(callback));
            return false;
        }
        if (userExtract.getStatus() != 2) {
            logger.error("回调订单状态不正确==》userExtract={}，callbackObj={}",
                    JSONObject.toJSONString(userExtract),
                    JSONObject.toJSONString(callback));
            return false;
        }
        Boolean result = false;
        if (userExtract.getType().equals(1)) {
            result = userExtractService.updateStatus(userExtract, 3, "");
        } else if (userExtract.getType().equals(2)) {
            result = userExtractService.updateStatusAccountBalance(userExtract, 3, "");
        } else if (userExtract.getType().equals(3)) {
            result = userExtractService.updateStatusMerchantBalance(userExtract, 3, "");
        }
        return result;
    }

}
