package com.zbkj.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.request.MerchantExtractRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.dao.MerchantShopDao;
import com.zbkj.service.service.MerchantShopService;
import com.zbkj.service.service.SystemAttachmentService;
import com.zbkj.service.service.SystemConfigService;
import com.zbkj.service.service.UserExtractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SystemCityServiceImpl 接口实现
 */
@Service
public class MerchantShopServiceImpl extends ServiceImpl<MerchantShopDao, MerchantShop> implements MerchantShopService {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private UserExtractService userExtractService;

    @Resource
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public List<MerchantShop> getList(MerchantShop merchantShop, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<MerchantShop> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (!systemAdmin.getIsAdmin()) {
            lambdaQueryWrapper.eq(MerchantShop::getMerchant, systemAdmin.getId());//只能查看自己的店铺(不包括管理员的情况下)
        }
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(merchantShop.getShopName()), MerchantShop::getShopName, merchantShop.getShopName());
        lambdaQueryWrapper.eq(Objects.nonNull(merchantShop.getShopStatus()), MerchantShop::getShopStatus, merchantShop.getShopStatus());
        lambdaQueryWrapper.eq(Objects.nonNull(merchantShop.getCategoryId()), MerchantShop::getCategoryId, merchantShop.getCategoryId());
        lambdaQueryWrapper.orderByDesc(MerchantShop::getId);
        return super.baseMapper.selectList(lambdaQueryWrapper);
    }

    @PostConstruct
    public List<MerchantShop> init() {
        try {
            redisUtil.deleteKeysWithPrefix(Constants.MERCHANT_SHOP);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        LambdaQueryWrapper<MerchantShop> merchantShopLambdaQueryWrapper = new LambdaQueryWrapper<MerchantShop>().eq(MerchantShop::getShopStatus, true).orderByAsc(MerchantShop::getId);
        List<MerchantShop> merchantShopList = super.baseMapper.selectList(merchantShopLambdaQueryWrapper);
        if (merchantShopList != null && merchantShopList.size() > 0) {
            Map<Integer, List<MerchantShop>> groupedByCategoryId = merchantShopList.stream()
                    .collect(Collectors.groupingBy(MerchantShop::getCategoryId));
            groupedByCategoryId.forEach((k, v) -> {
                redisUtil.set(Constants.MERCHANT_SHOP + k, JSON.toJSONString(v));
            });


        }
        return merchantShopList;
    }

    public List<MerchantShop> getList(Integer id) {
        List<MerchantShop> merchantShops = null;
        Object object = redisUtil.get(Constants.MERCHANT_SHOP + id);
        if (object != null) {
            merchantShops = JSON.parseArray(object.toString(), MerchantShop.class);
        } else {
            init();
            object = redisUtil.get(Constants.MERCHANT_SHOP + id);
            if (object != null) {
                merchantShops = JSON.parseArray(object.toString(), MerchantShop.class);
            }
        }
        return merchantShops != null ? merchantShops : List.of();
    }

    @Override
    public boolean isMerchantExists(Integer merchantId) {
        LambdaQueryWrapper<MerchantShop> lambdaQueryWrapper = new LambdaQueryWrapper<MerchantShop>().eq(MerchantShop::getMerchant, merchantId);
        return this.count(lambdaQueryWrapper) > 0;
    }

    @Override
    public MerchantShop getByMerchant(Integer merchantId) {
        LambdaQueryWrapper<MerchantShop> lambdaQueryWrapper = new LambdaQueryWrapper<MerchantShop>().eq(MerchantShop::getMerchant, merchantId);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean withdraw(MerchantExtractRequest request) {
        MerchantShop merchantShop = this.getById(request.getId());
        if (merchantShop == null) {
            throw new CrmebException("无效的商铺ID");
        }
        //如果店铺的额度小于提款的金额
        if (merchantShop.getBalance().compareTo(request.getExtractPrice()) < 0) {
            throw new CrmebException("店铺额度不足，仅可提现:" + merchantShop.getBalance() + "元");
        }
        UserExtract userExtract = userExtractService.getByUidAndType(request.getId(), 3);
        if (userExtract != null && userExtract.getStatus() == 0) {
            throw new CrmebException("已有提现订单处理中！");
        }
        switch (request.getExtractType()) {
            case "weixin":
                if (StringUtils.isBlank(request.getWechat())) {
                    throw new CrmebException("请填写微信号！");
                }
                request.setAlipayCode(null);
                request.setBankCode(null);
                request.setBankName(null);
                break;
            case "alipay":
                if (StringUtils.isBlank(request.getAlipayCode())) {
                    throw new CrmebException("请填写支付宝账号！");
                }
                request.setWechat(null);
                request.setBankCode(null);
                request.setBankName(null);
                break;
            case "bank":
                if (StringUtils.isBlank(request.getBankName())) {
                    throw new CrmebException("请填写银行名称！");
                }
                if (StringUtils.isBlank(request.getBankCode())) {
                    throw new CrmebException("请填写银行卡号！");
                }
                request.setWechat(null);
                request.setAlipayCode(null);
                break;
            case "fpay":
                if (StringUtils.isBlank(request.getRealName())) {
                    throw new CrmebException("请填写地址！");
                }
                break;
        }
        String merchantWithdrawFee = systemConfigService.getValueByKeyException("merchantWithdrawFee");
        BigDecimal fee = BigDecimal.ZERO;
        if (StrUtil.isNotBlank(merchantWithdrawFee) && new BigDecimal(merchantWithdrawFee).compareTo(BigDecimal.ZERO) > 0) {
            fee = request.getExtractPrice().multiply(new BigDecimal(merchantWithdrawFee).divide(new BigDecimal(100))).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        BigDecimal balance = merchantShop.getBalance().subtract(request.getExtractPrice());
        merchantShop.setBalance(balance);
        this.updateById(merchantShop);
        UserExtract extract = new UserExtract();
        BeanUtils.copyProperties(request, extract);
        extract.setUid(request.getId());
        extract.setBalance(balance);
        extract.setExtractPrice(request.getExtractPrice().subtract(fee));
        extract.setMark(SecurityUtil.getLoginUserVo().getUser().getAccount() + "申请");
        extract.setFee(fee);
        //存入银行名称
        if (StrUtil.isNotBlank(extract.getQrcodeUrl())) {
            extract.setQrcodeUrl(systemAttachmentService.clearPrefix(extract.getQrcodeUrl()));
        }
        return userExtractService.save(extract);
    }

    @Override
    public Map<Integer, MerchantShop> getListById(List<Integer> ids) {
        LambdaQueryWrapper<MerchantShop> lambdaQueryWrapper = new LambdaQueryWrapper<MerchantShop>().in(MerchantShop::getId, ids);
        List<MerchantShop> list = this.list(lambdaQueryWrapper);
        return list.stream().collect(Collectors.toMap(MerchantShop::getId, merchantShop -> merchantShop));
    }
}

