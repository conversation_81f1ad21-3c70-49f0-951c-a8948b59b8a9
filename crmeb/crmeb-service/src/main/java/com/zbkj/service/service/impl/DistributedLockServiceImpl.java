package com.zbkj.service.service.impl;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.utils.RedisCache;
import com.zbkj.service.service.DistributedLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁服务实现类
 * 统一管理各种分布式锁操作
 * 
 * 设计说明：
 * - 提供通用的分布式锁服务
 * - 支持自定义锁键，灵活性更高
 * - 同时提供用户锁的便捷方法
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DistributedLockServiceImpl implements DistributedLockService {

    @Autowired
    private RedisCache redisCache;

    /**
     * 用户锁的键前缀 - 统一格式，与项目中其他地方保持一致
     * 所有用户相关操作都使用同一把锁，确保数据一致性
     */
    private static final String USER_LOCK_PREFIX = "user_lock_";

    /**
     * 默认锁超时时间（秒）
     */
    private static final long DEFAULT_LOCK_TIMEOUT = 30L;

    /**
     * 获取锁的最大等待时间（毫秒）
     */
    private static final long MAX_WAIT_TIME = 5000L;

    @Override
    public <T> T executeWithLock(String lockKey, Supplier<T> operation) {
        if (lockKey == null || lockKey.trim().isEmpty()) {
            throw new CrmebException("锁键不能为空");
        }

        String requestId = generateRequestId();

        log.debug("尝试获取锁，lockKey: {}, requestId: {}", lockKey, requestId);

        // 尝试获取锁 - 使用RedisCache的tryLock方法，支持等待时间
        boolean lockAcquired = false;
        try {
            lockAcquired = redisCache.tryLock(lockKey, requestId, MAX_WAIT_TIME, DEFAULT_LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("获取锁失败，lockKey: {}, requestId: {}", lockKey, requestId);
                throw new CrmebException("系统繁忙，请稍后重试");
            }

            log.debug("成功获取锁，lockKey: {}, requestId: {}", lockKey, requestId);

            // 执行业务操作
            return operation.get();

        } catch (Exception e) {
            log.error("执行锁操作失败，lockKey: {}, requestId: {}, error: {}",
                     lockKey, requestId, e.getMessage(), e);
            throw e;
        } finally {
            // 释放锁
            if (lockAcquired) {
                boolean unlocked = redisCache.unlock(lockKey, requestId);
                if (unlocked) {
                    log.debug("成功释放锁，lockKey: {}, requestId: {}", lockKey, requestId);
                } else {
                    log.warn("释放锁失败，lockKey: {}, requestId: {}", lockKey, requestId);
                }
            }
        }
    }

    @Override
    public void executeWithLock(String lockKey, Runnable operation) {
        executeWithLock(lockKey, () -> {
            operation.run();
            return null;
        });
    }

    /**
     * 生成唯一的请求ID
     * 
     * @return 请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString();
    }

}
