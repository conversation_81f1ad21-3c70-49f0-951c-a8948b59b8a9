package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserTaskSchedule;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserTaskScheduleRequest;
import com.zbkj.common.response.UserTaskScheduleResponse;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.service.dao.UserTaskScheduleDao;
import com.zbkj.service.service.UserService;
import com.zbkj.service.service.UserTaskScheduleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 用户任务进度 接口实现类
 */

@Service
public class UserTaskScheduleServiceImpl extends ServiceImpl<UserTaskScheduleDao, UserTaskSchedule> implements UserTaskScheduleService {
    @Autowired
    private UserService userService;

    /**
     * UserTaskSchedule列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserTaskScheduleResponse> getList(UserTaskScheduleRequest request) {
        Page<UserTaskScheduleResponse> startPage = PageHelper.startPage(request.getPage(), request.getLimit());

        // 通过账号获取用户的逻辑
        if (StringUtils.isNotBlank(request.getAccount())) {
            User user = userService.getUserByAccount(request.getAccount());
            if (user != null) {
                request.setUserId(user.getUid());
            }
        }

        // 使用 lambdaQuery 构建查询条件
        LambdaQueryWrapper<UserTaskSchedule> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUserId()), UserTaskSchedule::getUserId, request.getUserId())
                .eq(Objects.nonNull(request.getTaskId()), UserTaskSchedule::getTaskId, request.getTaskId())
                .eq(Objects.nonNull(request.getStatus()), UserTaskSchedule::getStatus, request.getStatus());

        lambdaQueryWrapper.orderByDesc(UserTaskSchedule::getCreateTime);

        List<UserTaskSchedule> userTaskSchedules = baseMapper.selectList(lambdaQueryWrapper);

        if (CollectionUtil.isEmpty(userTaskSchedules)) {
            return CommonPage.copyPageInfo(startPage, Collections.emptyList());
        }

        // 使用流式处理转换为 UserTaskScheduleResponse 列表
        List<UserTaskScheduleResponse> responseList = userTaskSchedules.stream()
                .map(userTaskSchedule -> {
                    UserTaskScheduleResponse userTaskScheduleResponse = new UserTaskScheduleResponse();
                    BeanUtils.copyProperties(userTaskSchedule, userTaskScheduleResponse);

                    // 查询获取 User 对象
                    User user = userService.getById(userTaskSchedule.getUserId());
                    if (user != null) {
                        userTaskScheduleResponse.setAccount(user.getAccount());
                    }

                    return userTaskScheduleResponse;
                })
                .collect(Collectors.toList());

        return CommonPage.copyPageInfo(startPage, responseList);
    }

    @Override
    public List<UserTaskSchedule> getUserTaskScheduleByTaskId(Integer userId, Integer taskId, PageParamRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());
        LambdaQueryWrapper<UserTaskSchedule> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(Objects.nonNull(userId), UserTaskSchedule::getUserId, userId);
        lambdaQueryWrapper.eq(Objects.nonNull(taskId), UserTaskSchedule::getTaskId, taskId);
        lambdaQueryWrapper.orderByDesc(UserTaskSchedule::getCreateTime);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Boolean canSubmitToday(Integer userId, Integer taskId, Integer count) {
        String nowedDate = DateUtil.nowDate(Constants.DATE_FORMAT_DATE);
        LambdaQueryWrapper<UserTaskSchedule> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserTaskSchedule::getUserId, userId);
        lambdaQueryWrapper.eq(UserTaskSchedule::getTaskId, taskId);
        lambdaQueryWrapper.eq(UserTaskSchedule::getCreateDate, nowedDate);
        Integer selectCount = baseMapper.selectCount(lambdaQueryWrapper);
        return count > selectCount;
    }

}
