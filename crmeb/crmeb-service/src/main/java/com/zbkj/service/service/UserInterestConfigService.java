package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.UserInterestConfig;
import com.zbkj.common.request.UserInterestConfigRequest;

/**
 * 用户利息配置表 业务接口
 */
public interface UserInterestConfigService extends IService<UserInterestConfig> {

    /**
     * UserInterestConfig 列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<UserInterestConfig> getList(UserInterestConfigRequest request);

    /**
     * 配置的时间区间利息查询
     * @return
     */
    UserInterestConfig configInterestQuery(Integer userId);
}

