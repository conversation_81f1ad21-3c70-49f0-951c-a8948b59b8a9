package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.user.UserTaskSchedule;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserTaskScheduleRequest;
import com.zbkj.common.response.UserTaskScheduleResponse;

import java.util.List;

/**
 * 用户任务进度 业务接口
 */
public interface UserTaskScheduleService extends IService<UserTaskSchedule> {

    /**
     * UserTaskSchedule 列表查询
     * @param request
     * @return
     */
    PageInfo<UserTaskScheduleResponse> getList(UserTaskScheduleRequest request);

    /**
     * 根據用戶ID和任务ID查询进度
     * @param userId
     * @param taskId
     * @return
     */
    List<UserTaskSchedule> getUserTaskScheduleByTaskId(Integer userId, Integer taskId, PageParamRequest request);


    /**
     * 是否还能提交
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param count 任务次数
     * @return
     */
    Boolean canSubmitToday(Integer userId, Integer taskId, Integer count);
}

