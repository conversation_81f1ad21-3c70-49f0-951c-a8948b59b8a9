package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zbkj.common.enums.PlatformType;
import com.zbkj.common.enums.LoginMethod;

import java.util.Date;

import com.zbkj.common.enums.OperationResult;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.UserLoginLogs;
import com.zbkj.common.model.VersionInfo;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.LoginMobileRequest;
import com.zbkj.common.request.LoginRequest;
import com.zbkj.common.request.UserLoginLogsSearchRequest;
import com.zbkj.common.response.LoginResponse;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.ValidateFormUtil;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.UserLoginLogsDao;
import com.zbkj.service.service.UserLoginLogsService;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户登录日志表 接口实现类
 */

@Service
public class UserLoginLogsServiceImpl extends ServiceImpl<UserLoginLogsDao, UserLoginLogs> implements UserLoginLogsService {

    @Autowired
    private UserService userService;

    /**
     * UserLoginLogs列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserLoginLogs> getList(UserLoginLogsSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserLoginLogs 类的多条件查询
        LambdaQueryWrapper<UserLoginLogs> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(request.getAccount())) {
            User user = userService.getUserByAccount(request.getAccount());
            if (user != null) {
                request.setUserId(user.getUid());
            }
        }
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            DateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(UserLoginLogs::getLoginTime, dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUserId()), UserLoginLogs::getUserId, request.getUserId());
        lambdaQueryWrapper.likeRight(StringUtils.isNotBlank(request.getLoginIp()), UserLoginLogs::getLoginIp, request.getLoginIp());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getLoginStatus()), UserLoginLogs::getLoginStatus, request.getLoginStatus());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getLoginPlatform()), UserLoginLogs::getLoginPlatform, request.getLoginPlatform());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getLoginMethod()), UserLoginLogs::getLoginMethod, request.getLoginMethod());
        lambdaQueryWrapper.orderByDesc(UserLoginLogs::getLoginTime);
        List<UserLoginLogs> userLoginLogs = baseMapper.selectList(lambdaQueryWrapper);
        return CommonPage.copyPageInfo(startPage, userLoginLogs);
    }

    @Override
    public UserLoginLogs getLastLoginInfo(Integer userId) {
        //列表查询 UserLoginLogs 类的多条件查询
        LambdaQueryWrapper<UserLoginLogs> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserLoginLogs::getUserId, userId);
        lambdaQueryWrapper.eq(UserLoginLogs::getLoginStatus, OperationResult.SUCCESS);
        lambdaQueryWrapper.orderByDesc(UserLoginLogs::getId);
        lambdaQueryWrapper.last(" limit 1");
        UserLoginLogs userLoginLogs = baseMapper.selectOne(lambdaQueryWrapper);
        return userLoginLogs;
    }

    @Override
    public Integer getLoginNum(String startTime, String endTime, Integer groupId) {
        QueryWrapper<UserLoginLogs> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge(StringUtils.isNotBlank(startTime), "login_time", startTime);
        queryWrapper.le(StringUtils.isNotBlank(endTime), "login_time", endTime);
        queryWrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        queryWrapper.eq("login_status", OperationResult.SUCCESS);
        queryWrapper.select("COUNT(DISTINCT user_id) as total_count");
        List<Map<String, Object>> resultMapList = baseMapper.selectMaps(queryWrapper);
        // 获取记录数量
        int totalCount = 0;
        if (!resultMapList.isEmpty() && resultMapList.size() > 0) {
            // 获取第一个 Map 对象的 total_count 值
            totalCount = Integer.parseInt(resultMapList.get(0).get("total_count").toString());
        }
        return totalCount;
    }

    @Override
    public Object saveLoginLogs(ProceedingJoinPoint joinPoint,LoginMethod loginMethod) {
        Object[] args = joinPoint.getArgs();
        User user = null;
        String defaultAccount = "";
        if (args[0] instanceof LoginMobileRequest){
            LoginMobileRequest loginMobileRequest = (LoginMobileRequest)args[0];
            defaultAccount = loginMobileRequest.getPhone();
            user = userService.getByPhone(loginMobileRequest.getNumberCode(),loginMobileRequest.getPhone());
        }else if (args[0] instanceof LoginRequest){
            LoginRequest loginRequest =  (LoginRequest)args[0];
            defaultAccount = loginRequest.getAccount();
            boolean isValidPhone = ValidateFormUtil.isPhoneNumber(loginRequest.getAccount());
            if (isValidPhone){
                user = userService.getByEncryptedPhone(loginRequest.getNumberCode(),loginRequest.getAccount());
            }else {
                user = userService.getUserByAccount(loginRequest.getAccount());
            }
        }
        UserLoginLogs userLoginLogs = new UserLoginLogs();
        if (user != null){
            userLoginLogs.setUserId(user.getUid());
            userLoginLogs.setGroupId(user.getGroupId());
            defaultAccount = user.getAccount();
        }
        userLoginLogs.setAccount(defaultAccount);
        HttpServletRequest request = CrmebUtil.getRequest();
        String jsonStr = CrmebUtil.getVersionInfo(request);
        VersionInfo versionInfo;
        if (StringUtils.isNotBlank(jsonStr)){
            versionInfo = JSON.parseObject(jsonStr, VersionInfo.class);
        }else {
            versionInfo = new VersionInfo();
        }
        userLoginLogs.setLoginTime(new Date());
        userLoginLogs.setLoginIp(CrmebUtil.getClientIp(request));
        userLoginLogs.setLoginMethod(loginMethod);
        PlatformType deviceType = CrmebUtil.getDeviceType(request.getHeader("user-agent"));
        if (deviceType == PlatformType.OTHER){
            deviceType = CrmebUtil.getDeviceType(versionInfo.getOsInfo());
        }
        userLoginLogs.setLoginPlatform(deviceType);
        userLoginLogs.setBrowserInfo(versionInfo.getBrowserInfo());
        userLoginLogs.setOsInfo(versionInfo.getOsInfo());
        userLoginLogs.setLoginDuration(0L);
        userLoginLogs.setUserAgent(request.getHeader("user-agent"));

        Object result = null;

        // 执行目标方法
        try {
            result = joinPoint.proceed();
            LoginResponse loginResponse = (LoginResponse)result;
            if (StringUtils.isNotBlank(loginResponse.getToken())){
                userLoginLogs.setLoginStatus(OperationResult.SUCCESS);
                userLoginLogs.setLoginResult("登录成功");
            }
        } catch (Throwable e) {
            userLoginLogs.setLoginStatus(OperationResult.FAIL);
            userLoginLogs.setLoginResult(e.getMessage());
        }
        baseMapper.insert(userLoginLogs);
        if (userLoginLogs.getLoginStatus() == OperationResult.FAIL){
            throw new CrmebException(userLoginLogs.getLoginResult());
        }
        return result;
    }
}
