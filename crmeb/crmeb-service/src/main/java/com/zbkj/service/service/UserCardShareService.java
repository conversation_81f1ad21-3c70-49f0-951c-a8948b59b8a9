package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.user.UserCard;
import com.zbkj.common.model.user.UserCardShare;
import com.zbkj.common.request.UserCardRecordRequest;
import com.zbkj.common.request.UserCardShareRequest;
import com.zbkj.common.response.UserCardRecordResponse;
import com.zbkj.common.response.UserCardShareResponse;

import java.util.HashMap;
import java.util.List;

/**
 * SystemCityService 接口

 */
public interface UserCardShareService extends IService<UserCardShare> {
    /**
     * 分享卡片
     * @param cardId
     * @return
     */
    String share(Integer cardId);

    /**
     * 领取分享的卡片
     * @param code
     * @return
     */
    HashMap<String, Object> receive(String code);

    UserCardShare getUserCardShareByCode(String code);

    PageInfo<UserCardShareResponse> getList(UserCardShareRequest request);
}
