package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.*;
import com.zbkj.common.enums.InterestType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.UserExclusive;
import com.zbkj.common.model.UserInterestConfig;
import com.zbkj.common.model.coupon.StoreCoupon;
import com.zbkj.common.model.coupon.StoreCouponUser;
import com.zbkj.common.model.order.StoreOrder;
import com.zbkj.common.model.record.UserVisitRecord;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.system.SystemUserLevel;
import com.zbkj.common.model.user.*;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.*;
import com.zbkj.common.response.*;
import com.zbkj.common.token.FrontTokenComponent;
import com.zbkj.common.utils.*;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.UserDao;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 用户表 服务实现类
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserDao, User> implements UserService {

    private Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    private final ExecutorService executorService = Executors.newFixedThreadPool(5); // 根据实际需求配置线程池大小


    @Resource
    private UserDao userDao;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FrontTokenComponent tokenComponet;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private UserTagService userTagService;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserSignService userSignService;

    @Autowired
    private StoreCouponUserService storeCouponUserService;

    @Autowired
    private StoreCouponService storeCouponService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserIntegralRecordService userIntegralRecordService;

    @Autowired
    private UserBrokerageRecordService userBrokerageRecordService;

    @Autowired
    private StoreProductRelationService storeProductRelationService;

    @Autowired
    private UserExperienceRecordService experienceRecordService;

    @Autowired
    private UserVisitRecordService userVisitRecordService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private UserBalanceInterestReportService userBalanceInterestReportService;

    @Autowired
    private UserRechargeService userRechargeService;
    @Autowired
    private UserExtractService userExtractService;
    @Autowired
    private UserInterestConfigService userInterestConfigService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private UserExclusiveService userExclusiveService;

    @Autowired
    private DistributedLockService distributedLockService;


    @Override
    public List<User> getUserListById(Collection collection) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(User::getUid, collection);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 分页显示用户表
     *
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     */
    @Override
    public PageInfo<UserResponse> getList(UserSearchRequest request, PageParamRequest pageParamRequest) {
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        Page<User> pageUser = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(systemAdmin.getGroupId())) {
            lambdaQueryWrapper.eq(User::getGroupId, systemAdmin.getGroupId());
        } else {
            if (StringUtils.isNotBlank(request.getGroupId())) {
                if (request.getGroupId().equals("-1")) {
                    lambdaQueryWrapper.isNull(User::getGroupId).or(wrapper -> wrapper.eq(User::getGroupId, ""));
                } else {
                    lambdaQueryWrapper.in(User::getGroupId, request.getGroupId().split(","));
                }
            }
        }
        if (StringUtils.isNotBlank(request.getAccount())) {
            String string = request.getAccount().replaceAll(" ", "");
            String[] accounts = string.split("\n");
            if (accounts.length > 50) {
                throw new CrmebException("批量账号搜索仅支持50个账号");
            }
            lambdaQueryWrapper.in(User::getAccount, accounts);
        }

        if (StringUtils.isNotBlank(request.getKeywords())) {
            lambdaQueryWrapper.eq(User::getAccount, request.getKeywords())
                    .or().eq(User::getPhone, request.getKeywords())
                    .or().eq(User::getNickname, request.getKeywords())
                    .or().eq(User::getUid, request.getKeywords());
        }
        lambdaQueryWrapper.in(StringUtils.isNotEmpty(request.getLevel()), User::getLevel, request.getLevel());
        lambdaQueryWrapper.in(StringUtils.isNotEmpty(request.getLabelId()), User::getTagId, request.getLabelId());
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            DateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(User::getCreateTime, dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        lambdaQueryWrapper.ge(Objects.nonNull(request.getRechargeAmountMin()), User::getRechargeAmount, request.getRechargeAmountMin());
        lambdaQueryWrapper.le(Objects.nonNull(request.getRechargeAmountMax()), User::getRechargeAmount, request.getRechargeAmountMax());
        lambdaQueryWrapper.ge(Objects.nonNull(request.getWithdrawAmountMin()), User::getWithdrawAmount, request.getWithdrawAmountMin());
        lambdaQueryWrapper.le(Objects.nonNull(request.getWithdrawAmountMax()), User::getWithdrawAmount, request.getWithdrawAmountMax());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(request.getSex()), User::getSex, request.getSex());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getIsPromoter()), User::getIsPromoter, request.getIsPromoter());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getAuthenticationStatus()), User::getAuthenticationStatus, request.getAuthenticationStatus());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(request.getInvitationCode()), User::getInvitationCode, request.getInvitationCode());
        //查询下级
        lambdaQueryWrapper.eq(Objects.nonNull(request.getSubUid()), User::getSpreadUid, request.getSubUid());

        if (Objects.nonNull(request.getSort())) {
            switch (request.getSort()) {
                case REGISTER_TIME_ASC:
                    lambdaQueryWrapper.orderByAsc(User::getCreateTime);
                    break;
                case REGISTER_TIME_DESC:
                    lambdaQueryWrapper.orderByDesc(User::getCreateTime);
                    break;
                case BALANCE_ASC:
                    lambdaQueryWrapper.orderByAsc(User::getNowMoney);
                    break;
                case BALANCE_DESC:
                    lambdaQueryWrapper.orderByDesc(User::getNowMoney);
                    break;
                case TOTAL_RECHARGE_ASC:
                    lambdaQueryWrapper.orderByAsc(User::getRechargeAmount);
                    break;
                case TOTAL_RECHARGE_DESC:
                    lambdaQueryWrapper.orderByDesc(User::getRechargeAmount);
                    break;
                case TOTAL_WITHDRAWAL_ASC:
                    lambdaQueryWrapper.orderByAsc(User::getWithdrawAmount);
                    break;
                case TOTAL_WITHDRAWAL_DESC:
                    lambdaQueryWrapper.orderByDesc(User::getWithdrawAmount);
                    break;
                case POINTS_ASC:
                    lambdaQueryWrapper.orderByAsc(User::getIntegral);
                    break;
                case POINTS_DESC:
                    lambdaQueryWrapper.orderByDesc(User::getIntegral);
                    break;
            }
        }


        List<User> users = userDao.selectList(lambdaQueryWrapper);
        List<UserResponse> userResponses = new ArrayList<>();
        users.forEach(user -> {
            UserResponse userResponse = new UserResponse();
            BeanUtils.copyProperties(user, userResponse);
            if (Objects.nonNull(user.getExclusiveId())) {
                UserExclusive userExclusive = userExclusiveService.getInfoById(user.getExclusiveId());
                if (userExclusive != null) {
                    userResponse.setExclusiveName(userExclusive.getName());
                }
            }
            // 获取分组信息
            if (Objects.nonNull(user.getGroupId())) {
                userResponse.setGroupName(userGroupService.getGroupNameInId(user.getGroupId() + ""));
                userResponse.setGroupId(user.getGroupId());
            }

            // 获取标签信息
            if (!StringUtils.isBlank(user.getTagId())) {
                userResponse.setTagName(userTagService.getGroupNameInId(user.getTagId() + ""));
                userResponse.setTagId(user.getTagId());
            }
            //获取推广人信息
            if (null == user.getSpreadUid() || user.getSpreadUid() == 0) {
                userResponse.setSpreadNickname("无");
            } else {
                User superiorUser = userDao.selectById(user.getSpreadUid());
                if (superiorUser != null) {
                    userResponse.setSuperiorUser(superiorUser);
                    userResponse.setSpreadNickname(superiorUser.getNickname());
                }
            }
            //下级数量
            Integer spreadListBySpreadIdOfCount = getSpreadListBySpreadIdOfCount(user.getUid());
            userResponse.setSubCount(spreadListBySpreadIdOfCount);
            if (StringUtils.isBlank(userResponse.getPhone())) {
                userResponse.setNumberCode("");
                userResponse.setPhone("已加密");
            }
            userResponses.add(userResponse);
        });
        return CommonPage.copyPageInfo(pageUser, userResponses);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFrozenBalance(UserOperateIntegralMoneyRequest request) {
        if (request.getMoneyType() == null) {
            throw new CrmebException("请选择有效的类型");
        }
        if (ObjectUtil.isNull(request.getMoneyValue()) || request.getMoneyValue().compareTo(BigDecimal.ZERO) <= 0) {
            throw new CrmebException("请输入有效的金额");
        }
        User user = this.getInfoByUid(request.getUid());
        if (user == null) {
            throw new CrmebException("无效的用户ID");
        }

        // 处理余额
        // 生成UserBill
        UserBill userBill = new UserBill();
        userBill.setUid(user.getUid());
        userBill.setLinkId("0");
        userBill.setTitle("底仓设置");
        userBill.setCategory("frozen_balance");
        userBill.setNumber(request.getMoneyValue());
        userBill.setStatus(1);
        userBill.setCreateTime(DateUtil.nowDateTime());
        userBill.setBalance(user.getNowMoney());

        //1 增加 2减少
        if (request.getMoneyType() == 1) {
            BigDecimal nowMoney = user.getNowMoney();
            if (nowMoney.compareTo(request.getMoneyValue()) < 0) {
                throw new CrmebException("用户余额不足，当前余额：" + nowMoney);
            }
            user.setNowMoney(nowMoney.subtract(request.getMoneyValue()));
            user.setFrozenBalance(user.getFrozenBalance().add(request.getMoneyValue()));
            //日志
            userBill.setPm(1);
            userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_ADD);
            userBill.setMark(StrUtil.format("后台操作冻结额度增加了{}余额", request.getMoneyValue()));
        } else if (request.getMoneyType() == 2) {
            BigDecimal frozenBalance = user.getFrozenBalance();
            if (frozenBalance.compareTo(request.getMoneyValue()) < 0) {
                throw new CrmebException("用户冻结额度不足，当前额度：" + frozenBalance);
            }
            user.setNowMoney(user.getNowMoney().add(request.getMoneyValue()));
            user.setFrozenBalance(user.getFrozenBalance().subtract(request.getMoneyValue()));
            //日志
            userBill.setPm(0);
            userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_SUB);
            userBill.setMark(StrUtil.format("后台操作冻结额度减少了{}余额", request.getMoneyValue()));

        }
        userBillService.save(userBill);
        this.updateById(user);
        return true;
    }

    /**
     * 操作积分、余额
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateIntegralMoney(UserOperateIntegralMoneyRequest request) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + request.getUid(), () -> {
            if (ObjectUtil.isNull(request.getMoneyValue()) || ObjectUtil.isNull(request.getIntegralValue())) {
                throw new CrmebException("至少输入一个金额");
            }
            if (request.getMoneyValue().compareTo(BigDecimal.ZERO) < 1 && request.getIntegralValue() <= 0) {
                throw new CrmebException("修改值不能等小于等于0");
            }

            User user = getById(request.getUid());
            if (ObjectUtil.isNull(user)) {
                throw new CrmebException("用户不存在");
            }
        // 减少时要判断小于0的情况,添加时判断是否超过数据限制
        if (request.getMoneyType().equals(2) && request.getMoneyValue().compareTo(BigDecimal.ZERO) != 0) {
            if (user.getNowMoney().subtract(request.getMoneyValue()).compareTo(BigDecimal.ZERO) < 0) {
                throw new CrmebException("余额扣减后不能小于0");
            }
        }
        if (request.getMoneyType().equals(1) && request.getMoneyValue().compareTo(BigDecimal.ZERO) != 0) {
            if (user.getNowMoney().add(request.getMoneyValue()).compareTo(new BigDecimal("********.99")) > 0) {
                throw new CrmebException("余额添加后后不能大于********.99");
            }
        }

        if (request.getIntegralType().equals(2) && request.getIntegralValue() != 0) {
            if (user.getIntegral() - request.getIntegralValue() < 0) {
                throw new CrmebException("积分扣减后不能小于0");
            }
        }
        if (request.getIntegralType().equals(1) && request.getIntegralValue() != 0) {
            if ((user.getIntegral() + request.getIntegralValue()) > ********) {
                throw new CrmebException("积分添加后不能大于********");
            }
        }

        Boolean execute = transactionTemplate.execute(e -> {
            // 处理余额
            if (request.getMoneyValue().compareTo(BigDecimal.ZERO) > 0) {
                // 生成UserBill
                UserBill userBill = new UserBill();
                userBill.setUid(user.getUid());
                userBill.setLinkId("0");

                userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                userBill.setNumber(request.getMoneyValue());
                userBill.setStatus(1);
                userBill.setCreateTime(DateUtil.nowDateTime());
                String remark = request.getMoneyRemark();

                if (request.getMoneyType() == 1) {// 增加
                    userBill.setPm(1);
                    userBill.setTitle("奖励");
                    userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_ADD);
                    userBill.setBalance(user.getNowMoney().add(request.getMoneyValue()));
                    userBill.setMark(remark);
                    userBill.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
                    userBill.setCreateTime(DateUtil.nowDateTime());

                    userBillService.save(userBill);
                    operationNowMoney(user.getUid(), request.getMoneyValue(), user.getNowMoney(), "add");
                } else {
                    userBill.setPm(0);
                    userBill.setTitle("奖励");
                    userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_SUB);
                    userBill.setBalance(user.getNowMoney().subtract(request.getMoneyValue()));
                    userBill.setMark(remark);
                    userBill.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
                    userBill.setCreateTime(DateUtil.nowDateTime());

                    userBillService.save(userBill);
                    operationNowMoney(user.getUid(), request.getMoneyValue(), user.getNowMoney(), "sub");
                }
            }

            // 处理积分
            if (request.getIntegralValue() > 0) {
                String remark = SecurityUtil.getLoginUserVo().getUser().getAccount().concat(request.getIntegralRemark());
                // 生成记录
                UserIntegralRecord integralRecord = new UserIntegralRecord();
                integralRecord.setUid(user.getUid());
                integralRecord.setLinkType(IntegralRecordConstants.INTEGRAL_RECORD_LINK_TYPE_SIGN);
                integralRecord.setTitle(IntegralRecordConstants.BROKERAGE_RECORD_TITLE_SYSTEM);
                integralRecord.setIntegral(request.getIntegralValue());
                integralRecord.setStatus(IntegralRecordConstants.INTEGRAL_RECORD_STATUS_COMPLETE);
                if (request.getIntegralType() == 1) {// 增加
                    integralRecord.setType(IntegralRecordConstants.INTEGRAL_RECORD_TYPE_ADD);
                    integralRecord.setBalance(user.getIntegral() + request.getIntegralValue());
                    integralRecord.setMark(remark);

                    operationIntegral(user.getUid(), request.getIntegralValue(), user.getIntegral(), "add");
                } else {
                    integralRecord.setType(IntegralRecordConstants.INTEGRAL_RECORD_TYPE_SUB);
                    integralRecord.setBalance(user.getIntegral() - request.getIntegralValue());
                    integralRecord.setMark(remark);
                    operationIntegral(user.getUid(), request.getIntegralValue(), user.getIntegral(), "sub");
                }
                userIntegralRecordService.save(integralRecord);
            }
            return Boolean.TRUE;
        });

            if (!execute) {
                throw new CrmebException("修改积分/余额失败");
            }
            return execute;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateIntegralMoneyBatch(UserOperateIntegralMoneyRequest request) {
        if (ObjectUtil.isNull(request.getMoneyValue()) || ObjectUtil.isNull(request.getIntegralValue())) {
            throw new CrmebException("至少输入一个金额");
        }
        if (request.getMoneyValue().compareTo(BigDecimal.ZERO) < 1 && request.getIntegralValue() <= 0) {
            throw new CrmebException("修改值不能等小于等于0");
        }
        String accounts = request.getAccount();
        accounts = accounts.replaceAll(" ", "");
        String[] account = accounts.split("\n");
        for (String string : account) {
            User user = getUserByAccount(string);
            if (ObjectUtil.isNull(user)) {
                continue;
            }
            // 减少时要判断小于0的情况,添加时判断是否超过数据限制
            if (request.getMoneyType().equals(2) && request.getMoneyValue().compareTo(BigDecimal.ZERO) != 0) {
                if (user.getNowMoney().subtract(request.getMoneyValue()).compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }
            }
            if (request.getMoneyType().equals(1) && request.getMoneyValue().compareTo(BigDecimal.ZERO) != 0) {
                if (user.getNowMoney().add(request.getMoneyValue()).compareTo(new BigDecimal("********.99")) > 0) {
                    continue;
                }
            }

            if (request.getIntegralType().equals(2) && request.getIntegralValue() != 0) {
                if (user.getIntegral() - request.getIntegralValue() < 0) {
                    continue;
                }
            }
            if (request.getIntegralType().equals(1) && request.getIntegralValue() != 0) {
                if ((user.getIntegral() + request.getIntegralValue()) > ********) {
                    continue;
                }
            }

            transactionTemplate.execute(e -> {
                // 处理余额
                if (request.getMoneyValue().compareTo(BigDecimal.ZERO) > 0) {
                    // 生成UserBill
                    UserBill userBill = new UserBill();
                    userBill.setUid(user.getUid());
                    userBill.setLinkId("0");
                    userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                    userBill.setNumber(request.getMoneyValue());
                    userBill.setStatus(1);
                    userBill.setCreateTime(DateUtil.nowDateTime());
                    String remark = request.getMoneyRemark();

                    if (request.getMoneyType() == 1) {// 增加
                        userBill.setPm(1);
                        userBill.setTitle("奖励");
                        userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_ADD);
                        userBill.setBalance(user.getNowMoney().add(request.getMoneyValue()));
                        userBill.setMark(remark);
                        userBill.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
                        userBill.setCreateTime(DateUtil.nowDateTime());

                        userBillService.save(userBill);
                        operationNowMoney(user.getUid(), request.getMoneyValue(), user.getNowMoney(), "add");
                    } else {
                        userBill.setPm(0);
                        userBill.setTitle("奖励");
                        userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_SUB);
                        userBill.setBalance(user.getNowMoney().subtract(request.getMoneyValue()));
                        userBill.setMark(remark);
                        userBill.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
                        userBill.setCreateTime(DateUtil.nowDateTime());

                        userBillService.save(userBill);
                        operationNowMoney(user.getUid(), request.getMoneyValue(), user.getNowMoney(), "sub");
                    }
                }

                // 处理积分
                if (request.getIntegralValue() > 0) {
                    String remark = SecurityUtil.getLoginUserVo().getUser().getAccount().concat(request.getIntegralRemark());
                    // 生成记录
                    UserIntegralRecord integralRecord = new UserIntegralRecord();
                    integralRecord.setUid(user.getUid());
                    integralRecord.setLinkType(IntegralRecordConstants.INTEGRAL_RECORD_LINK_TYPE_SIGN);
                    integralRecord.setTitle(IntegralRecordConstants.BROKERAGE_RECORD_TITLE_SYSTEM);
                    integralRecord.setIntegral(request.getIntegralValue());
                    integralRecord.setStatus(IntegralRecordConstants.INTEGRAL_RECORD_STATUS_COMPLETE);
                    if (request.getIntegralType() == 1) {// 增加
                        integralRecord.setType(IntegralRecordConstants.INTEGRAL_RECORD_TYPE_ADD);
                        integralRecord.setBalance(user.getIntegral() + request.getIntegralValue());
                        integralRecord.setMark(remark);

                        operationIntegral(user.getUid(), request.getIntegralValue(), user.getIntegral(), "add");
                    } else {
                        integralRecord.setType(IntegralRecordConstants.INTEGRAL_RECORD_TYPE_SUB);
                        integralRecord.setBalance(user.getIntegral() - request.getIntegralValue());
                        integralRecord.setMark(remark);
                        operationIntegral(user.getUid(), request.getIntegralValue(), user.getIntegral(), "sub");
                    }
                    userIntegralRecordService.save(integralRecord);
                }
                return Boolean.TRUE;
            });
        }
        return true;
    }

    /**
     * 更新用户金额
     *
     * @param user  用户
     * @param price 金额
     * @param type  增加add、扣减sub
     * @return 更新后的用户对象
     */
    @Override
    public Boolean updateNowMoney(User user, BigDecimal price, String type) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + user.getUid(), () -> {
            LambdaUpdateWrapper<User> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
            if (type.equals("add")) {
                lambdaUpdateWrapper.set(User::getNowMoney, user.getNowMoney().add(price));
            } else {
                lambdaUpdateWrapper.set(User::getNowMoney, user.getNowMoney().subtract(price));
            }
            lambdaUpdateWrapper.eq(User::getUid, user.getUid());
            if (type.equals("sub")) {
                lambdaUpdateWrapper.apply(StrUtil.format(" now_money - {} >= 0", price));
            }
            return update(lambdaUpdateWrapper);
        });
    }

    /**
     * 会员分组
     *
     * @param id      String id
     * @param groupId Integer 分组Id
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean group(String id, Integer groupId) {
        if (StrUtil.isBlank(id)) throw new CrmebException("会员编号不能为空");
        if (Objects.isNull(groupId)) throw new CrmebException("分组id不能为空");

        //循环id处理
        List<Integer> idList = CrmebUtil.stringToArray(id);
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<User> list = getListInUid(idList);
        if (CollUtil.isEmpty(list)) throw new CrmebException("没有找到用户信息");
        if (list.size() < idList.size()) {
            throw new CrmebException("没有找到用户信息");
        }
        for (User user : list) {
            user.setGroupId(groupId);
            userRechargeService.updateGroup(user.getUid(), user.getGroupId());
            userExtractService.updateGroup(user.getUid(), user.getGroupId());
            userBrokerageRecordService.updateGroup(user.getUid(), user.getGroupId());
        }
        return updateBatchById(list);
    }

    /**
     * 用户id in list
     *
     * @param uidList List<Integer> id
     * <AUTHOR>
     * @since 2020-04-28
     */
    private List<User> getListInUid(List<Integer> uidList) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(User::getUid, uidList);
        return userDao.selectList(lambdaQueryWrapper);
    }

    /**
     * 修改密码
     *
     * @param request PasswordRequest 密码
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean password(PasswordRequest request) {
        try {
            User user = getInfo();
            if (user == null) {
                throw new CrmebException("用户登录过期");
            }
            String oldPassword = CrmebUtil.encryptPassword(request.getOldPassword(), user.getAccount());
            if (!oldPassword.equals(user.getPwd())) {
                throw new CrmebException("旧密码错误");
            }
            //修改密码
            return lambdaUpdate().eq(User::getUid, user.getUid()).set(User::getPwd, CrmebUtil.encryptPassword(request.getPassword(), user.getAccount())).update();
        } catch (Exception e) {
            logger.error("修改密码失败：{}", e.getMessage(), e);
            throw new CrmebException("修改密码失败");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawReset(WithdrawPasswordRequest request) {
        User user = getInfo();
        if (StringUtils.isNotBlank(user.getWithdrawPwd())) {
            if (StringUtils.isBlank(request.getOldPassword())) {
                throw new CrmebException("请输入旧密码");
            }
            String oldPassword = CommonUtil.md5(request.getOldPassword(), 2);
            if (!oldPassword.equals(user.getWithdrawPwd())) {
                throw new CrmebException("旧密码错误");
            }
        }
        //密码
        user.setWithdrawPwd(CommonUtil.md5(request.getPassword(), 2));
        return updateById(user);
    }

    @Override
    public Boolean paymentReset(WithdrawPasswordRequest request) {
        User user = getInfo();
        if (StringUtils.isNotBlank(user.getPaymentPwd())) {
            if (StringUtils.isBlank(request.getOldPassword())) {
                throw new CrmebException("请输入旧密码");
            }
            String oldPassword = CommonUtil.md5(request.getOldPassword(), 2);
            if (!oldPassword.equals(user.getPaymentPwd())) {
                throw new CrmebException("旧密码错误");
            }
        }
        //密码
        user.setPaymentPwd(CommonUtil.md5(request.getPassword(), 2));
        return updateById(user);
    }

    /**
     * 获取个人资料
     *
     * @return User
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public User getInfo() {
        if (getUserId() == 0) {
            return null;
        }
        return getById(getUserId());
    }

    /**
     * 获取个人资料
     *
     * @return User
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public User getInfoException() {
        User user = getInfo();
        if (user == null) {
            throw new CrmebException("用户信息不存在！");
        }

        if (!user.getStatus()) {
            throw new CrmebException("用户已经被禁用！");
        }
        return user;
    }

    /**
     * 获取当前用户id
     *
     * @return Integer
     */
    @Override
    public Integer getUserIdException() {
        Integer id = tokenComponet.getUserId();
        if (null == id) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }
        return id;
    }

    /**
     * 获取当前用户id
     *
     * @return Integer
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public Integer getUserId() {
        Integer id = tokenComponet.getUserId();
        if (null == id) {
            return 0;
        }
        return id;
    }


    /**
     * 按开始结束时间查询每日新增用户数量
     *
     * @param date String 时间范围
     * @return HashMap<String, Object>
     */
    @Override
    public Map<Object, Object> getAddUserCountGroupDate(String date) {
        Map<Object, Object> map = new HashMap<>();
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("count(uid) as uid", "left(create_time, 10) as create_time");
        if (StringUtils.isNotBlank(date)) {
            DateLimitUtilVo dateLimit = DateUtil.getDateLimit(date);
            queryWrapper.between("create_time", dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        queryWrapper.groupBy("left(create_time, 10)").orderByAsc("create_time");
        List<User> list = userDao.selectList(queryWrapper);
        if (list.size() < 1) {
            return map;
        }

        for (User user : list) {
            map.put(DateUtil.dateToStr(user.getCreateTime(), Constants.DATE_FORMAT_DATE), user.getUid());
        }
        return map;
    }

    /**
     * 换绑手机号校验
     */
    @Override
    public Boolean updatePhoneVerify(UserBindingPhoneUpdateRequest request) {
        User user = getInfoException();
        //检测验证码
        checkValidateCode(user.getNumberCode() + request.getPhone(), request.getCaptcha());

        //删除验证码
        redisUtil.delete(getValidateCodeRedisKey(user.getNumberCode() + request.getPhone()));

        String string = CommonUtil.md5(request.getPhone(), 2);

        if (!user.getEncryptedPhone().equals(string)) {
            throw new CrmebException("该手机号不是绑定的手机号");
        }

        return Boolean.TRUE;
    }

    /**
     * 换绑手机号
     */
    @Override
    public Boolean updatePhone(UserBindingPhoneUpdateRequest request) {
        User info = this.getInfo();
        //检测验证码
        checkValidateCode(info.getNumberCode() + request.getPhone(), request.getCaptcha());

        //删除验证码
        redisUtil.delete(getValidateCodeRedisKey(request.getPhone()));

        //检测当前手机号是否已经是账号
        User user = getByEncryptedPhone(info.getNumberCode(), request.getPhone());
        if (null != user) {
            throw new CrmebException("此手机号码已被注册");
        }
        //查询手机号信息
        User bindUser = getInfoException();
        bindUser.setPhone(request.getPhone());
        bindUser.setEncryptedPhone(CommonUtil.md5(request.getPhone(), 2));
        return updateById(bindUser);
    }

    /**
     * 用户中心
     *
     * @return UserCenterResponse
     */
    @Override
    public UserCenterResponse getUserCenter() {
        User currentUser = getInfo();
        if (ObjectUtil.isNull(currentUser)) {
            throw new CrmebException("您的登录已过期，请先登录");
        }
        if (StringUtils.isBlank(currentUser.getPhone())) {
            currentUser.setPhone("号码已加密保护");
        }
        UserCenterResponse userCenterResponse = new UserCenterResponse();
        BeanUtils.copyProperties(currentUser, userCenterResponse);
        userCenterResponse.setIsWithdrawPwd(StringUtils.isNotBlank(currentUser.getWithdrawPwd()));
        userCenterResponse.setIsPaymentPwd(StringUtils.isNotBlank(currentUser.getPaymentPwd()));

        //专属昵称
        if (Objects.nonNull(currentUser.getExclusiveId())) {
            UserExclusive userExclusive = userExclusiveService.getInfoById(currentUser.getExclusiveId());
            if (userExclusive != null) {
                userCenterResponse.setExclusiveName(userExclusive.getName());
                userCenterResponse.setExclusiveIcon(userExclusive.getIcon());
            }
        }
        // 优惠券数量
        userCenterResponse.setCouponCount(storeCouponUserService.getUseCount(currentUser.getUid()));
        // 收藏数量
        userCenterResponse.setCollectCount(storeProductRelationService.getCollectCountByUid(currentUser.getUid()));
        //总余额
        userCenterResponse.setTotalBalance(currentUser.getNowMoney().add(currentUser.getFrozenBalance()));

        userCenterResponse.setVip(false);
        if (userCenterResponse.getLevel() > 0) {
            SystemUserLevel systemUserLevel = systemUserLevelService.getByLevelId(currentUser.getLevel());
            if (ObjectUtil.isNotNull(systemUserLevel)) {
                userCenterResponse.setVip(true);
                userCenterResponse.setVipIcon(systemUserLevel.getIcon());
                userCenterResponse.setVipName(systemUserLevel.getName());
            }
        }
        // 充值开关
        String rechargeSwitch = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_RECHARGE_SWITCH);
        if (StrUtil.isNotBlank(rechargeSwitch)) {
            userCenterResponse.setRechargeSwitch(Boolean.valueOf(rechargeSwitch));
        }

        // 判断是否展示我的推广，1.分销模式是否开启
        String funcStatus = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_BROKERAGE_FUNC_STATUS);
        if (!funcStatus.equals("1")) {
            userCenterResponse.setIsPromoter(false);
        }
        //是否启用阿里云人脸
        String aliyunFaceEnabled = systemConfigService.getValueByKey("aliyunFaceEnabled");
        userCenterResponse.setAliyunFaceEnabled("1".equals(aliyunFaceEnabled));

        //充值和资金明细开关
        String rechargeRecordEnabled = systemConfigService.getValueByKey("rechargeRecordEnabled");
        userCenterResponse.setRechargeRecordEnabled("1".equals(rechargeRecordEnabled));

        String AmountDetailEnabled = systemConfigService.getValueByKey("AmountDetailEnabled");
        userCenterResponse.setAmountDetailEnabled("1".equals(AmountDetailEnabled));


        // 保存用户访问记录
        UserVisitRecord visitRecord = new UserVisitRecord();
        visitRecord.setDate(cn.hutool.core.date.DateUtil.date().toString("yyyy-MM-dd"));
        visitRecord.setUid(getUserId());
        visitRecord.setVisitType(4);
        userVisitRecordService.save(visitRecord);
        return userCenterResponse;
    }

    /**
     * 根据用户id获取用户列表 map模式
     *
     * @return HashMap<Integer, User>
     */
    @Override
    public HashMap<Integer, User> getMapListInUid(List<Integer> uidList) {
        List<User> userList = getListInUid(uidList);
        return getMapByList(userList);
    }

    /**
     * 根据用户id获取用户列表 map模式
     *
     * @return HashMap<Integer, User>
     * <AUTHOR>
     * @since 2020-04-28
     */
    private HashMap<Integer, User> getMapByList(List<User> list) {
        HashMap<Integer, User> map = new HashMap<>();
        if (null == list || list.size() < 1) {
            return map;
        }

        for (User user : list) {
            map.put(user.getUid(), user);
        }

        return map;
    }

    /**
     * 重置连续签到天数
     *
     * @param userId Integer 用户id
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public void repeatSignNum(Integer userId) {
        User user = new User();
        user.setUid(userId);
        user.setSignNum(0);
        updateById(user);
    }

    /**
     * 会员标签
     *
     * @param id         String id
     * @param tagIdValue Integer 标签Id
     */
    @Override
    public Boolean tag(String id, String tagIdValue) {
        if (StrUtil.isBlank(id)) throw new CrmebException("会员编号不能为空");
        if (StrUtil.isBlank(tagIdValue)) throw new CrmebException("标签id不能为空");

        //循环id处理
        List<Integer> idList = CrmebUtil.stringToArray(id);
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<User> list = getListInUid(idList);
        if (CollUtil.isEmpty(list)) throw new CrmebException("没有找到用户信息");
        if (list.size() < 1) {
            throw new CrmebException("没有找到用户信息");
        }
        for (User user : list) {
            user.setTagId(tagIdValue);
        }
        return updateBatchById(list);
    }

    /**
     * 根据用户id获取自己本身的推广用户
     *
     * @param userIdList List<Integer> 用户id集合
     * @return List<User>
     */
    @Override
    public List<Integer> getSpreadPeopleIdList(List<Integer> userIdList) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(User::getUid); //查询用户id
        lambdaQueryWrapper.in(User::getSpreadUid, userIdList); //xx的下线集合
        List<User> list = userDao.selectList(lambdaQueryWrapper);

        if (null == list || list.size() < 1) {
            return new ArrayList<>();
        }
        return list.stream().map(User::getUid).distinct().collect(Collectors.toList());
    }

    /**
     * 根据用户id获取自己本身的推广用户
     */
    @Override
    public List<UserSpreadPeopleItemResponse> getSpreadPeopleList(
            List<Integer> userIdList, String keywords, String sortKey, String isAsc, PageParamRequest pageParamRequest) {

        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        Map<String, Object> map = new HashMap<>();
        map.put("userIdList", userIdList.stream().map(String::valueOf).distinct().collect(Collectors.joining(",")));
        if (StringUtils.isNotBlank(keywords)) {
            map.put("keywords", "%" + keywords + "%");
        }
        map.put("sortKey", "create_time");
        if (StringUtils.isNotBlank(sortKey)) {
            map.put("sortKey", sortKey);
        }
        map.put("sortValue", Constants.SORT_DESC);
        if (isAsc.toLowerCase().equals(Constants.SORT_ASC)) {
            map.put("sortValue", Constants.SORT_ASC);
        }

        return userDao.getSpreadPeopleList(map);
    }

    /**
     * 检测手机验证码
     */
    public void checkValidateCode(String phone, String value) {
        phone = phone.replace("+86", "");
        Object validateCode = redisUtil.get(getValidateCodeRedisKey(phone));
        if (validateCode == null) {
            throw new CrmebException("验证码已过期");
        }

        if (!validateCode.toString().equals(value)) {
            throw new CrmebException("验证码错误");
        }
    }

    /**
     * 检测手机验证码key
     *
     * @param phone String 手机号
     * @return String
     */
    @Override
    public String getValidateCodeRedisKey(String phone) {
        return SmsConstants.SMS_VALIDATE_PHONE + phone;
    }

    /**
     * 手机号注册用户
     *
     * @param loginMobileRequest 手机号
     * @param spreadUid          推广人编号
     * @return User
     */
    @Override
    public User registerPhone(LoginMobileRequest loginMobileRequest, Integer spreadUid) {
        String phone = loginMobileRequest.getPhone();
        User user = new User();
        user.setAccount(phone);
        user.setPwd(CommonUtil.createPwd(phone));
        user.setPhone(phone);
        user.setUserType(Constants.USER_LOGIN_TYPE_H5);
        user.setNickname(CommonUtil.createNickName(phone));
        user.setAvatar(systemConfigService.getValueByKey(Constants.USER_DEFAULT_AVATAR_CONFIG_KEY));
        Date nowDate = DateUtil.nowDateTime();
        user.setCreateTime(nowDate);
        user.setLastLoginTime(nowDate);
        user.setNumberCode(loginMobileRequest.getNumberCode());

        // 推广人
        user.setSpreadUid(0);
        Boolean check = checkBingSpread(user, spreadUid, "new");
        if (check) {
            user.setSpreadUid(spreadUid);
            user.setSpreadTime(nowDate);
        }

        // 查询是否有新人注册赠送优惠券
        List<StoreCouponUser> couponUserList = CollUtil.newArrayList();
        List<StoreCoupon> couponList = storeCouponService.findRegisterList();
        if (CollUtil.isNotEmpty(couponList)) {
            couponList.forEach(storeCoupon -> {
                //是否有固定的使用时间
                if (!storeCoupon.getIsFixedTime()) {
                    String endTime = DateUtil.addDay(DateUtil.nowDate(Constants.DATE_FORMAT), storeCoupon.getDay(), Constants.DATE_FORMAT);
                    storeCoupon.setUseEndTime(DateUtil.strToDate(endTime, Constants.DATE_FORMAT));
                    storeCoupon.setUseStartTime(DateUtil.nowDateTimeReturnDate(Constants.DATE_FORMAT));
                }

                StoreCouponUser storeCouponUser = new StoreCouponUser();
                storeCouponUser.setCouponId(storeCoupon.getId());
                storeCouponUser.setName(storeCoupon.getName());
                storeCouponUser.setMoney(storeCoupon.getMoney());
                storeCouponUser.setMinPrice(storeCoupon.getMinPrice());
                storeCouponUser.setStartTime(storeCoupon.getUseStartTime());
                storeCouponUser.setEndTime(storeCoupon.getUseEndTime());
                storeCouponUser.setUseType(storeCoupon.getUseType());
                storeCouponUser.setType(CouponConstants.STORE_COUPON_USER_TYPE_REGISTER);
                if (storeCoupon.getUseType() > 1) {
                    storeCouponUser.setPrimaryKey(storeCoupon.getPrimaryKey());
                }
                couponUserList.add(storeCouponUser);
            });
        }

        Boolean execute = transactionTemplate.execute(e -> {
            save(user);
            // 推广人处理
            if (check) {
                updateSpreadCountByUid(spreadUid, "add");
            }
            // 赠送客户优惠券
            if (CollUtil.isNotEmpty(couponUserList)) {
                couponUserList.forEach(couponUser -> couponUser.setUid(user.getUid()));
                storeCouponUserService.saveBatch(couponUserList);
                couponList.forEach(coupon -> storeCouponService.deduction(coupon.getId(), 1, coupon.getIsLimited()));
            }
            return Boolean.TRUE;
        });
        if (!execute) {
            throw new CrmebException("创建用户失败!");
        }
        return user;
    }

    @Override
    public User register(RegisterRequest registerRequest, User supUser) {
        User user = new User();
        user.setAccount(registerRequest.getAccount());
        user.setPwd(CrmebUtil.encryptPassword(registerRequest.getPassword(), registerRequest.getAccount()));
        user.setPhone(registerRequest.getPhone());
        user.setUserType(Constants.USER_LOGIN_TYPE_H5);
        user.setNickname(registerRequest.getAccount());
        user.setAvatar(systemConfigService.getValueByKey(Constants.USER_DEFAULT_AVATAR_CONFIG_KEY));
        Date nowDate = DateUtil.nowDateTime();
        user.setCreateTime(nowDate);
        user.setLastLoginTime(nowDate);
        user.setNumberCode(registerRequest.getNumberCode());
        user.setEncryptedPhone(CommonUtil.md5(registerRequest.getPhone(), 2));
        user.setGroupId(supUser.getGroupId());
        for (int i = 0; i < 5; i++) {
            String invitationCode = ExchangeCodeGenerator.generateInvitationCode();
            User userByInvitationCode = getUserByInvitationCode(invitationCode);
            if (userByInvitationCode == null) {
                user.setInvitationCode(invitationCode);
                break;
            }
        }
        if (StringUtils.isBlank(user.getInvitationCode())) {
            throw new CrmebException("创建失败，请重试!");
        }

        // 推广人
        user.setSpreadUid(registerRequest.getSpreadPid());
        Boolean check = checkBingSpread(user, registerRequest.getSpreadPid(), "new");
        if (check) {
            user.setSpreadUid(registerRequest.getSpreadPid());
            user.setSpreadTime(nowDate);
        }

        // 查询是否有新人注册赠送优惠券
        List<StoreCouponUser> couponUserList = CollUtil.newArrayList();
        List<StoreCoupon> couponList = storeCouponService.findRegisterList();
        if (CollUtil.isNotEmpty(couponList)) {
            couponList.forEach(storeCoupon -> {
                //是否有固定的使用时间
                if (!storeCoupon.getIsFixedTime()) {
                    String endTime = DateUtil.addDay(DateUtil.nowDate(Constants.DATE_FORMAT), storeCoupon.getDay(), Constants.DATE_FORMAT);
                    storeCoupon.setUseEndTime(DateUtil.strToDate(endTime, Constants.DATE_FORMAT));
                    storeCoupon.setUseStartTime(DateUtil.nowDateTimeReturnDate(Constants.DATE_FORMAT));
                }

                StoreCouponUser storeCouponUser = new StoreCouponUser();
                storeCouponUser.setCouponId(storeCoupon.getId());
                storeCouponUser.setName(storeCoupon.getName());
                storeCouponUser.setMoney(storeCoupon.getMoney());
                storeCouponUser.setMinPrice(storeCoupon.getMinPrice());
                storeCouponUser.setStartTime(storeCoupon.getUseStartTime());
                storeCouponUser.setEndTime(storeCoupon.getUseEndTime());
                storeCouponUser.setUseType(storeCoupon.getUseType());
                storeCouponUser.setType(CouponConstants.STORE_COUPON_USER_TYPE_REGISTER);
                if (storeCoupon.getUseType() > 1) {
                    storeCouponUser.setPrimaryKey(storeCoupon.getPrimaryKey());
                }
                couponUserList.add(storeCouponUser);
            });
        }

        Boolean execute = transactionTemplate.execute(e -> {
            save(user);
            // 推广人处理
            if (check) {
                updateSpreadCountByUid(registerRequest.getSpreadPid(), "add");
            }
            // 赠送客户优惠券
            if (CollUtil.isNotEmpty(couponUserList)) {
                couponUserList.forEach(couponUser -> couponUser.setUid(user.getUid()));
                storeCouponUserService.saveBatch(couponUserList);
                couponList.forEach(coupon -> storeCouponService.deduction(coupon.getId(), 1, coupon.getIsLimited()));
            }
            return Boolean.TRUE;
        });
        if (!execute) {
            throw new CrmebException("创建用户失败!");
        }
        return user;
    }

    /**
     * 更新推广员推广数
     *
     * @param uid  uid
     * @param type add or sub
     */
    public Boolean updateSpreadCountByUid(Integer uid, String type) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql("spread_count = spread_count + 1");
        } else {
            updateWrapper.setSql("spread_count = spread_count - 1");
        }
        updateWrapper.eq("uid", uid);
        return update(updateWrapper);
    }

    /**
     * 添加/扣减佣金
     *
     * @param uid            用户id
     * @param price          金额
     * @param brokeragePrice 历史金额
     * @param type           类型：add—添加，sub—扣减
     * @return Boolean
     */
    @Override
    public Boolean operationBrokerage(Integer uid, BigDecimal price, BigDecimal brokeragePrice, String type) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + uid, () -> {
            UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
            if (type.equals("add")) {
                updateWrapper.setSql(StrUtil.format("brokerage_price = brokerage_price + {}", price));
            } else {
                updateWrapper.setSql(StrUtil.format("brokerage_price = brokerage_price - {}", price));
                updateWrapper.last(StrUtil.format(" and (brokerage_price - {} >= 0)", price));
            }
            updateWrapper.eq("uid", uid);
            updateWrapper.eq("brokerage_price", brokeragePrice);
            return update(updateWrapper);
        });
    }

    /**
     * 添加/扣减余额
     *
     * @param uid      用户id
     * @param price    金额
     * @param nowMoney 历史金额
     * @param type     类型：add—添加，sub—扣减
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean operationNowMoney(Integer uid, BigDecimal price, BigDecimal nowMoney, String type) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + uid, () -> {
            UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
            if (type.equals("add")) {
                updateWrapper.setSql(StrUtil.format("now_money = now_money + {}", price));
            } else {
                updateWrapper.setSql(StrUtil.format("now_money = now_money - {}", price));
                updateWrapper.last(StrUtil.format(" and (now_money - {} >= 0)", price));
            }
            updateWrapper.eq("uid", uid);
            boolean update = update(updateWrapper);
            if (!update) {
                throw new RuntimeException("修改失败，请重试！");
            }
            return update;
        });
    }

    /**
     * 添加/扣减积分
     *
     * @param uid         用户id
     * @param integral    积分
     * @param nowIntegral 历史积分
     * @param type        类型：add—添加，sub—扣减
     * @return Boolean
     */
    @Override
    public Boolean operationIntegral(Integer uid, Integer integral, Integer nowIntegral, String type) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + uid, () -> {
            UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
            if (type.equals("add")) {
                updateWrapper.setSql(StrUtil.format("integral = integral + {}", integral));
            } else {
                updateWrapper.setSql(StrUtil.format("integral = integral - {}", integral));
                updateWrapper.last(StrUtil.format(" and (integral - {} >= 0)", integral));
            }
            updateWrapper.eq("uid", uid);
            updateWrapper.eq("integral", nowIntegral);
            return update(updateWrapper);
        });
    }

    /**
     * PC后台分销员列表
     *
     * @param keywords    搜索参数
     * @param dateLimit   时间参数
     * @param pageRequest 分页参数
     * @return PageInfo
     */
    @Override
    public PageInfo<User> getAdminSpreadPeopleList(String keywords, String dateLimit, PageParamRequest pageRequest) {
        Page<User> pageUser = PageHelper.startPage(pageRequest.getPage(), pageRequest.getLimit());
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        // id,头像，昵称，姓名，电话，推广用户数，推广订单数，推广订单额，佣金总金额，已提现金额，提现次数，未提现金额，上级推广人
        lqw.select(User::getUid, User::getNickname, User::getRealName, User::getPhone, User::getAvatar,
                User::getSpreadCount, User::getBrokeragePrice, User::getSpreadUid, User::getPromoterTime);
        lqw.eq(User::getIsPromoter, true);
        if (StrUtil.isNotBlank(keywords)) {
            lqw.and(i -> i.eq(User::getUid, keywords) //用户账号
                    .or().like(User::getNickname, keywords) //昵称
                    .or().like(User::getPhone, keywords)); //手机号码
        }
        if (StrUtil.isNotBlank(dateLimit)) {
            DateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(dateLimit);
            lqw.between(User::getPromoterTime, dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        lqw.orderByDesc(User::getUid);
        List<User> userList = userDao.selectList(lqw);
        return CommonPage.copyPageInfo(pageUser, userList);
    }

    /**
     * 检测能否绑定关系
     *
     * @param user      当前用户
     * @param spreadUid 推广员Uid
     * @param type      用户类型:new-新用户，old—老用户
     * @return Boolean
     * 1.判断分销功能是否启用
     * 2.判断分销模式
     * 3.根据不同的分销模式校验
     * 4.指定分销，只有分销员才可以分销，需要spreadUid是推广员才可以绑定
     * 5.人人分销，可以直接绑定
     * *推广关系绑定，下级不能绑定自己的上级为下级，A->B->A(❌)
     */
    public Boolean checkBingSpread(User user, Integer spreadUid, String type) {
        log.info("checkBingSpread: userUid = {}，spreadUid = {}，type={}", user.getUid(), spreadUid, type);
        if (ObjectUtil.isNull(spreadUid)) {
            log.info("checkBingSpread: spreadUid is null，userUid = {}，spreadUid = {}，type={}", user.getUid(), spreadUid, type);
            return false;
        }
        if (spreadUid <= 0 || user.getSpreadUid() > 0) {
            log.info("checkBingSpread: spreadUid <= 0 || user.getSpreadUid() > 0，userUid = {}，spreadUid = {}，type={}",
                    user.getUid(),
                    spreadUid,
                    type);
            return false;
        }
        if (ObjectUtil.isNotNull(user.getUid()) && user.getUid().equals(spreadUid)) {
            log.info("checkBingSpread: ObjectUtil.isNotNull(user.getUid()) && user.getUid().equals(spreadUid)，userUid = {}，spreadUid = {}，type={}",
                    user.getUid(),
                    spreadUid,
                    type);
            return false;
        }
        // 判断分销功能是否启用
        String isOpen = systemConfigService.getValueByKey(Constants.CONFIG_KEY_STORE_BROKERAGE_IS_OPEN);
        if (StrUtil.isBlank(isOpen) || isOpen.equals("0")) {
            log.info("checkBingSpread: isOpen is false，userUid = {}，spreadUid = {}，type={}",
                    user.getUid(),
                    spreadUid,
                    type);
            return false;
        }
        if (type.equals("old")) {
            // 判断分销关系绑定类型（所有、新用户）
            String bindType = systemConfigService.getValueByKey(Constants.CONFIG_KEY_DISTRIBUTION_TYPE);
            if (StrUtil.isBlank(bindType) || bindType.equals("1")) {
                log.info("checkBingSpread: bindType is false，userUid = {}，spreadUid = {}，type={}",
                        user.getUid(),
                        spreadUid,
                        type);
                return false;
            }
            if (user.getSpreadUid().equals(spreadUid)) {
                log.info("checkBingSpread: user.getSpreadUid().equals(spreadUid)，userUid = {}，spreadUid = {}，type={}",
                        user.getUid(),
                        spreadUid,
                        type);
                return false;
            }
        }
        // 查询推广员
        User spreadUser = getById(spreadUid);
        if (ObjectUtil.isNull(spreadUser) || !spreadUser.getStatus()) {
            log.info("checkBingSpread: ObjectUtil.isNull(spreadUser) || !spreadUser.getStatus()，userUid = {}，spreadUid = {}，type={}",
                    user.getUid(),
                    spreadUid,
                    type);
            return false;
        }
        // 指定分销不是推广员不绑定
        if (!spreadUser.getIsPromoter()) {
            log.info("checkBingSpread: 指定分销不是推广员不绑定，userUid = {}，spreadUid = {}，type={}",
                    user.getUid(),
                    spreadUid,
                    type);
            return false;
        }
        // 下级不能绑定自己的上级为自己的下级
        if (ObjectUtil.isNotNull(user.getUid()) && spreadUser.getSpreadUid().equals(user.getUid())) {
            log.info("checkBingSpread: 下级不能绑定自己的上级为自己的下级，userUid = {}，spreadUid = {}，type={}",
                    user.getUid(),
                    spreadUid,
                    type);
            return false;
        }
        return true;
    }

    /**
     * 获取用户好友关系，spread_uid往下两级的用户信息
     *
     * @return List<User>
     */
    private List<User> getUserRelation(Integer userId) {
        List<User> userList = new ArrayList<>();
        User currUser = userDao.selectById(userId);
        if (currUser.getSpreadUid() > 0) {
            User spUser1 = userDao.selectById(currUser.getSpreadUid());
            if (null != spUser1) {
                userList.add(spUser1);
                if (spUser1.getSpreadUid() > 0) {
                    User spUser2 = userDao.selectById(spUser1.getSpreadUid());
                    if (null != spUser2) {
                        userList.add(spUser2);
                    }
                }
            }
        }
        return userList;
    }

    /**
     * 根据条件获取会员对应信息列表
     *
     * @param userId           用户id
     * @param type             0=消费记录，1=积分明细，2=签到记录，3=持有优惠券，4=余额变动，5=好友关系
     * @param pageParamRequest 分页参数
     * @return Object
     */
    @Override
    public Object getInfoByCondition(Integer userId, Integer type, PageParamRequest pageParamRequest) {
        switch (type) {
            case 0:
                return storeOrderService.findPaidListByUid(userId, pageParamRequest);
            case 1:
                AdminIntegralSearchRequest fmsq = new AdminIntegralSearchRequest();
                fmsq.setUid(userId);
                return userIntegralRecordService.findAdminList(fmsq, pageParamRequest);
            case 2:
                UserSign userSign = new UserSign();
                userSign.setUid(userId);
                return userSignService.getListByCondition(userSign, pageParamRequest);
            case 3:
                StoreCouponUserSearchRequest scur = new StoreCouponUserSearchRequest();
                scur.setUid(userId);
                return storeCouponUserService.findListByUid(userId, pageParamRequest);
            case 4:
                FundsMonitorSearchRequest fmsqq = new FundsMonitorSearchRequest();
                fmsqq.setUid(userId);
                //fmsqq.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                return userBillService.getList(fmsqq, pageParamRequest);
            case 5:
                return getUserRelation(userId);
        }

        return new ArrayList<>();
    }

    /**
     * 会员详情顶部数据
     *
     * @param userId Integer 用户id
     * @return Object
     */
    @Override
    public TopDetail getTopDetail(Integer userId) {
        TopDetail topDetail = new TopDetail();
        User currentUser = userDao.selectById(userId);
        topDetail.setUser(currentUser);
        topDetail.setBalance(currentUser.getNowMoney());
        topDetail.setIntegralCount(currentUser.getIntegral());
        topDetail.setMothConsumeCount(storeOrderService.getSumPayPriceByUidAndDate(userId, Constants.SEARCH_DATE_MONTH));
        topDetail.setAllConsumeCount(storeOrderService.getSumPayPriceByUid(userId));
        topDetail.setMothOrderCount(storeOrderService.getOrderCountByUidAndDate(userId, Constants.SEARCH_DATE_MONTH));
        topDetail.setAllOrderCount(storeOrderService.getOrderCountByUid(userId));
        return topDetail;
    }

    /**
     * 通过微信信息注册用户
     *
     * @param thirdUserRequest RegisterThirdUser 三方用户登录注册信息
     * @return User
     */
    @Override
    public User registerByThird(RegisterThirdUserRequest thirdUserRequest) {
        User user = new User();
        user.setAccount(DigestUtils.md5Hex(CrmebUtil.getUuid() + DateUtil.getNowTime()));
        user.setUserType(thirdUserRequest.getType());
        user.setNickname(thirdUserRequest.getNickName());
        String avatar = null;
        switch (thirdUserRequest.getType()) {
            case Constants.USER_LOGIN_TYPE_PUBLIC:
                avatar = thirdUserRequest.getHeadimgurl();
                break;
            case Constants.USER_LOGIN_TYPE_PROGRAM:
            case Constants.USER_LOGIN_TYPE_H5:
            case Constants.USER_LOGIN_TYPE_IOS_WX:
            case Constants.USER_LOGIN_TYPE_ANDROID_WX:
                avatar = thirdUserRequest.getAvatar();
                break;
        }
        user.setAvatar(avatar);
        user.setSpreadTime(DateUtil.nowDateTime());
        user.setSex(Integer.parseInt(thirdUserRequest.getSex()));
        user.setAddres(thirdUserRequest.getCountry() + "," + thirdUserRequest.getProvince() + "," + thirdUserRequest.getCity());
        return user;
    }

    /**
     * 根据推广级别和其他参数当前用户下的推广列表
     *
     * @param request 推广列表参数
     * @return 当前用户的推广人列表
     */
    @Override
    public PageInfo<User> getUserListBySpreadLevel(RetailShopStairUserRequest request, PageParamRequest pageParamRequest) {
        if (request.getType().equals(1)) {// 一级推广人
            return getFirstSpreadUserListPage(request, pageParamRequest);
        }
        if (request.getType().equals(2)) {// 二级推广人
            return getSecondSpreadUserListPage(request, pageParamRequest);
        }
        return getAllSpreadUserListPage(request, pageParamRequest);
    }

    // 分页获取一级推广员
    private PageInfo<User> getFirstSpreadUserListPage(RetailShopStairUserRequest request, PageParamRequest pageParamRequest) {
        Page<User> userPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getUid, User::getAvatar, User::getNickname, User::getIsPromoter, User::getSpreadCount, User::getPayCount);
        queryWrapper.eq(User::getSpreadUid, request.getUid());
        if (StrUtil.isNotBlank(request.getNickName())) {
            queryWrapper.and(e -> e.like(User::getNickname, request.getNickName()).or().eq(User::getUid, request.getNickName())
                    .or().eq(User::getPhone, request.getNickName()));
        }
        List<User> userList = userDao.selectList(queryWrapper);
        return CommonPage.copyPageInfo(userPage, userList);
    }

    // 分页获取二级推广员
    private PageInfo<User> getSecondSpreadUserListPage(RetailShopStairUserRequest request, PageParamRequest pageParamRequest) {
        // 先获取一级推广员
        List<User> firstUserList = getSpreadListBySpreadIdAndType(request.getUid(), 1);
        if (CollUtil.isEmpty(firstUserList)) {
            return new PageInfo<>(CollUtil.newArrayList());
        }
        List<Integer> userIds = firstUserList.stream().map(User::getUid).distinct().collect(Collectors.toList());
        Page<User> userPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getUid, User::getAvatar, User::getNickname, User::getIsPromoter, User::getSpreadCount, User::getPayCount);
        queryWrapper.in(User::getSpreadUid, userIds);
        if (StrUtil.isNotBlank(request.getNickName())) {
            queryWrapper.and(e -> e.like(User::getNickname, request.getNickName()).or().eq(User::getUid, request.getNickName())
                    .or().eq(User::getPhone, request.getNickName()));
        }
        List<User> userList = userDao.selectList(queryWrapper);
        return CommonPage.copyPageInfo(userPage, userList);
    }

    // 分页获取所有推广员
    private PageInfo<User> getAllSpreadUserListPage(RetailShopStairUserRequest request, PageParamRequest pageParamRequest) {
        // 先所有一级推广员
        List<User> firstUserList = getSpreadListBySpreadIdAndType(request.getUid(), 0);
        if (CollUtil.isEmpty(firstUserList)) {
            return new PageInfo<>(CollUtil.newArrayList());
        }
        List<Integer> userIds = firstUserList.stream().map(User::getUid).distinct().collect(Collectors.toList());
        Page<User> userPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getUid, User::getAvatar, User::getNickname, User::getIsPromoter, User::getSpreadCount, User::getPayCount);
        queryWrapper.in(User::getUid, userIds);
        if (StrUtil.isNotBlank(request.getNickName())) {
            queryWrapper.and(e -> e.like(User::getNickname, request.getNickName()).or().eq(User::getUid, request.getNickName())
                    .or().eq(User::getPhone, request.getNickName()));
        }
        List<User> userList = userDao.selectList(queryWrapper);
        return CommonPage.copyPageInfo(userPage, userList);
    }

    /**
     * 根据推广级别和其他参数获取推广列表
     *
     * @param request 推广层级和推广时间参数
     * @return 推广订单列表
     */
    @Override
    public PageInfo<SpreadOrderResponse> getOrderListBySpreadLevel(RetailShopStairUserRequest request, PageParamRequest pageParamRequest) {
        // 获取推广人列表
        if (ObjectUtil.isNull(request.getType())) {
            request.setType(0);
        }
        PageInfo<UserBrokerageRecord> recordPageInfo = userBrokerageRecordService.findAdminSpreadListByUid(request, pageParamRequest);
        List<SpreadOrderResponse> responseList = recordPageInfo.getList().stream().map(e -> {
            SpreadOrderResponse response = new SpreadOrderResponse();
            StoreOrder storeOrder = storeOrderService.getByOderId(e.getLinkId());
            response.setId(storeOrder.getId());
            response.setOrderId(storeOrder.getOrderId());
            response.setRealName(storeOrder.getRealName());
            response.setUserPhone(storeOrder.getUserPhone());
            response.setPrice(e.getPrice());
            response.setUpdateTime(e.getUpdateTime());
            return response;
        }).collect(Collectors.toList());

        return CommonPage.copyPageInfo(recordPageInfo, responseList);
    }

//    /**
//     * 根据推广级别和其他参数获取推广列表
//     *
//     * @param request 推广层级和推广时间参数
//     * @return 推广订单列表
//     */
//    @Override
//    public PageInfo<SpreadOrderResponse> getOrderListBySpreadLevel(RetailShopStairUserRequest request, PageParamRequest pageParamRequest) {
//        // 获取推广人列表
//        if (ObjectUtil.isNull(request.getType())) {
//            request.setType(0);
//        }
//        List<User> userList = getSpreadListBySpreadIdAndType(request.getUid(), request.getType());
//        if (CollUtil.isEmpty(userList)) {
//            return new PageInfo<>();
//        }
//
//        List<Integer> userIds = userList.stream().map(User::getUid).distinct().collect(Collectors.toList());
//        // 获取推广人订单号集合
//        List<StoreOrder> orderList = storeOrderService.getOrderListStrByUids(userIds, request);
//        if (CollUtil.isEmpty(orderList)) {
//            return new PageInfo<>();
//        }
//        List<String> orderNoList = CollUtil.newArrayList();
//        Map<String, StoreOrder> orderMap = CollUtil.newHashMap();
//        orderList.forEach(e -> {
//            orderNoList.add(e.getOrderId());
//            orderMap.put(e.getOrderId(), e);
//        });
//        // 获取用户佣金记录
//        PageInfo<UserBrokerageRecord> recordPageInfo = userBrokerageRecordService.findListByLinkIdsAndLinkTypeAndUid(orderNoList, BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_ORDER, request.getUid(), pageParamRequest);
//        List<SpreadOrderResponse> responseList = recordPageInfo.getList().stream().map(e -> {
//            SpreadOrderResponse response = new SpreadOrderResponse();
//            StoreOrder storeOrder = orderMap.get(e.getLinkId());
//            response.setId(storeOrder.getId());
//            response.setOrderId(storeOrder.getOrderId());
//            response.setRealName(storeOrder.getRealName());
//            response.setUserPhone(storeOrder.getUserPhone());
//            response.setPrice(e.getPrice());
//            response.setUpdateTime(e.getUpdateTime());
//            return response;
//        }).collect(Collectors.toList());
//
//        return CommonPage.copyPageInfo(recordPageInfo, responseList);
//    }

    /**
     * 获取推广人列表
     *
     * @param spreadUid 父Uid
     * @param type      类型 0 = 全部 1=一级推广人 2=二级推广人
     */
    private List<User> getSpreadListBySpreadIdAndType(Integer spreadUid, Integer type) {
        // 获取一级推广人
        List<User> userList = getSpreadListBySpreadId(spreadUid);
        if (CollUtil.isEmpty(userList)) return userList;
        if (type.equals(1)) return userList;
        // 获取二级推广人
        List<User> userSecondList = CollUtil.newArrayList();
        userList.forEach(user -> {
            List<User> childUserList = getSpreadListBySpreadId(user.getUid());
            if (CollUtil.isNotEmpty(childUserList)) {
                userSecondList.addAll(childUserList);
            }
        });
        if (type.equals(2)) {
            return userSecondList;
        }
        userList.addAll(userSecondList);
        return userList;
    }

    /**
     * 获取推广人列表
     *
     * @param spreadUid 父Uid
     */
    private List<User> getSpreadListBySpreadId(Integer spreadUid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getSpreadUid, spreadUid);
        return userDao.selectList(queryWrapper);
    }

    private Integer getSpreadListBySpreadIdOfCount(Integer spreadUid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getSpreadUid, spreadUid);
        return userDao.selectCount(queryWrapper);
    }

    /**
     * 根据用户id清除用户当前推广人
     *
     * @param userId 当前推广人id
     * @return 清除推广结果
     */
    @Override
    public boolean clearSpread(Integer userId) {
        User teamUser = getById(userId);
        User user = new User();
        user.setUid(userId);
        user.setPath("/0/");
        user.setSpreadUid(0);
        user.setSpreadTime(null);
        Boolean execute = transactionTemplate.execute(e -> {
            userDao.updateById(user);
            if (teamUser.getSpreadUid() > 0) {
                updateSpreadCountByUid(teamUser.getSpreadUid(), "sub");
            }
            return Boolean.TRUE;
        });
        return execute;
    }

    /**
     * 推广人排行
     *
     * @param type             String 类型
     * @param pageParamRequest PageParamRequest 分页
     * @return List<User>
     */
    @Override
    public List<User> getTopSpreadPeopleListByDate(String type, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("count(spread_count) as spread_count, spread_uid")
                .gt("spread_uid", 0)
                .eq("status", true);
        if (StrUtil.isNotBlank(type)) {
            DateLimitUtilVo dateLimit = DateUtil.getDateLimit(type);
            queryWrapper.between("spread_time", dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        queryWrapper.groupBy("spread_uid").orderByDesc("spread_count");
        List<User> spreadVoList = userDao.selectList(queryWrapper);
        if (spreadVoList.size() < 1) {
            return null;
        }

        List<Integer> spreadIdList = spreadVoList.stream().map(User::getSpreadUid).collect(Collectors.toList());
        if (spreadIdList.size() < 1) {
            return null;
        }

        ArrayList<User> userList = new ArrayList<>();
        //查询用户
        HashMap<Integer, User> userVoList = getMapListInUid(spreadIdList);

        //解决排序问题
        for (User spreadVo : spreadVoList) {
            User user = new User();
            User userVo = userVoList.get(spreadVo.getSpreadUid());
            user.setUid(spreadVo.getSpreadUid());
            user.setAvatar(userVo.getAvatar());
            user.setSpreadCount(spreadVo.getSpreadCount());
            if (StringUtils.isBlank(userVo.getNickname())) {
                user.setNickname(userVo.getPhone().substring(0, 2) + "****" + userVo.getPhone().substring(7));
            } else {
                user.setNickname(userVo.getNickname());
            }
            userList.add(user);
        }

        return userList;
    }

    /**
     * 推广人排行
     *
     * @param minPayCount int 最小消费次数
     * @param maxPayCount int 最大消费次数
     * @return Integer
     */
    @Override
    public Integer getCountByPayCount(int minPayCount, int maxPayCount) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.between(User::getPayCount, minPayCount, maxPayCount);
        return userDao.selectCount(lambdaQueryWrapper);
    }

    /**
     * 绑定推广关系（登录状态）
     *
     * @param spreadUid 推广人id
     */
    @Override
    public void bindSpread(Integer spreadUid) {
        //新用户会在注册的时候单独绑定，此处只处理登录用户
        if (ObjectUtil.isNull(spreadUid) || spreadUid == 0) {
            return;
        }
        User user = getInfo();
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("当前用户未登录,请先登录");
        }

        bindSpread(user, spreadUid);
    }

    private Boolean bindSpread(User user, Integer spreadUid) {

        Boolean checkBingSpread = checkBingSpread(user, spreadUid, "old");
        if (!checkBingSpread) return false;

        user.setSpreadUid(spreadUid);
        user.setSpreadTime(DateUtil.nowDateTime());

        Boolean execute = transactionTemplate.execute(e -> {
            updateById(user);
            updateSpreadCountByUid(spreadUid, "add");
            return Boolean.TRUE;
        });
        if (!execute) {
            logger.error(StrUtil.format("绑定推广人时出错，userUid = {}, spreadUid = {}", user.getUid(), spreadUid));
        }
        return execute;

    }

    /**
     * 更新推广人
     *
     * @param request 请求参数
     * @return Boolean 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editSpread(UserUpdateSpreadRequest request) {
        Integer userId = request.getUserId();
        Integer spreadUid = request.getSpreadUid();
        if (userId.equals(spreadUid)) {
            throw new CrmebException("上级推广人不能为自己，用户ID: " + userId);
        }
        User user = getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在，用户ID: " + userId);
        }
        if (user.getSpreadUid().equals(spreadUid)) {
            throw new CrmebException("当前推广人已经是所选人，用户ID: " + userId + "，推广人ID: " + spreadUid);
        }
        Integer oldSprUid = user.getSpreadUid();

        User spreadUser = getById(spreadUid);
        if (ObjectUtil.isNull(spreadUser)) {
            throw new CrmebException("上级推广人不存在，推广人ID: " + spreadUid);
        }
        if (spreadUser.getSpreadUid().equals(userId)) {
            throw new CrmebException("当前用户已是推广人的上级，用户ID: " + userId + "，推广人ID: " + spreadUid);
        }

        User tempUser = new User();
        tempUser.setUid(userId);
        tempUser.setSpreadUid(spreadUid);
        tempUser.setSpreadTime(DateUtil.nowDateTime());
        tempUser.setGroupId(user.getGroupId());

        // 执行更新操作
        updateById(tempUser);
        updateSpreadCountByUid(spreadUid, "add");
        if (oldSprUid > 0) {
            updateSpreadCountByUid(oldSprUid, "sub");
        }

        return Boolean.TRUE;
    }

    /**
     * 批量更新推广人
     *
     * @param request 批量更新推广人请求参数
     * @return Boolean 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editSpreadBatch(UserUpdateSpreadBatchRequest request) {
        // 验证输入参数
        if (CollUtil.isEmpty(request.getUserIds())) {
            throw new CrmebException("用户ID列表不能为空，用户ID列表: []");
        }
        if (ObjectUtil.isNull(request.getSpreadUid())) {
            throw new CrmebException("推广人ID不能为空，推广人ID: null");
        }

        // 验证推广人不能是待更新的用户之一
        if (request.getUserIds().contains(request.getSpreadUid())) {
            throw new CrmebException("推广人不能为待更新的用户之一，推广人ID: " + request.getSpreadUid() + "，用户ID列表: " + request.getUserIds());
        }

        // 使用 for 循环逐一调用 editSpread
        for (Integer userId : request.getUserIds()) {
            UserUpdateSpreadRequest singleRequest = new UserUpdateSpreadRequest();
            singleRequest.setUserId(userId);
            singleRequest.setSpreadUid(request.getSpreadUid());
            editSpread(singleRequest);
        }

        return Boolean.TRUE;
    }

    /**
     * 更新用户积分
     *
     * @param user     用户
     * @param integral 积分
     * @param type     增加add、扣减sub
     * @return 更新后的用户对象
     */
    @Override
    public Boolean updateIntegral(User user, Integer integral, String type) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + user.getUid(), () -> {
            LambdaUpdateWrapper<User> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
            if (type.equals("add")) {
                lambdaUpdateWrapper.set(User::getIntegral, user.getIntegral() + integral);
            } else {
                lambdaUpdateWrapper.set(User::getIntegral, user.getIntegral() - integral);
            }
            lambdaUpdateWrapper.eq(User::getUid, user.getUid());
            if (type.equals("sub")) {
                lambdaUpdateWrapper.apply(StrUtil.format(" integral - {} >= 0", integral));
            }
            return update(lambdaUpdateWrapper);
        });
    }

    /**
     * 清除User Group id
     *
     * @param groupId 待清除的GroupId
     */
    @Override
    public void clearGroupByGroupId(String groupId) {
        LambdaUpdateWrapper<User> upw = Wrappers.lambdaUpdate();
        upw.set(User::getGroupId, "").eq(User::getGroupId, groupId);
        update(upw);
    }

    /**
     * 更新用户
     *
     * @param userRequest 用户参数
     * @return Boolean
     */
    @Override
    public Boolean updateUser(UserUpdateRequest userRequest) {
        User tempUser = getById(userRequest.getUid());
        User user = new User();
        BeanUtils.copyProperties(userRequest, user);
        if (!tempUser.getIsPromoter() && user.getIsPromoter()) {
            user.setPromoterTime(cn.hutool.core.date.DateUtil.date());
        }
        return updateById(user);
    }

    /**
     * 根据手机号查询用户
     *
     * @param phone 用户手机号
     * @return 用户信息
     */
    @Override
    public User getByPhone(String code, String phone) {
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.eq(User::getNumberCode, code);
        lqw.eq(User::getPhone, phone);
        return userDao.selectOne(lqw);
    }

    /**
     * 后台修改用户手机号
     *
     * @param id    用户uid
     * @param phone 手机号
     * @return Boolean
     */
    @Override
    public Boolean updateUserPhone(Integer id, String phone) {
        User user = getById(id);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("对应用户不存在");
        }
        if (!PhoneNumberUtils.isPhoneNumberValid(user.getNumberCode(), phone)) {
            throw new CrmebException("手机号格式错误，请输入正确的手机号");
        }
        if (phone.equals(user.getPhone())) {
            throw new CrmebException("手机号与之前一致");
        }
        //检测当前手机号是否已经是账号
        User tempUser = getByEncryptedPhone(user.getNumberCode(), phone);
        if (ObjectUtil.isNotNull(tempUser)) {
            throw new CrmebException("此手机号码已被注册");
        }

        User newUser = new User();
        newUser.setUid(id);
        newUser.setPhone(phone);
        newUser.setEncryptedPhone(CommonUtil.md5(phone, 2));
        return this.updateInfo(newUser, user.getUid());
    }

    @Override
    public Boolean clearPhoneAll() {
        // 构建 LambdaUpdateWrapper 条件
        LambdaUpdateWrapper<User> updateWrapper = Wrappers.lambdaUpdate(User.class)
                .set(User::getPhone, ""); // 清空手机号
        return baseMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public User getByEncryptedPhone(String code, String phone) {
        String string = CommonUtil.md5(phone, 2);
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.eq(User::getNumberCode, code);
        lqw.eq(User::getEncryptedPhone, string);
        return userDao.selectOne(lqw);
    }

    @Override
    public Boolean updateUserPassword(UserUpdatePasswordRequest userUpdatePasswordRequest) {
        User user = getById(userUpdatePasswordRequest.getUid());
        if (user == null) {
            throw new CrmebException("无效的用户");
        }
        user.setPwd(CrmebUtil.encryptPassword(userUpdatePasswordRequest.getPassword(), user.getAccount()));
        return userDao.updateById(user) > 0;
    }

    @Override
    public Boolean updateUserWithdrawPassword(UserUpdatePasswordRequest userUpdatePasswordRequest) {
        User user = getById(userUpdatePasswordRequest.getUid());
        if (user == null) {
            throw new CrmebException("无效的用户");
        }
        user.setWithdrawPwd(CommonUtil.md5(userUpdatePasswordRequest.getPassword(), 2));
        return userDao.updateById(user) > 0;
    }

    @Override
    public Boolean updateUserPaymentPassword(UserUpdatePasswordRequest userUpdatePasswordRequest) {
        User user = getById(userUpdatePasswordRequest.getUid());
        if (user == null) {
            throw new CrmebException("无效的用户");
        }
        user.setPaymentPwd(CommonUtil.md5(userUpdatePasswordRequest.getPassword(), 2));
        return userDao.updateById(user) > 0;
    }

    /**
     * 根据昵称匹配用户，返回id集合
     *
     * @param nikeName 需要匹配得昵称
     * @return List
     */
    @Override
    public List<Integer> findIdListLikeName(String nikeName) {
        LambdaQueryWrapper<User> lqw = Wrappers.lambdaQuery();
        lqw.select(User::getUid);
        lqw.like(User::getNickname, nikeName);
        List<User> userList = userDao.selectList(lqw);
        if (CollUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }
        return userList.stream().map(User::getUid).collect(Collectors.toList());
    }

    /**
     * 清除对应的用户等级
     *
     * @param levelId 等级id
     */
    @Override
    public Boolean removeLevelByLevelId(Integer levelId) {
        LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
        luw.set(User::getLevel, 0);
        luw.eq(User::getLevel, levelId);
        return update(luw);
    }

    /**
     * 更新用户会员等级
     *
     * @param request request
     * @return Boolean
     */
    @Override
    public Boolean updateUserLevel(UpdateUserLevelRequest request) {
        User user = getById(request.getUid());
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }
        if (user.getLevel().equals(request.getLevelId())) {
            throw new CrmebException("用户等级与修改前相同");
        }
        SystemUserLevel userLevel = new SystemUserLevel();
        if (request.getLevelId() == null) {
            user.setLevel(0);
        } else {
            userLevel = systemUserLevelService.getByLevelId(request.getLevelId());
            if (ObjectUtil.isNull(userLevel)) {
                throw new CrmebException("系统会员等级不存在，请先配置");
            }
            user.setLevel(userLevel.getId());
        }
        // 创建用户会员等级记录
        UserLevel newLevel = new UserLevel();
        newLevel.setUid(user.getUid());
        newLevel.setLevelId(userLevel.getId());
        newLevel.setGrade(userLevel.getGrade());
        newLevel.setStatus(true);
        newLevel.setMark(StrUtil.format("尊敬的用户 {},在{}管理员调整会员等级成为{}", user.getNickname(), DateUtil.nowDateTimeStr(), userLevel.getName()));
        newLevel.setDiscount(userLevel.getDiscount());
        newLevel.setCreateTime(DateUtil.nowDateTime());
        return transactionTemplate.execute(e -> {
            updateById(user);
            userLevelService.save(newLevel);
            return Boolean.TRUE;
        });
    }

    /**
     * 获取用户总人数
     */
    @Override
    public Integer getTotalNum() {
        LambdaQueryWrapper<User> lqw = Wrappers.lambdaQuery();
        lqw.select(User::getUid);
        return userDao.selectCount(lqw);
    }

    /**
     * 根据日期段获取注册用户数量
     *
     * @param startDate 日期
     * @param endDate   日期
     * @return UserOverviewResponse
     */
    @Override
    public Integer getRegisterNumByPeriod(String startDate, String endDate) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("uid");
        wrapper.apply("date_format(create_time, '%Y-%m-%d') between {0} and {1}", startDate, endDate);
        return userDao.selectCount(wrapper);
    }

    /**
     * 获取用户性别数据
     *
     * @return List
     */
    @Override
    public List<User> getSexData() {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("sex", "count(uid) as pay_count");
        wrapper.groupBy("sex");
        return userDao.selectList(wrapper);
    }

    /**
     * 获取用户渠道数据
     *
     * @return List
     */
    @Override
    public List<User> getChannelData() {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("user_type", "count(uid) as pay_count");
        wrapper.groupBy("user_type");
        return userDao.selectList(wrapper);
    }

    /**
     * 获取所有用户的id跟地址
     *
     * @return List
     */
    @Override
    public List<User> findIdAndAddresList() {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("uid", "addres");
        return userDao.selectList(wrapper);
    }

    /**
     * 修改个人资料
     *
     * @param request 修改信息
     */
    @Override
    public Boolean editUser(UserEditRequest request) {
        User user = getInfo();
        user.setAvatar(systemAttachmentService.clearPrefix(request.getAvatar()));
        user.setNickname(request.getNickname());
        return updateById(user);
    }

    /**
     * 获取用户详情
     *
     * @param id 用户uid
     */
    @Override
    public User getInfoByUid(Integer id) {
        User user = getById(id);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }
        return user;
    }

    @Override
    public User getUserByAccount(String account) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(User::getAccount, account);
        return userDao.selectOne(queryWrapper);
    }

    @Override
    public List<User> getUserByAccount(String[] account) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(User::getAccount, account);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void calculateTotalRateAmount() {
        try {
            /*if (userBalanceInterestReportService.hasProcessedBalanceInterestToday()) {
                logger.error("今日已处理过余额利息，无法重复执行！");
                return;
            }*/
            String interestSwitch = systemConfigService.getValueByKey("interestSwitch");
            if (StringUtils.isBlank(interestSwitch) || "0".equals(interestSwitch)) {
                logger.warn(StrUtil.format("余额利息总开关已关闭，当前无法赠送利息：{}", interestSwitch));
                return;
            }
            String balanceInterestRate = systemConfigService.getValueByKey("balance_interest_rate");
            if (StringUtils.isBlank(balanceInterestRate) || "0".equals(balanceInterestRate)) {
                balanceInterestRate = "0";
            }
            logger.warn(StrUtil.format("日利息执行：当前设定的全局日利息：{}", balanceInterestRate));
            BigDecimal rate = new BigDecimal(balanceInterestRate);
            batchUserInterest(rate);
        } catch (Exception e) {
            logger.error("更新余额利息发生异常:{}", e.getMessage(), e);
        }
    }

    /**
     * 批量用户利息
     *
     * @param rate
     */
    public void batchUserInterest(BigDecimal rate) {
        int startUid = 0;//起始用户ID
        int batchCount = 500;//批次数量

        String balanceInterestUsers = systemConfigService.getValueByKey("balance_interest_users");
        List<String> userList = new ArrayList<>();
        if (StringUtils.isNotBlank(balanceInterestUsers)) {
            balanceInterestUsers = balanceInterestUsers.replaceAll(" ", "");
            String[] users = balanceInterestUsers.split("\n");
            userList = Arrays.stream(users).collect(Collectors.toList());
        }

        String balanceInterestRequestRechager = systemConfigService.getValueByKey("balance_interest_request_rechager");
        BigDecimal balanceInterestRequestRechagerAmount;
        if (StringUtils.isNotBlank(balanceInterestRequestRechager)) {
            balanceInterestRequestRechagerAmount = new BigDecimal(balanceInterestRequestRechager);
        } else {
            balanceInterestRequestRechagerAmount = BigDecimal.ZERO;
        }
        /**
         * 批次处理，每次仅处理 {@link batchCount} 个用户
         */
        while (true) {

            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = transactionManager.getTransaction(def);

            try {
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.and(qw -> qw.apply("now_money+frozen_balance >= {0}", balanceInterestRequestRechagerAmount));
                queryWrapper.eq(User::getStatus, Boolean.TRUE);
                queryWrapper.eq(User::getInterestSwitch, Boolean.TRUE);
                queryWrapper.gt(User::getUid, startUid);
                queryWrapper.orderByAsc(User::getUid);
                queryWrapper.last(" limit " + batchCount + " FOR UPDATE");
                List<User> users = userDao.selectList(queryWrapper);
                if (users == null || users.size() == 0) {
                    return;
                }
                startUid = users.get(users.size() - 1).getUid();
                updateAccountBalanceAndRecordBill(rate, users, userList);
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                logger.error("batchUserInterest发生异常：用数据回滚：{}", e.getMessage());
            }
        }
    }

    /**
     * 写入报表
     *
     * @param totalInterest
     */
    private void saveInterestReport(BigDecimal totalInterest, Integer groupId) {
        if (groupId == null) {
            groupId = 0;
        }
        //如果 totalAmount 大于 0 就写库
        if (totalInterest.compareTo(BigDecimal.ZERO) > 0) {
            LocalDate now = LocalDate.now();
            UserBalanceInterestReport one = userBalanceInterestReportService.getOne(new LambdaQueryWrapper<UserBalanceInterestReport>()
                    .eq(UserBalanceInterestReport::getCreateDate, now)
                    .eq(UserBalanceInterestReport::getGroupId, groupId)
            );
            if (one == null) {
                UserBalanceInterestReport userBalanceInterestReport = new UserBalanceInterestReport();
                userBalanceInterestReport.setTotalInterest(totalInterest);
                userBalanceInterestReport.setCreateDate(now);
                userBalanceInterestReport.setGroupId(groupId);
                userBalanceInterestReportService.save(userBalanceInterestReport);
            } else {
                BigDecimal interest = one.getTotalInterest().add(totalInterest);
                one.setTotalInterest(interest);
                userBalanceInterestReportService.updateById(one);
            }
        }
    }

    /**
     * 更新账户余额和账单记录
     *
     * @param globalRate
     * @param users
     * @param userList   过滤的用户
     * @return
     */

    public void updateAccountBalanceAndRecordBill(BigDecimal globalRate, List<User> users, List<String> userList) {
        for (User user : users) {
            try {
                BigDecimal rate = globalRate;
                if (userList.size() > 0 && userList.contains(user.getAccount())) {
                    logger.warn(StrUtil.format("账号：{}，全局已设定过滤", user.getAccount()));
                    continue;
                }
                UserInterestConfig userInterestConfig = userInterestConfigService.configInterestQuery(user.getUid());
                if (userInterestConfig != null) {
                    if (userInterestConfig.getType().equals(InterestType.CLOSE)) {
                        logger.warn(StrUtil.format("账号：{}，已设定利息不赠送", user.getAccount()));
                        continue;
                    }
                    rate = userInterestConfig.getInterest();
                } else {
                    //如果余额利息有值 并且没有配置余额利息比率
                    if (user.getBalanceInterest().compareTo(BigDecimal.ZERO) > 0) {
                        rate = user.getBalanceInterest();
                    }
                }
                //如果全局日利息和用户指定利息都是0则跳过
                if (rate.compareTo(BigDecimal.ZERO) == 0 && user.getBalanceInterest().compareTo(BigDecimal.ZERO) == 0) {
                    logger.warn(StrUtil.format("账号：{}，未设定利息", user.getAccount()));
                    continue;
                }
                //该用户当日是否已经计算过利息
                Boolean hasInterestToday = userBillService.hasInterestToday(user.getUid());
                if (hasInterestToday) {
                    logger.warn(StrUtil.format("用户ID：{}当日已经计算过利息！", user.getUid()));
                    continue;
                }
                BigDecimal lixiMoney = user.getNowMoney().add(user.getFrozenBalance());
                //使用rate计算每个会员的利息
                BigDecimal interest = lixiMoney.multiply(rate).divide(new BigDecimal("100"));
                interest = interest.setScale(2, RoundingMode.HALF_UP);
                //过滤利息小于0的用户
                if (interest.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.warn(StrUtil.format("账号：{}，利息过低，未赠送", user.getAccount()));
                    continue;
                }
                BigDecimal newMoney = user.getNowMoney().add(interest);
                // 生成UserBill
                UserBill userBill = new UserBill();
                userBill.setUid(user.getUid());
                userBill.setLinkId("0");
                userBill.setTitle("余额利息");
                userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                userBill.setNumber(interest);
                userBill.setStatus(1);
                userBill.setCreateTime(DateUtil.nowDateTime());
                userBill.setPm(1);
                userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_BALANCE_INTEREST);
                userBill.setBalance(newMoney);
                userBill.setMark(StrUtil.format("余额利息增加{}元，指定利息：{}%", interest, rate));
                try {
                    operationNowMoney(user.getUid(), interest, user.getNowMoney(), "add");
                } catch (Exception e) {
                    logger.error(StrUtil.format("operationNowMoney:{}，利息发生异常：{}", user.getUid(), e.getMessage()));
                }
                userBillService.save(userBill);
                saveInterestReport(interest, user.getGroupId());
            } catch (Exception e) {
                logger.error(StrUtil.format("用户ID:{}，利息发生异常：{}", user.getUid(), e.getMessage()));
            }
        }
    }

    @Override
    public List<User> getUserListBySpread(Integer spreadId) {
        if (spreadId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(User::getSpreadUid, spreadId);
        return userDao.selectList(queryWrapper);
    }

    @Override
    public BigDecimal getBalanceAll(String startTime, String endTime, Integer groupId) {
        QueryWrapper<User> wrapper = Wrappers.query();
        //wrapper.ge(StringUtils.isNotEmpty(startTime), "create_time", startTime);
        //wrapper.le(StringUtils.isNotEmpty(endTime), "create_time", endTime);
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        wrapper.select("sum(now_money)");
        List<Object> sumResult = userDao.selectObjs(wrapper);
        if (sumResult != null && sumResult.size() > 0 && sumResult.get(0) != null) {
            return new BigDecimal(sumResult.get(0).toString());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getFrozenBalance(String startTime, String endTime, Integer groupId) {
        QueryWrapper<User> wrapper = Wrappers.query();
        //wrapper.ge(StringUtils.isNotEmpty(startTime), "create_time", startTime);
        //wrapper.le(StringUtils.isNotEmpty(endTime), "create_time", endTime);
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        wrapper.select("sum(frozen_balance)");
        List<Object> sumResult = userDao.selectObjs(wrapper);
        if (sumResult != null && sumResult.size() > 0 && sumResult.get(0) != null) {
            return new BigDecimal(sumResult.get(0).toString());
        }
        return BigDecimal.ZERO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void resetSign() {
        try {
            // 执行更新操作
            baseMapper.updateJikaEveryDaySignNum(1);
        } catch (Exception e) {
            logger.error(StrUtil.format("resetSign:{}", e.getMessage()));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void resetDZP() {
        try {
            // 创建 UpdateWrapper，设置更新条件
            UpdateWrapper<User> updateWrapper = Wrappers.update();
            updateWrapper.gt("uid", 0);
            // 创建 User 对象，设置要更新的字段
            User updateUser = new User();
            updateUser.setDzpEveryDaySignNum(1);
            // 执行更新操作
            baseMapper.update(updateUser, updateWrapper);
        } catch (Exception e) {
            logger.error(StrUtil.format("resetDZP:{}", e.getMessage()));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Override
    public Boolean statisticalWithdrawal(Integer userId, BigDecimal amount) {
        User user = getInfoByUid(userId);
        if (user != null && amount.compareTo(BigDecimal.ZERO) > 0) {
            user.setWithdrawAmount(amount.add(user.getWithdrawAmount()));
            baseMapper.updateById(user);
        }
        return true;
    }

    @Override
    public User getUserByInvitationCode(String invitationCode) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(User::getInvitationCode, invitationCode);
        lambdaQueryWrapper.last(" limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public List<User> getUserByUserIds(List<Integer> userIds) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(User::getUid, userIds);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Boolean updateInfo(User user, Integer userId) {
        UpdateWrapper<User> updateWrapper = Wrappers.update();
        updateWrapper.eq("uid", userId);
        // 执行更新操作
        return this.update(user, updateWrapper);
    }

    // 悲观锁查询方法
    @Override
    public User getByIdWithLock(Integer userId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUid, userId);
        queryWrapper.last("FOR UPDATE"); // 添加 FOR UPDATE 锁定
        return userDao.selectOne(queryWrapper);
    }


    @Override
    public BigDecimal getFirstTotalAmount(String startTime, String endTime, Integer groupId) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        wrapper.eq("recharge_count", 1);
        wrapper.select("sum(recharge_amount)");
        List<Object> sumResult = userDao.selectObjs(wrapper);
        if (sumResult != null && sumResult.size() > 0 && sumResult.get(0) != null) {
            return new BigDecimal(sumResult.get(0).toString());
        }
        return BigDecimal.ZERO;
    }
    //不能用时间判断
    @Override
    public Integer getFirstTotalUserNum(String startTime, String endTime, Integer groupId) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("uid");
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        wrapper.eq("recharge_count", 1);
        return userDao.selectCount(wrapper);
    }
    //不能用时间判断
    @Override
    public List<Integer> getFirstRechargeUserId(Integer count) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(User::getRechargeCount, count);
        lambdaQueryWrapper.select(User::getUid);
        List<User> userList = baseMapper.selectList(lambdaQueryWrapper);
        return userList.stream().map(User::getUid).collect(Collectors.toList());
    }

    /**
     * 根据日期获取注册用户数量
     *
     * @param date 日期
     * @return Integer
     */
    @Override
    public Integer getRegisterNumByDate(String date) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("uid");
        wrapper.apply("date_format(create_time, '%Y-%m-%d') = {0}", date);
        return userDao.selectCount(wrapper);
    }

    @Override
    public Integer getRegisterNumByDate(String startTime, String endTime, Integer groupId) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("uid");
        wrapper.ge(StringUtils.isNotEmpty(startTime), "create_time", startTime);
        wrapper.le(StringUtils.isNotEmpty(endTime), "create_time", endTime);
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        return userDao.selectCount(wrapper);
    }

    /**
     * 更新用户等级
     *
     * @param uid     用户id
     * @param levelId 会员等级id
     * @return Boolean
     */
    private Boolean updateLevel(Integer uid, Integer levelId) {
        LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
        luw.set(User::getLevel, levelId);
        luw.eq(User::getUid, uid);
        return update(luw);
    }
}
