package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.LiveSensitive;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.LiveSensitiveSearchRequest;
import com.zbkj.service.dao.LiveSensitiveDao;
import com.zbkj.service.service.LiveSensitiveRecordService;
import com.zbkj.service.service.LiveSensitiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


/**
 * 直播间敏感词 接口实现类
 */

@Service
public class LiveSensitiveServiceImpl extends ServiceImpl<LiveSensitiveDao, LiveSensitive> implements LiveSensitiveService {

    @Autowired
    private LiveSensitiveRecordService liveSensitiveRecordService;
    /**
     * LiveSensitive列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<LiveSensitive> getList(LiveSensitiveSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 LiveSensitive 类的多条件查询
        LambdaQueryWrapper<LiveSensitive> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getWords()),LiveSensitive::getWords,request.getWords());
        lambdaQueryWrapper.orderByAsc(LiveSensitive::getId);
        List<LiveSensitive> liveSensitives = baseMapper.selectList(lambdaQueryWrapper);
        for (LiveSensitive liveSensitive : liveSensitives) {
            liveSensitive.setHit(liveSensitiveRecordService.hitCount(liveSensitive.getId(),request.getDays()));
        }
        return CommonPage.copyPageInfo(startPage,liveSensitives);
    }

    @Override
    public LiveSensitive getByWords(String word) {
        LambdaQueryWrapper<LiveSensitive> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LiveSensitive::getWords,word);
        lambdaQueryWrapper.last(" limit 1");
        LiveSensitive liveSensitive = baseMapper.selectOne(lambdaQueryWrapper);
        return liveSensitive;
    }

}
