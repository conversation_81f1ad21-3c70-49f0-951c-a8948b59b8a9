package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.UserInterestConfig;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserInterestConfigRequest;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.UserInterestConfigDao;
import com.zbkj.service.service.UserInterestConfigService;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 用户利息配置表 接口实现类
 */

@Service
public class UserInterestConfigServiceImpl extends ServiceImpl<UserInterestConfigDao, UserInterestConfig> implements UserInterestConfigService {

    @Autowired
    private UserService userService;
    /**
     * UserInterestConfig列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserInterestConfig> getList(UserInterestConfigRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserInterestConfig 类的多条件查询
        LambdaQueryWrapper<UserInterestConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(request.getAccount())){
            User user = userService.getUserByAccount(request.getAccount());
            if (user != null){
                request.setUid(user.getUid());
            }
        }
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUid()),UserInterestConfig::getUserId,request.getUid());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()),UserInterestConfig::getStatus,request.getStatus());
        if (StringUtils.isNotBlank(request.getDateLimit())){
            DateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.ge(UserInterestConfig::getStartDate,dateLimit.getStartTime());
            lambdaQueryWrapper.le(UserInterestConfig::getEndDate,dateLimit.getEndTime());
        }
        lambdaQueryWrapper.orderByDesc(UserInterestConfig::getCreateTime,UserInterestConfig::getUserId);
        List<UserInterestConfig> userInterestConfigs = baseMapper.selectList(lambdaQueryWrapper);

        List<Integer> integerList = userInterestConfigs.stream().map(UserInterestConfig::getUserId).collect(Collectors.toList());
        List<User> userListById = List.of();
        if (integerList.size() > 0){
            userListById = userService.getUserListById(integerList);
        }

        Map<Integer, User> integerUserMap = userListById.stream().collect(Collectors.toMap(User::getUid, Function.identity()));
        userInterestConfigs.stream().map(userInterestConfig -> {
            User user = integerUserMap.get(userInterestConfig.getUserId());
            if (user != null){
                userInterestConfig.setAccount(user.getAccount());
            }
            return userInterestConfig;
        }).collect(Collectors.toList());
        return CommonPage.copyPageInfo(startPage,userInterestConfigs);
    }

    @Override
    public UserInterestConfig configInterestQuery(Integer userId) {
        LambdaQueryWrapper<UserInterestConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserInterestConfig::getStatus,true);
        String nowedDate = DateUtil.nowDate(Constants.DATE_FORMAT_DATE);
        lambdaQueryWrapper.le(UserInterestConfig::getStartDate,nowedDate);
        lambdaQueryWrapper.ge(UserInterestConfig::getEndDate,nowedDate);
        lambdaQueryWrapper.eq(UserInterestConfig::getUserId,userId);
        lambdaQueryWrapper.last(" limit 1");
        lambdaQueryWrapper.orderByDesc(UserInterestConfig::getCreateTime);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

}
