package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.qcloud.cos.utils.Md5Utils;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.BroBroadcastingStatus;
import com.zbkj.common.enums.LiveRecordType;
import com.zbkj.common.enums.LiveSystemMsgType;
import com.zbkj.common.enums.RoomStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.user.User;
import com.zbkj.common.request.AnchorStartBroadcastRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.RoomLikeRequest;
import com.zbkj.common.response.*;
import com.zbkj.common.token.AnchorTokenComponent;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.IdUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.MerchantLiveRecordStatistic;
import com.zbkj.service.service.*;
import com.zbkj.service.service.biz.TencentIMSDK;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class LiveServiceImpl implements LiveService {

    @Value("${crmeb.live-push-stream}")
    private String pushStream;
    @Value("${crmeb.live-push-stream-key}")
    private String pushStreamKey;

    @Value("${crmeb.live-pull-stream}")
    private String pullStream;
    @Value("${crmeb.live-pull-stream-key}")
    private String pullStreamKey;

    @Resource
    private TencentIMSDK tencentIMSDK;

    @Resource
    private LiveRoomService liveRoomService;

    @Resource
    private LiveRecordService liveRecordService;

    @Resource
    private SystemAdminService systemAdminService;

    @Resource
    private AnchorTokenComponent anchorTokenComponent;

    @Resource
    private MerchantShopService merchantShopService;

    @Resource
    private UserService userService;

    @Resource
    private LiveLeaderboardService liveLeaderboardService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserLivePlayRecordService userLivePlayRecordService;


    @Override
    public AnchorLoginResponse login(String account, String password) {
        SystemAdmin systemAdmin = systemAdminService.selectUserByUserName(account);
        if (systemAdmin == null) {
            throw new CrmebException("账号或密码错误");
        }
        String encryptPassword = CrmebUtil.encryptPassword(password, systemAdmin.getAccount());
        if (!systemAdmin.getPwd().equals(encryptPassword)) {
            throw new CrmebException("账号或者密码不正确");
        }
        Boolean isMerchant = systemAdmin.getMerchant();
        MerchantShop merchantShop = null;
        if (isMerchant) {
            merchantShop = merchantShopService.getByMerchant(systemAdmin.getId());
            if (merchantShop == null || !merchantShop.getShopStatus()) {
                throw new CrmebException("商户暂未绑定店铺或店铺已关闭");
            }
        }

        int merchant = isMerchant ? systemAdmin.getId() : 0;
        String token = anchorTokenComponent.createToken(merchant);
        LiveRoom liveRoom = liveRoomService.findByMerchant(merchant);
        if (liveRoom == null) {
            //创建直播间
            liveRoom = new LiveRoom();
            liveRoom.setRoomId(IdUtil.nextId());
            liveRoom.setMerchant(merchantShop.getMerchant());
            liveRoom.setRoomName(merchantShop.getShopName());
            liveRoom.setRoomAvatar(merchantShop.getShopLogo());
            liveRoom.setBroadcastingStatus(BroBroadcastingStatus.ENDED);
            liveRoom.setRoomStatus(RoomStatus.NORMAL);
            boolean saved = liveRoomService.save(liveRoom);
            tencentIMSDK.createAVChatRoom(liveRoom.getRoomId(), liveRoom.getRoomName());
            if (!saved){
                throw new CrmebException("直播间开通失败，请重试！");
            }
        }
        AnchorLoginResponse response = new AnchorLoginResponse();
        response.setToken(token);
        response.setRoomId(liveRoom.getRoomId());
        response.setRoomName(liveRoom.getRoomName());
        response.setMerchant(liveRoom.getMerchant());
        response.setRoomAvatar(liveRoom.getRoomAvatar());
        response.setMerchantShop(merchantShop);
        response.setImUsersig(tencentIMSDK.getUsersig(liveRoom.getRoomId().toString()));
        return response;
    }

    @Override
    public boolean logout(Integer merchant) {
        LiveRoom liveRoom = liveRoomService.findByMerchant(merchant);
        if (liveRoom == null) {
            return false;
        }
        //还有在播记录，强制关播
        if (liveRoom.getBroadcastingStatus() == BroBroadcastingStatus.STARTED) {
            liveRoomService.forceStop(liveRoom.getRoomId());
        }
        return true;
    }

    @Override
    public AnchorHomeResponse home(Integer merchant) {
        LiveRoom liveRoom = liveRoomService.findByMerchant(merchant);
        if (liveRoom == null) {
            throw new CrmebException("直播间不存在请联系系统管理员");
        }
        AnchorHomeResponse response = new AnchorHomeResponse();
        response.setRoomId(liveRoom.getRoomId());
        response.setRoomName(liveRoom.getRoomName());
        response.setMerchant(liveRoom.getMerchant());
        response.setRoomAvatar(liveRoom.getRoomAvatar());
        response.setBroadcastingStatus(liveRoom.getBroadcastingStatus());
        response.setRoomStatus(liveRoom.getRoomStatus());
        String hexTime = Long.toHexString(System.currentTimeMillis() / 1000 + 86400L);
        response.setStreamUrl(String.format(pushStream, liveRoom.getRoomId(), Md5Utils.md5Hex(pushStreamKey + liveRoom.getRoomId() + hexTime), hexTime));
        MerchantLiveRecordStatistic statistic = liveRecordService.statisticByMerchant(liveRoom.getMerchant());
        response.setLiveTimes(statistic.getLiveTimes());
        response.setSaleAmount(statistic.getSaleAmount());
        response.setSaleProduct(statistic.getSaleProduct());
        return response;
    }

    @Override
    public LiveRecordResponse startBroadcast(AnchorStartBroadcastRequest param, Integer merchant) {
        LiveRoom liveRoom = liveRoomService.findByMerchant(merchant);
        if (liveRoom == null) {
            throw new CrmebException("直播间不存在请联系系统管理员");
        }
        if (liveRoom.getRoomStatus() == RoomStatus.BANNED) {
            liveRoomService.forceStop(liveRoom.getRoomId());
            throw new CrmebException("直播间已封禁，请联系系统管理员");
        }

        //是否存在未关播的记录
        LiveRecord startedRecord = liveRecordService.findLastetStartedByRoomId(liveRoom.getRoomId());
        if (startedRecord == null) {
            startedRecord = new LiveRecord();
            startedRecord.setId(IdUtil.nextId());
            startedRecord.setRoomId(liveRoom.getRoomId());
            startedRecord.setMerchant(liveRoom.getMerchant());
            startedRecord.setRoomName(liveRoom.getRoomName());
            startedRecord.setTitle(param.getTitle());
            startedRecord.setRoomCover(param.getRoomCover());
            startedRecord.setBroadcastingStatus(BroBroadcastingStatus.STARTED);
            startedRecord.setStartTime(new Date());
            startedRecord.setSaleAmount(BigDecimal.ZERO);
            startedRecord.setSaleProduct(0);
            liveRecordService.save(startedRecord);
        } else {
            startedRecord.setTitle(param.getTitle());
            startedRecord.setRoomCover(param.getRoomCover());
            liveRecordService.updateById(startedRecord);
        }

        liveRoomService.updateBroadcastStatus(liveRoom.getRoomId(), BroBroadcastingStatus.STARTED, param.getStoreWindow());

        LiveRecordResponse response = new LiveRecordResponse();
        response.setId(startedRecord.getId());
        response.setRoomId(startedRecord.getRoomId());
        response.setMerchant(startedRecord.getMerchant());
        response.setRoomName(startedRecord.getRoomName());
        response.setTitle(startedRecord.getTitle());
        response.setRoomCover(startedRecord.getRoomCover());
        response.setBroadcastingStatus(startedRecord.getBroadcastingStatus());
        response.setStoreWindow(param.getStoreWindow());
        response.setStartTime(startedRecord.getStartTime());
        response.setEndTime(startedRecord.getEndTime());
        response.setRoomAvatar(liveRoom.getRoomAvatar());

        return response;
    }

    @Override
    public boolean endBroadcast(Integer merchant) {
        LiveRoom liveRoom = liveRoomService.findByMerchant(merchant);
        if (liveRoom == null) {
            throw new CrmebException("直播间不存在请联系系统管理员");
        }
        if (liveRoom.getRoomStatus() == RoomStatus.BANNED) {
            liveRoomService.forceStop(liveRoom.getRoomId());
            throw new CrmebException("直播间已封禁，请联系系统管理员");
        }
        LiveRecord startedRecord = liveRecordService.findLastetStartedByRoomId(liveRoom.getRoomId());
        if (startedRecord != null) {
            liveRoomService.forceStop(liveRoom.getRoomId());
            //删除排名信息和观看人次
            liveLeaderboardService.deleteAll(startedRecord.getRoomId());
            //下播通知
            tencentIMSDK.sendGroupSystemNotification(startedRecord.getRoomId(), LiveSystemMsgType.ANCHOR_OFFLINE);
        }
        return true;
    }

    @Override
    public boolean storeWindowSwitch(Integer merchant) {
        LiveRoom liveRoom = liveRoomService.findByMerchant(merchant);
        if (liveRoom == null) {
            throw new CrmebException("直播间不存在请联系系统管理员");
        }
        liveRoom.setStoreWindow(liveRoom.getStoreWindow() == false);
        liveRoomService.updateLiveRoom(liveRoom);
        //发送消息通知
        tencentIMSDK.sendGroupSystemNotification(liveRoom.getRoomId(), LiveSystemMsgType.SHOWCASE_SWITCH, liveRoom.getStoreWindow().toString());
        return liveRoom.getStoreWindow();
    }

    public PageInfo<LiveRecord> roomList(PageParamRequest request) {
        return liveRecordService.getLiveStreamsList(request, pullStream, pullStreamKey);
    }

    @Override
    public List<LiveRecord> loadLive(List<Long> roomList, Integer count) {
        if (count == null) {
            count = 10;
        }
        LambdaQueryWrapper<LiveRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LiveRecord::getBroadcastingStatus, BroBroadcastingStatus.STARTED);
        if (roomList != null && roomList.size() > 0) {
            lambdaQueryWrapper.notIn(LiveRecord::getRoomId, roomList);
        }
        lambdaQueryWrapper.last(" order by rand() limit " + count);
        List<LiveRecord> list = liveRecordService.list(lambdaQueryWrapper);
        if (list.size() == 0) {
            throw new CrmebException("没有正在直播的直播间~");
        }
        String hexTime = Long.toHexString(System.currentTimeMillis() / 1000 + 86400L);
        list.forEach(liveRecord -> {
            MerchantShop merchantShop = merchantShopService.getByMerchant(liveRecord.getMerchant());
            if (merchantShop != null) {
                liveRecord.setShopLogo(merchantShop.getShopLogo());
            }
            LiveRoom liveRoom = liveRoomService.findByMerchant(liveRecord.getMerchant());
            if (liveRoom != null) {
                liveRecord.setStoreWindow(liveRoom.getStoreWindow());
            }
            liveRecord.setPullStreamUrl(String.format(pullStream, "rtmp", liveRecord.getRoomId(), "", Md5Utils.md5Hex(pullStreamKey + liveRecord.getRoomId() + hexTime), hexTime));
            liveRecord.setM3u8StreamUrl(String.format(pullStream, "https", liveRecord.getRoomId(), ".m3u8", Md5Utils.md5Hex(pullStreamKey + liveRecord.getRoomId() + hexTime), hexTime));
            liveRecord.setLikeCount(liveLeaderboardService.getRoomWatchCount(liveRecord.getRoomId()));
        });
        return list;
    }

    /**
     * 重载 加载官方直播间
     * @param merchant
     * @param count
     * @return
     */
    public List<LiveRecord> loadLive(Integer merchant, Integer count) {
        if (count == null) {
            count = 10;
        }
        LambdaQueryWrapper<LiveRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LiveRecord::getBroadcastingStatus, BroBroadcastingStatus.STARTED);
        if (merchant != null) {
            lambdaQueryWrapper.eq(LiveRecord::getMerchant, merchant);
        }
        lambdaQueryWrapper.last(" order by rand() limit " + count);
        List<LiveRecord> list = liveRecordService.list(lambdaQueryWrapper);
        if (list.size() == 0) {
            throw new CrmebException("没有正在直播的直播间~");
        }
        String hexTime = Long.toHexString(System.currentTimeMillis() / 1000 + 86400L);
        list.forEach(liveRecord -> {
            MerchantShop merchantShop = merchantShopService.getByMerchant(liveRecord.getMerchant());
            if (merchantShop != null) {
                liveRecord.setShopLogo(merchantShop.getShopLogo());
            }
            LiveRoom liveRoom = liveRoomService.findByMerchant(liveRecord.getMerchant());
            if (liveRoom != null) {
                liveRecord.setStoreWindow(liveRoom.getStoreWindow());
            }
            liveRecord.setPullStreamUrl(String.format(pullStream, "rtmp", liveRecord.getRoomId(), "", Md5Utils.md5Hex(pullStreamKey + liveRecord.getRoomId() + hexTime), hexTime));
            liveRecord.setM3u8StreamUrl(String.format(pullStream, "https", liveRecord.getRoomId(), ".m3u8", Md5Utils.md5Hex(pullStreamKey + liveRecord.getRoomId() + hexTime), hexTime));
            liveRecord.setLikeCount(liveLeaderboardService.getRoomWatchCount(liveRecord.getRoomId()));
        });
        return list;
    }

    @Override
    public LiveRecord officialRoom() {
        List<LiveRecord> liveRecordList = loadLive(0, 1);
        if (liveRecordList!=null || liveRecordList.size() > 0){
            return liveRecordList.get(0);
        }
        return null;
    }

    @Override
    public LivePlayRecordResponse enterRoom(Long roomId) {
        User info = userService.getInfo();
        String roomKey = Constants.LIVE_ROOM_LIST + roomId;
        String watchKey = Constants.LIVE_ROOM_WATCH + roomId;

        UserLiveRoomResponse userLiveRoomResponse = new UserLiveRoomResponse();
        BeanUtils.copyProperties(info, userLiveRoomResponse);
        // 用户进入房间
        liveLeaderboardService.addUserToRoom(roomKey, userLiveRoomResponse, 0);
        // 增加观看人数
        redisUtil.incrAndCreate(watchKey);
        RoomInfoResponse roomInfoResponse = getRoomInfoResponse(roomId, info);
        //发送系统消息
        tencentIMSDK.sendGroupSystemNotification(roomId, LiveSystemMsgType.ENTER_ROOM, JSON.toJSONString(roomInfoResponse));

        //获取直播间记录
        userLivePlayRecordService.addRecord(info.getUid(), roomId, LiveRecordType.next);
        Long previousPlayRecord = userLivePlayRecordService.getPreviousPlayRecord(info.getUid(), roomId);
        Long nextPlayRecord = userLivePlayRecordService.getNextPlayRecord(info.getUid(), roomId);
        LivePlayRecordResponse livePlayRecordResponse = new LivePlayRecordResponse();
        //上一个直播对象
        if (previousPlayRecord == null) {
            LiveRecord previousLiveRecord = loadAndSetPlayRecord(info.getUid(), LiveRecordType.previous);
            livePlayRecordResponse.setPrevious(previousLiveRecord);
        } else {
            livePlayRecordResponse.setPrevious(liveRecordService.findLastetStartedByRoomId(previousPlayRecord));
        }
        //下一个直播对象
        if (nextPlayRecord == null) {
            LiveRecord nextLiveRecord = loadAndSetPlayRecord(info.getUid(), LiveRecordType.next);
            livePlayRecordResponse.setNext(nextLiveRecord);
        } else {
            livePlayRecordResponse.setNext(liveRecordService.findLastetStartedByRoomId(nextPlayRecord));
        }
        livePlayRecordResponse.setRoomId(roomId);
        return livePlayRecordResponse;
    }

    /**
     * 房间信息
     *
     * @param roomId
     * @return
     */
    private RoomInfoResponse getRoomInfoResponse(Long roomId, User info) {

        String roomKey = Constants.LIVE_ROOM_LIST + roomId;

        RoomInfoResponse roomInfoResponse = new RoomInfoResponse();
        roomInfoResponse.setRoomId(roomId);
        roomInfoResponse.setAccount(info.getAccount());
        roomInfoResponse.setNickName(info.getNickname());

        // 获取排行榜列表 前三用户
        List<UserLiveRoomResponse> topUser = liveLeaderboardService.getTopUser(roomKey, 0, 2);
        roomInfoResponse.setUserLiveRoomResponseList(topUser);
        // 设置总用户数和观看人数
        roomInfoResponse.setTotalUserNum(liveLeaderboardService.getUserCount(roomKey));
        roomInfoResponse.setTotalWatchNum(liveLeaderboardService.getRoomWatchCount(roomId));
        return roomInfoResponse;
    }

    @Override
    public void leaveRoom(Long roomId) {
        User info = userService.getInfo();
        String roomKey = Constants.LIVE_ROOM_LIST + roomId;
        UserLiveRoomResponse userLiveRoomResponse = new UserLiveRoomResponse();
        BeanUtils.copyProperties(info, userLiveRoomResponse);
        liveLeaderboardService.deleteUser(roomKey, userLiveRoomResponse);
        RoomInfoResponse roomInfoResponse = getRoomInfoResponse(roomId, info);
        //消息消息推送
        tencentIMSDK.sendGroupSystemNotification(roomId, LiveSystemMsgType.LEAVE_ROOM, JSON.toJSONString(roomInfoResponse));
    }

    @Override
    public void roomLike(RoomLikeRequest request) {
        Long roomId = request.getRoomId();
        User info = userService.getInfo();
        String roomKey = Constants.LIVE_ROOM_LIST + roomId;
        UserLiveRoomResponse userLiveRoomResponse = new UserLiveRoomResponse();
        BeanUtils.copyProperties(info, userLiveRoomResponse);
        liveLeaderboardService.updateMemberLikes(roomKey, userLiveRoomResponse, request.getCount());
        RoomInfoResponse roomInfoResponse = getRoomInfoResponse(roomId, info);
        roomInfoResponse.setCount(request.getCount());
        //消息消息推送
        tencentIMSDK.sendGroupSystemNotification(roomId, LiveSystemMsgType.LIKE, JSON.toJSONString(roomInfoResponse));

    }

    @Override
    public void quit(Long roomId) {
        User info = userService.getInfo();
        userLivePlayRecordService.deleteRecord(info.getUid());
        leaveRoom(roomId);
    }

    private LiveRecord loadAndSetPlayRecord(Integer uid, LiveRecordType recordType) {
        List<Long> allPlayRecords = userLivePlayRecordService.getAllPlayRecords(uid);
        List<LiveRecord> liveRecordList = null;
        try {
            liveRecordList = loadLive(allPlayRecords, 1);
        } catch (Exception exception) {

        }
        //没有更多的直播了
        if (liveRecordList == null || liveRecordList.isEmpty()) {
            // 删除观看记录，重新随机获取
            userLivePlayRecordService.deleteRecord(uid);
            try {
                liveRecordList = loadLive(List.of(), 1);
            } catch (Exception exception) {

            }
        }
        if (liveRecordList != null && liveRecordList.size() > 0) {
            LiveRecord liveRecord = liveRecordList.get(0);
            userLivePlayRecordService.addRecord(uid, liveRecord.getRoomId(), recordType);
            return liveRecord;
        }
        return null;
    }
}
