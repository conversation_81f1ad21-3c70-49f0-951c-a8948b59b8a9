package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.enums.LoginMethod;
import com.zbkj.common.model.UserLoginLogs;
import com.zbkj.common.request.UserLoginLogsSearchRequest;
import org.aspectj.lang.ProceedingJoinPoint;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 用户登录日志表 业务接口
 */
public interface UserLoginLogsService extends IService<UserLoginLogs> {

    /**
     * UserLoginLogs 列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<UserLoginLogs> getList(UserLoginLogsSearchRequest request);

    /**
     * 获取最后一条登录信息
     * @param userId
     * @return
     */
    UserLoginLogs getLastLoginInfo(Integer userId);

    /**
     * 获取登录数量
     * @return
     */
    Integer getLoginNum(String startTime,String endTime,Integer groupId);

    /**
     * 登录日志记录
     * @param joinPoint
     * @param loginMethod
     * @return
     */
    Object saveLoginLogs(ProceedingJoinPoint joinPoint, LoginMethod loginMethod);
}

