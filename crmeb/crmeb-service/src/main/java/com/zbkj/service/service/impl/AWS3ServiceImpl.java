package com.zbkj.service.service.impl;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.vo.CloudVo;
import com.zbkj.common.vo.FileResultVo;
import com.zbkj.service.service.AWS3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.UUID;


/**
 * CosServiceImpl 同步到云服务

 */
@Service
public class AWS3ServiceImpl implements AWS3Service {


    private static final Logger logger = LoggerFactory.getLogger(AWS3ServiceImpl.class);

    @Override
    public String uploadFile(CloudVo cloudVo, MultipartFile file,String path) {
        logger.info("上传文件开始：" + file.getOriginalFilename());
        String fileName = generateFileName(file.getOriginalFilename());
        fileName = path + fileName;
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());

            PutObjectRequest putObjectRequest = new PutObjectRequest(cloudVo.getBucketName(), fileName, file.getInputStream(), metadata);

            AWSCredentials awsCredentials = new BasicAWSCredentials(cloudVo.getAccessKey(), cloudVo.getSecretKey());
            AmazonS3Client amazonS3Client = new AmazonS3Client(awsCredentials);
            amazonS3Client.putObject(putObjectRequest);
            amazonS3Client.getUrl(cloudVo.getBucketName(), fileName);
            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
            throw new CrmebException("图片上传失败");
        }
    }

    private String generateFileName(String originalFileName) {
        String extension = originalFileName.substring(originalFileName.lastIndexOf("."));
        return UUID.randomUUID().toString() + extension;
    }
}

