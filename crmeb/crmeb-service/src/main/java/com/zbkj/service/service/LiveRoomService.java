package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.enums.BroBroadcastingStatus;
import com.zbkj.common.enums.RoomStatus;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.request.PageParamRequest;

import java.util.List;

public interface LiveRoomService extends IService<LiveRoom> {
    void init();

    LiveRoom findByMerchant(Integer merchant);

    boolean deleteByMerchant(Integer merchant);

    List<LiveRoom> getList(LiveRoom liveRoom, PageParamRequest pageParamRequest);

    boolean updateRoomStatus(Long roomId, RoomStatus status);

    boolean updateBroadcastStatus(Long roomId, BroBroadcastingStatus broadcastingStatus, Boolean storeWindow);

    boolean forceStop(Long roomId);

    boolean updateLiveRoom(LiveRoom liveRoom);
}
