package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.ActivityType;
import com.zbkj.common.model.ActivitySwitch;
import com.zbkj.common.model.SystemGroupDataDZPModel;
import com.zbkj.common.model.SystemGroupDataHBModel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserActivityRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserActivityRecordRequest;
import com.zbkj.service.dao.UserActivityRecordDao;
import com.zbkj.service.service.SystemConfigService;
import com.zbkj.service.service.SystemGroupDataService;
import com.zbkj.service.service.UserActivityRecordService;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户活动记录表 接口实现类
 */

@Service
public class UserActivityRecordServiceImpl extends ServiceImpl<UserActivityRecordDao, UserActivityRecord> implements UserActivityRecordService {

    @Autowired
    private UserService userService;

    @Autowired
    private SystemGroupDataService systemGroupDataService;

    @Autowired
    private SystemConfigService configService;

    /**
     * UserActivityRecord列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserActivityRecord> getList(UserActivityRecordRequest request) {
        List<SystemGroupDataDZPModel> entityByGid = systemGroupDataService.getEntityByGid(Constants.GROUP_DATA_ID_USER_DZP_CONFIG, SystemGroupDataDZPModel.class);
        Map<Integer, SystemGroupDataDZPModel> groupDataDZPModelMap = entityByGid.stream().collect(Collectors.toMap(by -> by.getId(), Function.identity()));

        List<SystemGroupDataHBModel> groupDataServiceEntityByGid = systemGroupDataService.getEntityByGid(Constants.GROUP_DATA_ID_USER_HB_CONFIG, SystemGroupDataHBModel.class);
        Map<Integer, SystemGroupDataHBModel> groupDataHBModelMap = groupDataServiceEntityByGid.stream().collect(Collectors.toMap(by -> by.getId(), Function.identity()));

        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserActivityRecord 类的多条件查询
        LambdaQueryWrapper<UserActivityRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(request.getAccount())){
            User user = userService.getUserByAccount(request.getAccount());
            if (user!=null){
                request.setUserId(user.getUid());
            }
        }
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUserId()),UserActivityRecord::getUserId,request.getUserId());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()),UserActivityRecord::getStatus,request.getStatus());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getType()),UserActivityRecord::getType,request.getType());
        lambdaQueryWrapper.orderByDesc(UserActivityRecord::getCreateTime,UserActivityRecord::getId);
        List<UserActivityRecord> userActivityRecordList = baseMapper.selectList(lambdaQueryWrapper);
        return CommonPage.copyPageInfo(startPage,userActivityRecordList.stream().map(userActivityRecord -> {
            User infoByUid = userService.getInfoByUid(userActivityRecord.getUserId());
            if (infoByUid != null){
                userActivityRecord.setAccount(infoByUid.getAccount());
            }
            return userActivityRecord;
        }).collect(Collectors.toList()));
    }

    @Override
    public ActivitySwitch activityStatus() {
        String jikaSwitch = configService.getValueByKey("jikaSwitch");
        String taskSwitch = configService.getValueByKey("taskSwitch");
        String dzpSwitch = configService.getValueByKey("dzpSwitch");
        String hbSwitch = configService.getValueByKey("hbSwitch");
        String jikaSwitchMenu = configService.getValueByKey("jikaSwitchMenu");
        String taskSwitchMenu = configService.getValueByKey("taskSwitchMenu");
        String dzpSwitchMenu = configService.getValueByKey("dzpSwitchMenu");
        String hbSwitchMenu = configService.getValueByKey("hbSwitchMenu");
        ActivitySwitch activitySwitch = new ActivitySwitch();
        activitySwitch.setJikaSwitch("1".equals(jikaSwitch));
        activitySwitch.setTaskSwitch("1".equals(taskSwitch));
        activitySwitch.setDzpSwitch("1".equals(dzpSwitch));
        activitySwitch.setHbSwitch("1".equals(hbSwitch));
        activitySwitch.setJikaSwitchMenu("1".equals(jikaSwitchMenu));
        activitySwitch.setTaskSwitchMenu("1".equals(taskSwitchMenu));
        activitySwitch.setDzpSwitchMenu("1".equals(dzpSwitchMenu));
        activitySwitch.setHbSwitchMenu("1".equals(hbSwitchMenu));
        return activitySwitch;
    }

    @Override
    public BigDecimal hongBaoTotal(String startTime, String endTime,Integer groupId) {
        QueryWrapper<UserActivityRecord> wrapper = Wrappers.query();
        wrapper.ge(StringUtils.isNotBlank(startTime), "create_time", startTime)
                .le(StringUtils.isNotBlank(endTime), "create_time", endTime)
                .eq(Objects.nonNull(groupId), "group_id", groupId)
                .eq("type", ActivityType.RED_RNVELOPE)
                .select("sum(amount)");
        List<Object> list = baseMapper.selectObjs(wrapper);
        if (CollUtil.isNotEmpty(list) && list.size() > 0 && list.get(0) != null) {
            return new BigDecimal(list.get(0).toString());
        }
        return BigDecimal.ZERO;
    }
}
