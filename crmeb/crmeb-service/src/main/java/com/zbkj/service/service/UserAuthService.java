package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.AliyunFaceVeryifyConfig;
import com.zbkj.common.model.user.UserAuth;
import com.zbkj.common.request.UserAuthSearchRequest;
import com.zbkj.common.request.UserAuthSubmitRequest;
import com.zbkj.common.response.UserAuthResponse;
import com.zbkj.common.response.UserAuthSubmitResponse;

/**
 * 用户实名认证 业务接口
 */
public interface UserAuthService extends IService<UserAuth> {
    /**
     * UserAuth 列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<UserAuthResponse> getList(UserAuthSearchRequest request);

    /**
     * 获取阿里云人脸识别配置
     * @return
     */
    AliyunFaceVeryifyConfig getAliyunFaceVeryifyConfig();

    /**
     * 根据certId获取提交的配置信息
     * @param certId
     * @return
     */
    UserAuth getUserAuthByCertId(String certId,Integer userId);

    /**
     * 根据证件号码获取认证信息
     * @param certNo
     * @return
     */
    UserAuth findApprovedUserAuthByCertNoAndName(String certNo, String name);

    /**
     * 获取状态
     * @param userId
     * @return
     */
    UserAuth getState(Integer userId);

    /**
     * 重置状态超过10分的状态重置为失败
     * @return
     */
    boolean resetAuthenticationStatus();

    /**
     * 清理实名记录
     * @return
     */
    boolean clean();

    UserAuthSubmitResponse submitAuth(UserAuthSubmitRequest request, String clientIp);

}

