package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.NumberPrefixes;
import com.zbkj.common.request.PageParamRequest;

import java.util.List;

/**
 * SystemCityService 接口

 */
public interface NumberPrefixesService extends IService<NumberPrefixes> {
    /**
     * 冠号列表
     * @param numberPrefixes 请求参数
     * @param pageParamRequest 分页参数
     * @return List
     */
    List<NumberPrefixes> getList(NumberPrefixes numberPrefixes, PageParamRequest pageParamRequest);

    /**
     * 初始化缓存
     * @return
     */
    List<NumberPrefixes> init();

    /**
     * 提供前端商铺接口数据
     * @return
     */
    List<NumberPrefixes> getList();

    /**
     * 检查冠号是否存在
     * @param code
     * @return
     */
    Boolean hasPrefix(String code);

    /**
     * 根据冠号查询信息
     * @param code
     * @return
     */
    NumberPrefixes getPrefixByCode(String code);
}
