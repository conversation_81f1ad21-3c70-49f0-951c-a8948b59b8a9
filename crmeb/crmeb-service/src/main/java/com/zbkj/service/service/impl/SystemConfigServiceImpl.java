package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.config.CrmebConfig;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemConfig;
import com.zbkj.common.request.SystemConfigAdminRequest;
import com.zbkj.common.request.SystemFormCheckRequest;
import com.zbkj.common.request.SystemFormItemCheckRequest;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.ExpressSheetVo;
import com.zbkj.service.dao.SystemConfigDao;
import com.zbkj.service.service.SystemAttachmentService;
import com.zbkj.service.service.SystemConfigService;
import com.zbkj.service.service.SystemFormTempService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SystemConfigServiceImpl 接口实现
 */
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigDao, SystemConfig> implements SystemConfigService {

    @Resource
    private SystemConfigDao dao;

    @Autowired
    private SystemFormTempService systemFormTempService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    CrmebConfig crmebConfig;

    private static final String redisKey = Constants.CONFIG_LIST;

    /**
     * 根据menu name 获取 value
     *
     * @param name menu name
     * @return String
     */
    @Override
    public String getValueByKey(String name) {
        return get(name);
    }


    /**
     * 同时获取多个配置
     *
     * @param keys 多个配置key
     * @return List<String>
     */
    @Override
    public List<String> getValuesByKes(List<String> keys) {
        List<String> result = new ArrayList<>();
        for (String key : keys) {
            result.add(getValueByKey(key));
        }
        return result;
    }

    /**
     * 根据 name 获取 value 找不到抛异常
     *
     * @param name menu name
     * @return String
     */
    @Override
    public String getValueByKeyException(String name) {
        String value = get(name);
        if (null == value) {
            throw new CrmebException("没有找到" + name + "数据");
        }

        return value;
    }

    /**
     * 整体保存表单数据
     *
     * @param systemFormCheckRequest SystemFormCheckRequest 数据保存
     * @return boolean
     */
    @Override
    public Boolean saveForm(SystemFormCheckRequest systemFormCheckRequest) {
        // 检测form表单，并且返回需要添加的数据
        systemFormTempService.checkForm(systemFormCheckRequest);

        List<SystemConfig> systemConfigList = new ArrayList<>();

        // 批量添加
        for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormCheckRequest.getFields()) {
            String value = systemFormItemCheckRequest.getValue();
            // 如果 value 为 *，则不添加
            if (Constants.HIDDEN_VALUE.equals(value)) {
                continue;
            }

            SystemConfig systemConfig = new SystemConfig();
            systemConfig.setName(systemFormItemCheckRequest.getName());
            //对回调地址过滤特殊处理
            if (StringUtils.isNotEmpty(value) && !value.contains("callback")) {
                value = systemAttachmentService.clearPrefix(value);
            }
            systemConfig.setValue(value);
            systemConfig.setFormId(systemFormCheckRequest.getId());
            systemConfig.setTitle(systemFormItemCheckRequest.getTitle());
            SystemConfig config = getSystemConfigByName(systemConfig.getName());
            if (config != null) {
                //新ID用旧的ID
                systemConfig.setId(config.getId());
            }
            systemConfigList.add(systemConfig);
        }
        saveOrUpdateBatch(systemConfigList);     //好像没更新到旧的id的数值
        async(systemConfigList);

        return true;
    }
    /**
     * 保存或更新配置数据
     *
     * @param name  菜单名称
     * @param value 菜单值
     * @return boolean
     */
    @Override
    public Boolean updateOrSaveValueByName(String name, String value) {
        value = systemAttachmentService.clearPrefix(value);

        LambdaQueryWrapper<SystemConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemConfig::getName, name);
        SystemConfig systemConfig = baseMapper.selectOne(lambdaQueryWrapper);
        Boolean result = false;
        if (systemConfig == null) {
            systemConfig = new SystemConfig();
            systemConfig.setName(name);
            systemConfig.setValue(value);
            systemConfig.setStatus(true);
            result = baseMapper.insert(systemConfig) > 0;
        } else {
            systemConfig.setValue(value);
            result = baseMapper.updateById(systemConfig) > 0;
        }
        if (result){
            async(List.of(systemConfig));
        }
        return result;
    }


    /**
     * 根据formId查询数据
     *
     * @param formId Integer id
     * @return HashMap<String, String>
     */
    @Override
    public HashMap<String, String> info(Integer formId) {
        LambdaQueryWrapper<SystemConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemConfig::getFormId, formId);
        List<SystemConfig> systemConfigList = dao.selectList(lambdaQueryWrapper);
        if (ObjectUtil.isNull(systemConfigList)) {
            return CollUtil.newHashMap();
        }
        HashMap<String, String> map = new HashMap<>();
        for (SystemConfig systemConfig : systemConfigList) {
            String name = systemConfig.getName();
            String value = systemConfig.getValue();
            // 检查 name 是否包含 "key"（不区分大小写），若包含则将 value 替换为 *
            String maskedValue = name.toLowerCase().contains("key") ? Constants.HIDDEN_VALUE : value;
            map.put(name, maskedValue);
        }
        map.put("id", formId.toString());
        return map;
    }

    /**
     * 根据name查询数据
     *
     * @param name name
     * @return boolean
     */
    @Override
    public Boolean checkName(String name) {
        String value = get(name);
        return StrUtil.isBlank(value);
    }

    /**
     * 根据key获取配置
     *
     * @param key key
     * @return List
     */
    @Override
    public List<SystemConfig> getListByKey(String key) {
        LambdaQueryWrapper<SystemConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(SystemConfig::getName, key);
        return dao.selectList(lqw);
    }

    /**
     * 获取面单默认配置信息
     *
     * @return ExpressSheetVo
     */
    @Override
    public ExpressSheetVo getDeliveryInfo() {
        String exportId = get("config_export_id");
        String exportTempId = get("config_export_temp_id");
        String exportCom = get("config_export_com");
        String exportToName = get("config_export_to_name");
        String exportToTel = get("config_export_to_tel");
        String exportToAddress = get("config_export_to_address");
        String exportSiid = get("config_export_siid");
        String exportOpen = get("config_export_open");
        return new ExpressSheetVo(Integer.valueOf(exportId), exportCom, exportTempId, exportToName, exportToTel, exportToAddress, exportSiid, Integer.valueOf(exportOpen));
    }

    /**
     * 更新配置信息
     *
     * @param requestList 请求数组
     * @return Boolean
     */
    @Override
    public Boolean updateByList(List<SystemConfigAdminRequest> requestList) {
        List<SystemConfig> configList = requestList.stream().map(e -> {
            SystemConfig systemConfig = new SystemConfig();
            BeanUtils.copyProperties(e, systemConfig);
            return systemConfig;
        }).collect(Collectors.toList());
        return updateBatchById(configList);
    }

    /**
     * 获取颜色配置
     *
     * @return SystemConfig
     */
    @Override
    public SystemConfig getColorConfig() {
        LambdaQueryWrapper<SystemConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(SystemConfig::getName, "change_color_config");
        lqw.eq(SystemConfig::getStatus, 0);
        return dao.selectOne(lqw);
    }

    @Override
    public SystemConfig getSystemConfigByName(String name) {
        return getOne(new LambdaQueryWrapper<SystemConfig>().eq(SystemConfig::getName, name).last("limit 1"));
    }

    /**
     * 把数据同步到redis
     *
     * @param systemConfigList List<SystemConfig> 需要同步的数据
     */
    private void async(List<SystemConfig> systemConfigList) {
        for (SystemConfig systemConfig : systemConfigList) {
            redisUtil.hmSet(redisKey, systemConfig.getName(),systemConfig.getValue());
        }

    }
    /**
     * 把数据同步到redis
     * @param name String
     * @return String
     */
    private String get(String name) {
        Object object = redisUtil.hmGet(redisKey, name);
        if (object == null) {
            LambdaQueryWrapper<SystemConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(SystemConfig::getName, name);
            lambdaQueryWrapper.last(" limit 1");
            SystemConfig systemConfig = baseMapper.selectOne(lambdaQueryWrapper);
            if (systemConfig == null) {
                return null;
            }
            async(List.of(systemConfig));
            return systemConfig.getValue();
        }
        return object.toString();
    }
}

