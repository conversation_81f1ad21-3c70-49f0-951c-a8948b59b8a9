package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.model.user.UserTask;
import com.zbkj.common.request.UserTaskRequest;
import com.zbkj.service.dao.UserTaskDao;
import com.zbkj.service.service.UserTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户任务管理 接口实现类
 */

@Service
public class UserTaskServiceImpl extends ServiceImpl<UserTaskDao, UserTask> implements UserTaskService {

    /**
     * UserTask列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public List<UserTask> getList(UserTaskRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserTask 类的多条件查询
        LambdaQueryWrapper<UserTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(request.getTitle()),UserTask::getTaskTitle,request.getTitle());
        lambdaQueryWrapper.orderByAsc(UserTask::getSort,UserTask::getId);
        return baseMapper.selectList(lambdaQueryWrapper);
    }
}
