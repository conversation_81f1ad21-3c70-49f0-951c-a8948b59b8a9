package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.CardType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.ActivitySwitch;
import com.zbkj.common.model.SystemGroupDataCardModel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserCard;
import com.zbkj.common.model.user.UserCardRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserCardAddRequest;
import com.zbkj.common.request.UserCardEditRequest;
import com.zbkj.common.request.UserCardRequest;
import com.zbkj.common.response.UserCardResponse;
import com.zbkj.common.utils.CommonUtil;
import com.zbkj.service.dao.UserCardDao;
import com.zbkj.service.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SystemCityServiceImpl 接口实现

 */
@Service
public class UserCardServiceImpl extends ServiceImpl<UserCardDao, UserCard> implements UserCardService {
    @Autowired
    private UserService userService;
    @Autowired
    private SystemGroupDataService systemGroupDataService;
    @Autowired
    private UserCardService userCardService;
    @Autowired
    private UserCardRecordService userCardRecordService;
    @Autowired
    private UserActivityRecordService userActivityRecordService;

    @Override
    public PageInfo<UserCardResponse> getList(UserCardRequest request, PageParamRequest pageParamRequest) {
        List<SystemGroupDataCardModel> byGid = systemGroupDataService.getModelByGid(Constants.GROUP_DATA_ID_USER_CARD_CONFIG);
        long cardCount = byGid.stream().filter(by -> "普通卡".equals(by.getJikaInclude())).count();//卡片机集齐数量，暂无配置
        Page<UserCardResponse> startPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<UserCard> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (request.getStatus() != null){
            if (request.getStatus()){
                lambdaQueryWrapper.gt(UserCard::getCardCount, 0)
                        .having("COUNT(*) >= "+cardCount);
            }else {
                lambdaQueryWrapper
                        .having("COUNT(*) < "+cardCount);
            }
        }
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUid()), UserCard::getUserId, request.getUid());
        lambdaQueryWrapper.groupBy(UserCard::getUserId);
        if (StringUtils.isNotBlank(request.getAccount())){
            User userByAccount = userService.getUserByAccount(request.getAccount());
            if (userByAccount != null){
                lambdaQueryWrapper.eq(UserCard::getUserId, userByAccount.getUid());
            }
        }
        lambdaQueryWrapper.orderByDesc(UserCard::getId);
        List<UserCard> userCards = baseMapper.selectList(lambdaQueryWrapper);
        if (userCards == null || userCards.isEmpty()){
            return CommonPage.copyPageInfo(startPage,List.of());
        }

        List<SystemGroupDataCardModel> modelByGid = systemGroupDataService.getModelByGid(Constants.GROUP_DATA_ID_USER_CARD_CONFIG);
        Map<Integer, SystemGroupDataCardModel> groupDataModelMap = modelByGid.stream().collect(Collectors.toMap(SystemGroupDataCardModel::getId, sys -> sys));

        List<Integer> integerList = userCards.stream().map(UserCard::getUserId).collect(Collectors.toList());
        List<User> userListById = userService.getUserListById(integerList);
        if (userListById == null){
            return CommonPage.copyPageInfo(startPage,List.of());
        }
        Map<Integer,User> userMap = userListById.stream().collect(Collectors.toMap(User::getUid, user -> user));

        List<UserCardResponse> userCardResponseList = new ArrayList<>(userCards.size());
        for (UserCard userCard : userCards) {
            UserCardResponse userCardResponse = new UserCardResponse();
            BeanUtils.copyProperties(userCard, userCardResponse);

            List<UserCard> userCardList = baseMapper.selectList(
                    new LambdaQueryWrapper<UserCard>()
                            .eq(UserCard::getUserId, userCard.getUserId())
                            .select(UserCard::getCardId, UserCard::getCardCount)
            );
            List<SystemGroupDataCardModel> systemGroupDataCardModelList = userCardList.stream()
                    .map(card -> {
                        SystemGroupDataCardModel systemGroupDataCardModel = new SystemGroupDataCardModel();
                        BeanUtils.copyProperties(groupDataModelMap.get(card.getCardId()), systemGroupDataCardModel);
                        systemGroupDataCardModel.setJikaCount(card.getCardCount());
                        return systemGroupDataCardModel;
                    })
                    .collect(Collectors.toList());

            userCardResponse.setSystemGroupDataCardModels(systemGroupDataCardModelList);
            userCardResponse.setAccount(userMap.get(userCard.getUserId()).getAccount());
            userCardResponse.setStatus(
                    userCardList.size() >= cardCount && userCardList.stream().filter(card -> card.getCardCount() > 0).count()  >= cardCount);

            userCardResponseList.add(userCardResponse);
        }
        return CommonPage.copyPageInfo(startPage,userCardResponseList);
    }

    @Override
    public UserCard getCollectibleCardInformation(Integer userId, Integer cardId) {
        LambdaQueryWrapper<UserCard> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserCard::getUserId, userId);
        lambdaQueryWrapper.eq(UserCard::getCardId, cardId);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public HashMap<String, Object> sign() {
        ActivitySwitch activitySwitch = userActivityRecordService.activityStatus();
        if (!activitySwitch.getJikaSwitch()){
            throw new CrmebException("当前活动暂未开放，敬请期待");
        }
        User user = userService.getInfo();
        if (userCardRecordService.signStatus(user.getUid())) {
            throw new CrmebException("当前暂无签到次数！");
        }
        List<HashMap<String, Object>> listMapByGid = cardList(null);
        HashMap<String, Object> randomOfList = CommonUtil.getRandomOfList(listMapByGid);
        if (randomOfList == null && randomOfList.isEmpty()) {
            throw new CrmebException("领取失败，请稍后重试");
        }
        //卡片表更新
        Integer cardId = Integer.valueOf(randomOfList.get("id").toString());
        UserCard cardInformation = userCardService.getCollectibleCardInformation(user.getUid(), cardId);
        if (cardInformation == null) {
            cardInformation = new UserCard();
            cardInformation.setCardCount(1);
            cardInformation.setUserId(user.getUid());
            cardInformation.setCardId(cardId);
        } else {
            cardInformation.setCardCount(cardInformation.getCardCount() + 1);
        }
        userCardService.saveOrUpdate(cardInformation);
        //更新次数
        if (user.getJikaEveryDaySignNum() > 0){
            user.setJikaEveryDaySignNum(user.getJikaEveryDaySignNum()-1);
        }else if (user.getJikaSignNum() > 0){
            user.setJikaSignNum(user.getJikaSignNum()-1);
        }else {
            throw new CrmebException("当前暂无签到次数！");
        }
        userService.updateById(user);
        //获取记录写入
        UserCardRecord userCardRecord = new UserCardRecord();
        userCardRecord.setType(CardType.SIGN_IN);
        userCardRecord.setCreateTime(new Date());
        userCardRecord.setCardId(cardId);
        userCardRecord.setUserId(user.getUid());
        userCardRecord.setCreateDate(LocalDate.now());
        userCardRecordService.save(userCardRecord);
        return randomOfList;
    }

    @Override
    public List<HashMap<String, Object>> cardList(List<Integer> integerList) {
        List<HashMap<String, Object>> listMapByGid = systemGroupDataService.getListMapByGid(Constants.GROUP_DATA_ID_USER_CARD_CONFIG,integerList);
        return listMapByGid;
    }

    @Override
    public List<UserCard> getCollectibleCardInformation(Integer userId) {
        return baseMapper.selectList(new LambdaQueryWrapper<UserCard>().eq(UserCard::getUserId,userId));
    }

    @Override
    public List<Object> myCard() {
        User user = userService.getInfo();
        List<UserCard> collectibleCardInformation = getCollectibleCardInformation(user.getUid());
        if (collectibleCardInformation == null || collectibleCardInformation.isEmpty()){
            return null;
        }
        List<Integer> integerList = collectibleCardInformation.stream().map(UserCard::getCardId).collect(Collectors.toList());
        List<HashMap<String, Object>> hashMaps = cardList(integerList);
        List<Object> ownList = new ArrayList<>(collectibleCardInformation.size());
        Map<Integer,Map<String,Object>> cardAllMaps = new HashMap<>();
        for (HashMap<String, Object> hashMap : hashMaps) {
            Integer cardId = Integer.valueOf(hashMap.get("id").toString());
            cardAllMaps.put(cardId,hashMap);
        }
        for (UserCard userCard : collectibleCardInformation) {
            Map<String,Object> objectMap = cardAllMaps.get(userCard.getCardId());
            if (objectMap!=null){
                objectMap.put("count",userCard.getCardCount());
                ownList.add(objectMap);
            }
        }
        return ownList;
    }

    @Override
    public List<HashMap<String, Object>> isAllCardsContainMyCard() {
        User user = userService.getInfo();
        List<HashMap<String, Object>> hashMaps = cardList(null);

        List<UserCard> collectibleCardInformation = getCollectibleCardInformation(user.getUid());
        if (collectibleCardInformation == null || collectibleCardInformation.isEmpty()){
            for (HashMap<String, Object> hashMap : hashMaps) {
                hashMap.put("count",0);
            }
            return hashMaps;
        }
        Map<Integer, UserCard> cardIdMap = collectibleCardInformation.stream().collect(Collectors.toMap(UserCard::getCardId, card -> card));
        for (HashMap<String, Object> hashMap : hashMaps) {
            Integer cardId = Integer.valueOf(hashMap.get("id").toString());
            UserCard userCard = cardIdMap.get(cardId);
            if (userCard == null){
                hashMap.put("count",0);
            }else {
                hashMap.put("count",userCard.getCardCount());
            }
        }
        return hashMaps;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCardCount(UserCardEditRequest request) {
        Integer userId = request.getUid();
        Integer cardId = request.getCardId();
        Integer count = request.getCount();
        if (count < 0){
            throw new CrmebException("无效的卡片数量");
        }
        User serviceById = userService.getById(userId);
        if (serviceById == null){
            throw new CrmebException("无效的用户ID");
        }
        SystemGroupDataCardModel groupDataModel = systemGroupDataService.getModelById(cardId);
        if (groupDataModel == null){
            throw new CrmebException("无效的卡片ID");
        }
        LambdaQueryWrapper<UserCard> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(UserCard::getUserId,userId).eq(UserCard::getCardId,cardId);
        UserCard userCard = baseMapper.selectOne(lambdaQueryWrapper);

        CardType cardType = null;
        int sendCount = request.getCount();
        if (userCard == null){
            userCard = new UserCard();
            userCard.setUserId(userId);
            userCard.setCardId(cardId);
            userCard.setCardCount(count);
            cardType = CardType.GIVE_AWAY;
        }else {

            if (count > userCard.getCardCount()){
                cardType = CardType.GIVE_AWAY;
                sendCount = count - userCard.getCardCount();
            }else {
                cardType = CardType.DEDUCT;
                sendCount = userCard.getCardCount() - count;
            }
            userCard.setCardCount(count);
        }

        if (sendCount > 0){
            //获取记录写入
            UserCardRecord userCardRecord = new UserCardRecord();
            userCardRecord.setType(cardType);
            userCardRecord.setCreateTime(new Date());
            userCardRecord.setCardId(userCard.getCardId());
            userCardRecord.setUserId(userCard.getUserId());
            userCardRecord.setCreateDate(LocalDate.now());
            userCardRecord.setCount(sendCount);
            userCardRecordService.save(userCardRecord);
        }
        return baseMapper.updateById(userCard) > 0;
    }

    @Override
    public Boolean repossess(Integer uid) {
        LambdaUpdateWrapper<UserCard> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserCard::getUserId,uid);
        UserCard userCard = new UserCard();
        userCard.setCardCount(0);
        return  baseMapper.update(userCard,updateWrapper) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addCard(UserCardAddRequest request) {
        User userByAccount = userService.getUserByAccount(request.getAccount());
        if (userByAccount == null){
            throw new CrmebException("无效的账号");
        }
        UserCard cardInformation = getCollectibleCardInformation(userByAccount.getUid(), request.getCardId());
        if (cardInformation == null){
            cardInformation = new UserCard();
            cardInformation.setCardCount(request.getCount());
            cardInformation.setUserId(userByAccount.getUid());
            cardInformation.setCardId(request.getCardId());
        }else {
            cardInformation.setCardCount(cardInformation.getCardCount()+request.getCount());
        }

        //获取记录写入
        UserCardRecord userCardRecord = new UserCardRecord();
        userCardRecord.setType(CardType.GIVE_AWAY);
        userCardRecord.setCreateTime(new Date());
        userCardRecord.setCardId(cardInformation.getCardId());
        userCardRecord.setUserId(userByAccount.getUid());
        userCardRecord.setCreateDate(LocalDate.now());
        userCardRecord.setCount(request.getCount());
        userCardRecordService.save(userCardRecord);

        return this.saveOrUpdate(cardInformation);
    }
}

