package com.zbkj.service.util;

import okhttp3.*;
import org.json.JSONObject;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用的类来处理GET和POST请求
 */
public class OkHttpUtils {
   // private OkHttpClient client;
    // 改成静态共享的 client，防止每次 new 都生成一个新连接池
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(20, TimeUnit.SECONDS)
            .writeTimeout(20, TimeUnit.SECONDS)
            .build();
    public OkHttpUtils() {
    }
    // 发送GET请求
    public String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        }
    }

    // 发送带参数的POST请求
    public String post(String url, Map<String, Object> parameters) throws IOException {
        // 创建 JSON 对象
        JSONObject rpcRequest = new JSONObject(parameters);

        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(
                        okhttp3.MediaType.parse("application/json"),
                        rpcRequest.toString()
                ))
                .build();

        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        }
    }
}
