package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.AdminWhitelist;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.AdminWhitelistSearchRequest;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.AdminWhitelistDao;
import com.zbkj.service.service.AdminWhitelistService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 后台日志记录 接口实现类
 */

@Service
public class AdminWhitelistServiceImpl extends ServiceImpl<AdminWhitelistDao, AdminWhitelist> implements AdminWhitelistService {

    @Resource
    private RedisUtil redisUtil;

    /**
     * AdminWhitelist列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<AdminWhitelist> getList(AdminWhitelistSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 AdminWhitelist 类的多条件查询
        LambdaQueryWrapper<AdminWhitelist> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getIp()),AdminWhitelist::getIp,request.getIp());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()),AdminWhitelist::getStatus,request.getStatus());
        return CommonPage.copyPageInfo(startPage,baseMapper.selectList(lambdaQueryWrapper));
    }

    @PostConstruct
    public void init() {
        LambdaQueryWrapper<AdminWhitelist> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AdminWhitelist::getStatus,true);
        List<AdminWhitelist> adminWhitelists = baseMapper.selectList(lambdaQueryWrapper);
        if (adminWhitelists != null && adminWhitelists.size() > 0){
            adminWhitelists.forEach(adminWhitelist -> {
                redisUtil.set(Constants.WHITELIST_LIST+adminWhitelist.getIp(),1);
            });
        }
    }

    @Override
    public boolean deleteWithCache(Integer id) {
        AdminWhitelist whitelist = getById(id);
        if (whitelist == null){
            throw new CrmebException("无效的ID");
        }
        int deleted = baseMapper.deleteById(id);
        if (deleted > 0){
            redisUtil.delete(Constants.WHITELIST_LIST+whitelist.getIp());
        }
        return true;
    }

    @Override
    public boolean deleteIPCache(String ip) {
        redisUtil.delete(Constants.WHITELIST_LIST+ip);
        return true;
    }
}
