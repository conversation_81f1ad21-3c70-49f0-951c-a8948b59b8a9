package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.system.AdminLog;
import com.zbkj.common.request.AdminLogRequest;

import java.util.List;

/**
 * 后台日志记录 业务接口
 */
public interface AdminLogService extends IService<AdminLog> {

    /**
     * AdminLog 列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<AdminLog> getList(AdminLogRequest request);
}

