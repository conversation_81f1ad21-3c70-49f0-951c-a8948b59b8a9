package com.zbkj.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.constants.ExperienceRecordConstants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.finance.UserRecharge;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserBill;
import com.zbkj.common.model.user.UserExperienceRecord;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import java.math.RoundingMode;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


/**
 * 支付类
 */
@Service
@Slf4j
public class RechargePayServiceImpl implements RechargePayService {

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private UserExperienceRecordService userExperienceRecordService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private DistributedLockService distributedLockService;

    /**
     * 支付成功处理
     * 增加余额，userBill记录
     *
     * @param userRecharge 充值订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 10)
    public Boolean paySuccess(UserRecharge userRecharge) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + userRecharge.getUid(), () -> {
            try {
                // 记录充值订单信息
                log.info("[paySuccess][userId={}] 开始处理充值订单，用户信息：{}", userRecharge.getUid(), userRecharge);

                userRecharge.setPaid(1);
                userRecharge.setPayTime(DateUtil.nowDateTime());

                // 查询用户（不再需要悲观锁，因为已经有Redis锁保护）
                User user = userService.getById(userRecharge.getUid());
                if (user == null) {
                    log.error("[paySuccess][userId={}] 用户不存在", userRecharge.getUid());
                    return Boolean.FALSE;
                }

            // 记录查询到的用户初始状态
            log.info("[paySuccess][userId={}] 查询用户初始状态，rechargeAmount={}，frozenBalance={}，nowMoney={}，experience={}",
                    user.getUid(), user.getRechargeAmount(), user.getFrozenBalance(), user.getNowMoney(), user.getExperience());

            BigDecimal payPrice = userRecharge.getPrice().add(userRecharge.getGivePrice());
            BigDecimal balance = user.getNowMoney().add(payPrice);

            // 记录计算后的 payPrice 和 balance
            log.info("[paySuccess][userId={}] 计算充值总额，payPrice={}，balance={}",
                    user.getUid(), payPrice, balance);

            // 余额变动对象
            UserBill userBill = new UserBill();
            userBill.setUid(userRecharge.getUid());
            userBill.setLinkId(userRecharge.getOrderId());
            userBill.setPm(1);
            userBill.setTitle("充值支付");
            userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
            userBill.setType(Constants.USER_BILL_TYPE_PAY_RECHARGE);
            userBill.setNumber(payPrice);
            userBill.setBalance(balance);
            userBill.setMark(StrUtil.format("余额增加了{}元", payPrice));
            userBill.setStatus(1);
            userBill.setCreateTime(DateUtil.nowDateTime());

            // 记录组装的 userBill 信息
            log.info("[paySuccess][userId={}] 组装余额变动记录，userBill.number={}，userBill.balance={}",
                    user.getUid(), userBill.getNumber(), userBill.getBalance());

            //组装用户信息
            User userUpdate = new User();
            userUpdate.setRechargeAmount(user.getRechargeAmount().add(payPrice));
            userUpdate.setExperience(user.getExperience() + payPrice.intValue());
            userUpdate.setUid(user.getUid());
            userUpdate.setLevel(user.getLevel());
            userUpdate.setNickname(user.getNickname());
            userUpdate.setNowMoney(balance);

            // 记录组装的 userUpdate 信息
            log.info("[paySuccess][userId={}] 组装用户信息，userUpdate.rechargeAmount={}，userUpdate.nowMoney={}，userUpdate.experience={}",
                    user.getUid(), userUpdate.getRechargeAmount(), userUpdate.getNowMoney(), userUpdate.getExperience());

            // 订单变动
            if (!userRechargeService.updateById(userRecharge)) {
                log.error("[paySuccess][userId={}] 更新充值订单失败: orderId={}",
                        user.getUid(), userRecharge.getOrderId());
                throw new CrmebException("更新充值订单失败，订单ID：" + userRecharge.getOrderId());
            }

            // 记录更新充值订单成功
            log.info("[paySuccess][userId={}] 更新充值订单成功，订单ID：{}",
                    user.getUid(), userRecharge.getOrderId());

            // 创建账单记录
            if (!userBillService.save(userBill)) {
                log.error("[paySuccess][userId={}] 保存账单记录失败: orderId={}",
                        user.getUid(), userRecharge.getOrderId());
                throw new CrmebException("保存账单记录失败，订单ID：" + userRecharge.getOrderId());
            }

            // 记录保存账单记录成功
            log.info("[paySuccess][userId={}] 保存账单记录成功，订单ID：{}",
                    user.getUid(), userRecharge.getOrderId());

            // 经验记录
            UserExperienceRecord record = new UserExperienceRecord();
            record.setUid(user.getUid());
            record.setLinkId(userRecharge.getOrderId());
            record.setLinkType("recharge");
            record.setType(ExperienceRecordConstants.EXPERIENCE_RECORD_TYPE_ADD);
            record.setTitle("用户充值成功");
            record.setExperience(payPrice.intValue());
            record.setBalance(user.getExperience()+payPrice.intValue());
            record.setMark("用户充值成功增加" + payPrice.intValue() + "经验");
            record.setCreateTime(cn.hutool.core.date.DateUtil.date());

            // 记录组装的经验记录
            log.info("[paySuccess][userId={}] 组装经验记录，record.experience={}，record.balance={}",
                    user.getUid(), record.getExperience(), record.getBalance());

            // 经验记录
            if (!userExperienceRecordService.save(record)) {
                log.error("[paySuccess][userId={}] 保存经验记录失败: orderId={}",
                        user.getUid(), userRecharge.getOrderId());
                throw new CrmebException("保存经验记录失败，订单ID：" + userRecharge.getOrderId());
            }

            // 记录保存经验记录成功
            log.info("[paySuccess][userId={}] 保存经验记录成功，订单ID：{}",
                    user.getUid(), userRecharge.getOrderId());

            //经验升级
            userLevelService.upLevel(userUpdate);

            // 记录调用 userLevelService.upLevel 后的状态
            log.info("[paySuccess][userId={}] 调用 userLevelService.upLevel 完成，level={}",
                    user.getUid(), userUpdate.getLevel());

            //更新用户信息
            if (!userService.updateInfo(userUpdate, user.getUid())) {
                log.error("[paySuccess][userId={}] 更新用户信息失败: uid={}",
                        user.getUid(), user.getUid());
                throw new CrmebException("更新用户信息失败，uid：" + user.getUid());
            }

            // 记录更新用户信息后的数据库状态
            User updatedUser = userService.getById(user.getUid());
            log.info("[paySuccess][userId={}] 更新用户信息后，rechargeAmount={}，nowMoney={}，experience={}",
                    user.getUid(), updatedUser.getRechargeAmount(), updatedUser.getNowMoney(), updatedUser.getExperience());

            // 记录事务完成
            log.info("[paySuccess][userId={}] 充值订单处理完成，订单ID：{}",
                    user.getUid(), userRecharge.getOrderId());

                return Boolean.TRUE;
            } catch (Exception e) {
                log.error("[paySuccess][userId={}] 支付订单处理异常，订单ID：{}，异常类型：{}，信息：{}",
                        userRecharge.getUid(), userRecharge.getOrderId(), e.getClass().getSimpleName(), e.getMessage(), e);
                throw new CrmebException("充值订单处理失败，订单ID：" + userRecharge.getOrderId() + "，原因：" + e.getMessage());
            }
        });
    }

    /**
     * 支付成功后-如果存在渠道送百分比，进行处理
     * 增加余额，userBill记录
     *
     * @param userRecharge 充值订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 10)
    public Boolean paySuccessSendBonus(UserRecharge userRecharge) {
        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + userRecharge.getUid(), () -> {
            try {
                // 记录充值订单信息
                log.info("[paySuccessSendBonus][userId={}] 开始处理渠道赠送，订单信息：{}", userRecharge.getUid(), JSON.toJSONString(userRecharge));

                // 查询用户（不再需要悲观锁，因为已经有Redis锁保护）
                User user = userService.getById(userRecharge.getUid());
                if (user == null) {
                    log.error("[paySuccessSendBonus][userId={}] 用户不存在", userRecharge.getUid());
                    return Boolean.FALSE;
                }

            // 计算渠道赠送比例
            Integer bonusPrice = this.getPayChannelBonus(userRecharge);
            log.info("[paySuccessSendBonus][orderId={}] 渠道赠送比例：{}", userRecharge.getOrderId(), bonusPrice);
            if (bonusPrice <= 0) {
                log.info("[paySuccessSendBonus][orderId={}] 赠送比例 {}，不处理", userRecharge.getOrderId(), bonusPrice);
                return Boolean.FALSE;
            }

            // 计算赠送金额
            BigDecimal price = userRecharge.getPrice();
            BigDecimal bonusPercentage = new BigDecimal(bonusPrice).divide(new BigDecimal(100), 2, RoundingMode.HALF_DOWN);
            BigDecimal payPrice = price.multiply(bonusPercentage).setScale(2, RoundingMode.HALF_DOWN);
            BigDecimal balance = user.getNowMoney().add(payPrice);

            // 记录计算结果
            log.info("[paySuccessSendBonus][userId={}] 计算赠送金额，payPrice={}，balance={}", user.getUid(), payPrice, balance);

            // 余额变动对象
            UserBill userBill = new UserBill();
            userBill.setUid(userRecharge.getUid());
            userBill.setLinkId(userRecharge.getOrderId().concat("_1"));
            userBill.setPm(1);
            userBill.setTitle("充值支付-渠道赠送");
            userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
            userBill.setType(Constants.USER_BILL_TYPE_PAY_RECHARGE_BONUS);
            userBill.setNumber(payPrice);
            userBill.setBalance(balance);
            userBill.setMark(StrUtil.format("余额增加了{}元", payPrice));
            userBill.setStatus(1);
            userBill.setCreateTime(DateUtil.nowDateTime());

            // 记录组装的 userBill 信息
            log.info("[paySuccessSendBonus][userId={}] 组装余额变动记录，number={}，balance={}",
                    user.getUid(), userBill.getNumber(), userBill.getBalance());

            //组装用户信息
            User userUpdate = new User();
            userUpdate.setUid(user.getUid());
            userUpdate.setLevel(user.getLevel());
            userUpdate.setNickname(user.getNickname());
            userUpdate.setNowMoney(balance);

            // 记录组装的 userUpdate 信息
            log.info("[paySuccessSendBonus][userId={}] 组装用户信息，nowMoney={}", user.getUid(), userUpdate.getNowMoney());

            // 保存账单记录
            if (!userBillService.save(userBill)) {
                log.error("[paySuccessSendBonus][userId={}] 保存账单记录失败，orderId={}", user.getUid(), userRecharge.getOrderId());
                throw new CrmebException("保存账单记录失败，订单ID：" + userRecharge.getOrderId());
            }

            // 记录保存账单记录成功
            log.info("[paySuccessSendBonus][userId={}] 保存账单记录成功，orderId={}", user.getUid(), userRecharge.getOrderId());

            // 更新用户信息
            if (!userService.updateInfo(userUpdate, user.getUid())) {
                log.error("[paySuccessSendBonus][userId={}] 更新用户信息失败，uid={}", user.getUid(), user.getUid());
                throw new CrmebException("更新用户信息失败，uid：" + user.getUid());
            }

            // 记录更新用户信息后的状态
            User updatedUser = userService.getById(user.getUid());
            log.info("[paySuccessSendBonus][userId={}] 更新用户信息后，nowMoney={}", user.getUid(), updatedUser.getNowMoney());

            // 记录处理完成
            log.info("[paySuccessSendBonus][userId={}] 渠道赠送处理完成，orderId={}", user.getUid(), userRecharge.getOrderId());

                return Boolean.TRUE;
            } catch (Exception e) {
                log.error("[paySuccessSendBonus][userId={}] 渠道赠送处理异常，orderId={}，异常类型：{}，信息：{}",
                        userRecharge.getUid(), userRecharge.getOrderId(), e.getClass().getSimpleName(), e.getMessage(), e);
                throw new CrmebException("渠道赠送处理失败，订单ID：" + userRecharge.getOrderId() + "，原因：" + e.getMessage());
            }
        });
    }

    //每个支付单独配置
    private Integer getPayChannelBonus(UserRecharge userRecharge) {
        Map<String, String[]> payTypeConfigMap = new HashMap<>();
        payTypeConfigMap.put(Constants.PAY_TYPE_WE_CHAT, new String[]{"pay_weixin_open", "weixin_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_ALI_PAY, new String[]{"pay_alipay_open", "alipay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_AA_PAY, new String[]{"aaStatus", "aapay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_M_PAY, new String[]{"mpStatus", "mpay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_F_PAY, new String[]{"fpStatus", "fpay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_WANB_PAY, new String[]{"wbStatus", "wanbpay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_CB_PAY, new String[]{"cbStatus", "cbpay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_KD_PAY, new String[]{"kdStatus", "kdpay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_TO_PAY, new String[]{"toStatus", "topay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_OK_PAY, new String[]{"okStatus", "okpay_recharge_bonus"});
        payTypeConfigMap.put(Constants.PAY_TYPE_USDT_PAY, new String[]{"usdtRechargerStatus", "usdt_recharge_bonus"});

        String rechargeType = userRecharge.getRechargeType();
        if (!payTypeConfigMap.containsKey(rechargeType)) {
            return 0;
        }

        String[] configKeys = payTypeConfigMap.get(rechargeType);
        String statusKey = configKeys[0];
        String bonusKey = configKeys[1];

        String status = systemConfigService.getValueByKeyException(statusKey);
        if (!"1".equals(status)) {
            return 0;
        }

        String bonusStr = systemConfigService.getValueByKeyException(bonusKey);
        if (bonusStr == null || bonusStr.trim().isEmpty()) {
            return 0;
        }

        try {
            int bonus = Integer.parseInt(bonusStr.trim());
            if (bonus < 0 || bonus > 100) {
                return 0;
            }
            return bonus;
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
