package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.LocationType;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.StoreLocationDao;
import com.zbkj.common.model.StoreLocation;
import com.zbkj.common.request.StoreLocationSearchRequest;
import com.zbkj.service.service.StoreLocationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 线下门店和公司地址 接口实现类
 */

@Service
public class StoreLocationServiceImpl extends ServiceImpl<StoreLocationDao, StoreLocation> implements StoreLocationService {

    @Resource
    private RedisUtil redisUtil;

    /**
     * StoreLocation列表查询
     *
     * @param request 默认生成搜索的对象 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<StoreLocation> getList(StoreLocationSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 StoreLocation 类的多条件查询
        LambdaQueryWrapper<StoreLocation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Objects.nonNull(request.getType()), StoreLocation::getType, request.getType());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(request.getAddress()), StoreLocation::getAddress, request.getAddress());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()), StoreLocation::getStatus, request.getStatus());
        lambdaQueryWrapper.orderByAsc(StoreLocation::getType, StoreLocation::getRanking);
        List<StoreLocation> list = baseMapper.selectList(lambdaQueryWrapper);
        return CommonPage.copyPageInfo(startPage, list);
    }

    @Override
    public Map<LocationType, List<StoreLocation>> init() {
        redisUtil.delete(Constants.STORE_LOCATION);
        LambdaQueryWrapper<StoreLocation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreLocation::getStatus, Boolean.TRUE);
        lambdaQueryWrapper.orderByAsc(StoreLocation::getType,StoreLocation::getRanking);
        List<StoreLocation> storeLocations = baseMapper.selectList(lambdaQueryWrapper);
        Map<LocationType, List<StoreLocation>> groupedLocations = storeLocations.stream()
                .collect(Collectors.groupingBy(StoreLocation::getType));
        redisUtil.set(Constants.STORE_LOCATION,groupedLocations);
        return groupedLocations;
    }

    @Override
    public Map<LocationType, List<StoreLocation>> getList() {
        Map<LocationType, List<StoreLocation>> locationTypeListMap = redisUtil.get(Constants.STORE_LOCATION);
        if (locationTypeListMap == null || locationTypeListMap.size() == 0){
            return init();
        }
        return locationTypeListMap;
    }

}
