package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.CardType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.SystemGroupDataCardModel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserCard;
import com.zbkj.common.model.user.UserCardRecord;
import com.zbkj.common.model.user.UserCardShare;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserCardShareRequest;
import com.zbkj.common.response.UserCardShareResponse;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.ExchangeCodeGenerator;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.UserCardShareDao;
import com.zbkj.service.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SystemCityServiceImpl 接口实现

 */
@Service
public class UserCardShareServiceImpl extends ServiceImpl<UserCardShareDao, UserCardShare> implements UserCardShareService {
    @Autowired
    private UserCardService userCardService;
    @Autowired
    private  UserCardShareService userCardShareService;
    @Autowired
    private SystemGroupDataService systemGroupDataService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserCardRecordService userCardRecordService;

    @Transactional(rollbackFor = Exception.class)
    public String share(Integer cardId) {
        if (cardId == null){
            throw new CrmebException("无效的卡片ID");
        }
        User user = userService.getInfo();
        UserCard cardServiceById = userCardService.getCollectibleCardInformation(user.getUid(),cardId);
        if (cardServiceById == null){
            throw new CrmebException("无效的卡片ID");
        }
        SystemGroupDataCardModel modelById = systemGroupDataService.getModelById(cardServiceById.getCardId());
        if (modelById == null){
            throw new CrmebException("无效的卡片ID");
        }
        if ("否".equals(modelById.getJikaShare())){
            throw new CrmebException("当前卡片无法分享");
        }
        if (user.getRechargeAmount().compareTo(modelById.getJikaAmount()) < 0){
            throw new CrmebException("当前未满足分享条件！");
        }
        if (cardServiceById.getCardCount() == 0){
            throw new CrmebException("卡片数量不足");
        }
        //cardServiceById.setCardCount(cardServiceById.getCardCount()-1);
        //userCardService.updateById(cardServiceById);
        String exchangeCode = ExchangeCodeGenerator.generateExchangeCode(20);
        UserCardShare userCardShare = new UserCardShare();
        userCardShare.setSendUserId(cardServiceById.getUserId());
        userCardShare.setCardId(cardServiceById.getCardId());
        userCardShare.setSendTime(LocalDateTime.now());
        userCardShare.setCode(exchangeCode);
        userCardShareService.save(userCardShare);
        return exchangeCode;
    }

    @Transactional(rollbackFor = Exception.class)
    public HashMap<String, Object> receive(String code) {
        if (StringUtils.isBlank(code)){
            throw new CrmebException("无效的口令码");
        }
        UserCardShare userCardShareByCode = getUserCardShareByCode(code);
        if (userCardShareByCode == null){
            throw new CrmebException("无效的口令码");
        }
        if (userCardShareByCode.getReceiveUserId() != null){
            throw new CrmebException("该口令码已被使用！");
        }

        UserCard sendCardUserInfo = userCardService.getCollectibleCardInformation(userCardShareByCode.getSendUserId(), userCardShareByCode.getCardId());
        if (sendCardUserInfo == null || sendCardUserInfo.getCardCount() == 0){
            throw new CrmebException("口令码已失效！");
        }
        SystemGroupDataCardModel modelById = systemGroupDataService.getModelById(sendCardUserInfo.getCardId());
        if (modelById == null){
            throw new CrmebException("已失效的卡片");
        }
        if ("否".equals(modelById.getJikaShare())){
            throw new CrmebException("当前卡片无法领取");
        }
        User user = userService.getInfo();
        if (user.getUid().equals(userCardShareByCode.getSendUserId())){
            throw new CrmebException("无法领取自己分享的口令码！");
        }
        userCardShareByCode.setReceiveUserId(user.getUid());
        userCardShareByCode.setReceiveTime(LocalDateTime.now());
        this.baseMapper.updateById(userCardShareByCode);

        Integer cardId = userCardShareByCode.getCardId();
        List<Integer> id = List.of(cardId);
        List<HashMap<String, Object>> listMapByGid = systemGroupDataService.getListMapByGid(Constants.GROUP_DATA_ID_USER_CARD_CONFIG,id);
        if (listMapByGid == null || listMapByGid.isEmpty()){
            throw new CrmebException("该卡片已失效！");
        }
        HashMap<String, Object> stringObjectHashMap = listMapByGid.get(0);
        UserCard cardInformation = userCardService.getCollectibleCardInformation(user.getUid(), cardId);
        if (cardInformation == null) {
            cardInformation = new UserCard();
            cardInformation.setCardCount(1);
            cardInformation.setUserId(user.getUid());
            cardInformation.setCardId(cardId);
        } else {
            cardInformation.setCardCount(cardInformation.getCardCount() + 1);
        }
        userCardService.saveOrUpdate(cardInformation);
        //获取记录写入
        UserCardRecord userCardRecord = new UserCardRecord();
        userCardRecord.setType(CardType.RECEIVE);
        userCardRecord.setCreateTime(new Date());
        userCardRecord.setCardId(cardId);
        userCardRecord.setUserId(user.getUid());
        userCardRecord.setCreateDate(LocalDate.now());
        userCardRecordService.save(userCardRecord);
        if (user.getUid() != userCardShareByCode.getSendUserId()){
            sendCardUserInfo.setCardCount(sendCardUserInfo.getCardCount()-1);
            userCardService.updateById(sendCardUserInfo);
        }
        return stringObjectHashMap;
    }

    @Override
    public UserCardShare getUserCardShareByCode(String code) {
        LambdaQueryWrapper<UserCardShare> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserCardShare::getCode,code);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public PageInfo<UserCardShareResponse> getList(UserCardShareRequest request) {
        Page<UserCardShare> startedPage = PageHelper.startPage(request.getPage(), request.getLimit());
        LambdaQueryWrapper<UserCardShare> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(request.getCode()),UserCardShare::getCode,request.getCode());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getCardId()),UserCardShare::getCardId,request.getCardId());
        if (StringUtils.isNotEmpty(request.getSendAccount())){
            User userByAccount = userService.getUserByAccount(request.getSendAccount());
            if (userByAccount != null){
                request.setSendUserId(userByAccount.getUid());
            }
        }
        if (StringUtils.isNotEmpty(request.getReceiveAccount())){
            User userByAccount = userService.getUserByAccount(request.getReceiveAccount());
            if (userByAccount != null){
                request.setSendUserId(userByAccount.getUid());
            }
        }
        lambdaQueryWrapper.eq(Objects.nonNull(request.getSendUserId()),UserCardShare::getSendUserId,request.getSendUserId());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getReceiveUserId()),UserCardShare::getReceiveUserId,request.getReceiveUserId());

        if (StringUtils.isNotBlank(request.getSendDateLimit())){
            DateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(request.getSendDateLimit());
            lambdaQueryWrapper.between(UserCardShare::getSendTime, dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }

        if (StringUtils.isNotBlank(request.getReceiveDateLimit())){
            DateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(request.getReceiveDateLimit());
            lambdaQueryWrapper.between(UserCardShare::getReceiveTime, dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        List<UserCardShare> userCardShares = baseMapper.selectList(lambdaQueryWrapper);
        if (userCardShares == null || userCardShares.isEmpty()){
            return CommonPage.copyPageInfo(startedPage,List.of());
        }

        List<Integer> integerList = userCardShares.stream().map(UserCardShare::getSendUserId).collect(Collectors.toList());
        integerList.addAll(userCardShares.stream().map(UserCardShare::getReceiveUserId).collect(Collectors.toList()));
        Set<Integer> set = new LinkedHashSet<>(integerList);
        List<User> userListById = userService.getUserListById(set);

        Map<Integer, User> integerUserMap = userListById.stream().collect(Collectors.toMap(User::getUid, Function.identity()));

        List<UserCardShareResponse> collect = userCardShares.stream().map(userCardShare -> {
            UserCardShareResponse userCardShareResponse = new UserCardShareResponse();
            BeanUtils.copyProperties(userCardShare, userCardShareResponse);
            if (userCardShare.getSendUserId() != null){
                User user1 = integerUserMap.get(userCardShare.getSendUserId());
                userCardShareResponse.setSendAccount(user1.getAccount());
            }
            if (userCardShare.getReceiveUserId() != null){
                User user2 = integerUserMap.get(userCardShare.getReceiveUserId());
                userCardShareResponse.setReceiveAccount(user2.getAccount());
            }
            return userCardShareResponse;
        }).collect(Collectors.toList());
        return CommonPage.copyPageInfo(startedPage,collect);
    }
}

