package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.request.PageParamRequest;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.finance.UserRecharge;
import com.zbkj.common.request.UserRechargBalanceRequest;
import com.zbkj.common.request.UserRechargReviewRequest;
import com.zbkj.common.request.UserRechargeSearchRequest;
import com.zbkj.common.response.UserRechargeResponse;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
* UserRechargeService 接口

*/
public interface UserRechargeService extends IService<UserRecharge> {

    /**
     * 充值记录列表
     * @param request 请求参数
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<UserRechargeResponse> getList(UserRechargeSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 审核
     * @param request
     * @return
     */
    Boolean review(UserRechargReviewRequest request);

    /**
     * 充值统计
     * @return HashMap
     */
    HashMap<String, BigDecimal> getBalanceList(UserRechargBalanceRequest request);

    UserRecharge getInfoByEntity(UserRecharge userRecharge);

    List<UserRecharge> findPendingSuccessOrder(int page, int pageSize);

    UserRecharge getInfoByOrderId(String orderId);

    /**
     * 根据日期获取充值订单数量
     * @param date 日期，yyyy-MM-dd格式
     * @return Integer
     */
    Integer getRechargeOrderNumByDate(String date);

    /**
     * 根据日期获取充值订单金额
     * @param date 日期，yyyy-MM-dd格式
     * @return BigDecimal
     */
    BigDecimal getRechargeOrderAmountByDate(String date);

    /**
     * 获取总人数
     * @return Integer
     */
    Integer getTotalPeople();

    /**
     * 获取总金额
     * @return BigDecimal
     */
    BigDecimal getTotalPrice();

    /**
     * 根据时间获取充值用户数量
     * @param date 日期
     * @return Integer
     */
    Integer getRechargeUserNumByDate(String date);

    /**
     * 根据时间获取充值用户数量
     * @param startDate 日期
     * @param endDate 日期
     * @return Integer
     */
    Integer getRechargeUserNumByPeriod(String startDate, String endDate);

    Integer getRechargeUserCount(String startTime, String endTime, Integer groupId);

    BigDecimal getRechargeAmount(String startTime, String endTime, Integer groupId);

    /**
     * 修改用户分组ID
     * @param userId
     * @param groupId
     * @return
     */
    Boolean updateGroup(Integer userId,Integer groupId);

    List<UserRecharge> findPendingSuccessOrder();

    Integer getFirstRechargeUserCount(String startTime, String endTime, Integer groupId);

    BigDecimal getFirstRechargeAmount(String startTime, String endTime, Integer groupId);
}
