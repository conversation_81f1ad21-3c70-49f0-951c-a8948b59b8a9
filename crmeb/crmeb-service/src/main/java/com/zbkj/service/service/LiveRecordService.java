package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.vo.MerchantLiveRecordStatistic;

import java.util.List;

public interface LiveRecordService extends IService<LiveRecord> {

    /**
     * 根据roomId找正在直播的记录
     * @param roomId
     * @return
     */
    LiveRecord findLastetStartedByRoomId(Long roomId);

    List<LiveRecord> getList(LiveRecord liveRecord, PageParamRequest pageParamRequest);

    /**
     *正在直播的列表
     * @return
     */
    PageInfo<LiveRecord> getLiveStreamsList(PageParamRequest pageParamRequest, String pullStream, String pullStreamKey);

    MerchantLiveRecordStatistic statisticByMerchant(Integer merchant);

}
