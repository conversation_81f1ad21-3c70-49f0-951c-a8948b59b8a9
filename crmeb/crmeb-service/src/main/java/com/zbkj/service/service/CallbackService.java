package com.zbkj.service.service;

import com.zbkj.common.vo.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 订单支付回调 service

 */
public interface CallbackService {
    /**
     * 微信支付回调
     * @param xmlInfo 微信回调json
     * @return String
     */
    String weChat(String xmlInfo);

    /**
     * 微信退款回调
     * @param request 微信回调json
     * @return String
     */
    String weChatRefund(String request);

    /**
     * ai pay
     * @param aiPayPaymentCallback
     * @return
     */
    Boolean apiPay(AiPayPaymentCallback aiPayPaymentCallback);

    /**
     * m pay
     * @param mPayPaymentCallback
     * @return
     */
    Boolean mPay(MPayPaymentCallback mPayPaymentCallback, HttpServletRequest httpServletRequest);


    Boolean mAgentPay(MPayPaymentCallback callbackVo, HttpServletRequest httpServletRequest);

    /**
     * f pay
     * @param mPayPaymentCallback
     * @return
     */
    Boolean fPay(FPayPaymentCallback mPayPaymentCallback, HttpServletRequest httpServletRequest);
    /**
     * f pay 代付回调
     * @param callback
     * @return
     */
    Boolean fPayAgent(FPayPaymentCallback callback, HttpServletRequest httpServletRequest);


    /**
     * wanb
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean wanbPay(WanbPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * aa
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean aaPay(AAPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * cb pay
     * @param callback
     * @return
     */
    Boolean cbPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * kd pay
     * @param callback
     * @return
     */
    Boolean kdPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * K豆代付回调
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean kdAgentPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * CB代付回调
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean cbAgentPay(CBPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * 博启代付回调
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean bqAgentPay(BQPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * 钻石代付回调
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean dmAgentPay(DMPayPaymentCallback callback, HttpServletRequest httpServletRequest);
    /**
     * aa代付
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean bdtAgentPay(BDTPayPaymentCallback callback, HttpServletRequest httpServletRequest);


    Boolean aaAgentPay(AAAgentPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * 新鹰代付回调
     * @param callback
     * @param httpServletRequest
     * @return
     */
    Boolean xyAgentPay(XYPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    Boolean toPay(ToPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    Boolean okPay(OkPayPaymentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * to代付
     * @param callback
     * @return
     */
    Boolean toAgentPay(ToAgentCallback callback, HttpServletRequest httpServletRequest);

    /**
     * ok代付
     * @param callback
     * @return
     */
    Boolean okAgentPay(OkAgentCallback callback, HttpServletRequest httpServletRequest);

}
