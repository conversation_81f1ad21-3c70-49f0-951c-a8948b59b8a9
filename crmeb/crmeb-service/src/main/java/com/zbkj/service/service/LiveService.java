package com.zbkj.service.service;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.request.AnchorStartBroadcastRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.RoomLikeRequest;
import com.zbkj.common.response.AnchorHomeResponse;
import com.zbkj.common.response.AnchorLoginResponse;
import com.zbkj.common.response.LivePlayRecordResponse;
import com.zbkj.common.response.LiveRecordResponse;

import java.util.List;

public interface LiveService {

    /**
     * 主播登录
     * @param account
     * @param password
     */
    AnchorLoginResponse login(String account, String password);

    /**
     * 主播登出
     */
    boolean logout(Integer merchant);

    /**
     * 首页
     */
    AnchorHomeResponse home(Integer merchant);

    /**
     * 开始直播
     */
    LiveRecordResponse startBroadcast(AnchorStartBroadcastRequest param, Integer merchant);

    /**
     * 结束直播
     */
    boolean endBroadcast(Integer merchant);

    /**
     * 橱窗开关
     * @param merchant
     * @return
     */
    boolean storeWindowSwitch(Integer merchant);

    /**
     * 正在直播的直播间
     * @return
     */
    PageInfo<LiveRecord> roomList(PageParamRequest request);

    /**
     * 首次加载视频
     * @param roomList 排除掉的直播间
     * @param count 加载的数量
     * @return
     */
    List<LiveRecord> loadLive(List<Long> roomList, Integer count);

    /**
     * 官方直播间
     * @return
     */
    LiveRecord officialRoom();

    /**
     * 进入直播间
     * @param roomId
     */
    LivePlayRecordResponse enterRoom(Long roomId);

    /**
     * 退出直播间
     * @param roomId
     */
    void leaveRoom(Long roomId);

    /**
     * 点赞
     * @param request
     */
    void roomLike(RoomLikeRequest request);

    /**
     * 用户退出直播 不是切换直播间 ，需要清理观看记录
     * @param
     */
    void quit(Long roomId);
}
