package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.CertificateResponseType;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.enums.CertificateType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.AliyunFaceVeryifyConfig;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserAuth;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserAuthSearchRequest;
import com.zbkj.common.request.UserAuthSubmitRequest;
import com.zbkj.common.response.UserAuthResponse;
import com.zbkj.common.response.UserAuthSubmitResponse;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.RedisCache;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.UserAuthDao;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 用户实名认证 接口实现类
 */

@Service
@Slf4j
public class UserAuthServiceImpl extends ServiceImpl<UserAuthDao, UserAuth> implements UserAuthService {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private DistributedLockService distributedLockService;

    /**
     * UserAuth列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     */
    @Override
    public PageInfo<UserAuthResponse> getList(UserAuthSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        LambdaQueryWrapper<UserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(request.getAccount())) {
            User user = userService.getUserByAccount(request.getAccount());
            if (user != null) {
                request.setUserId(user.getUid());
            }
        }
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUserId()), UserAuth::getUserId, request.getUserId());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()), UserAuth::getStatus, request.getStatus());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getReviewer()), UserAuth::getReviewer, request.getReviewer());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getCerNo()), UserAuth::getCerNo, request.getCerNo());
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            DateLimitUtilVo dateRage = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(UserAuth::getCreateTime, dateRage.getStartTime(), dateRage.getEndTime());
        }
        lambdaQueryWrapper.orderByDesc(UserAuth::getCreateTime, UserAuth::getUserId);
        List<UserAuth> userAuths = baseMapper.selectList(lambdaQueryWrapper);
        List<Integer> integerList = userAuths.stream().map(userAuth -> userAuth.getUserId()).collect(Collectors.toList());
        Map<Integer, User> integerUserMap;
        if (integerList.size() > 0){
            List<User> userListById = userService.getUserListById(integerList);
            integerUserMap = userListById.stream().collect(Collectors.toMap(user -> user.getUid(), Function.identity()));
        } else {
            integerUserMap = null;
        }
        return CommonPage.copyPageInfo(startPage, userAuths.stream().map(userAuth -> {
            User user = integerUserMap.get(userAuth.getUserId());
            UserAuthResponse userAuthResponse = new UserAuthResponse();
            BeanUtils.copyProperties(userAuth, userAuthResponse);
            if (user != null) {
                userAuthResponse.setAccount(user.getAccount());
            }
            return userAuthResponse;
        }).collect(Collectors.toList()));
    }

    @Override
    public AliyunFaceVeryifyConfig getAliyunFaceVeryifyConfig() {
        AliyunFaceVeryifyConfig aliyunFaceVeryifyConfig = new AliyunFaceVeryifyConfig();
        String aliyunScneId = systemConfigService.getValueByKey("aliyunScneId");
        if (StringUtils.isNotBlank(aliyunScneId)) {
            aliyunFaceVeryifyConfig.setAliyunScneId(Long.parseLong(aliyunScneId));
        }
        aliyunFaceVeryifyConfig.setAliyunProductCode(systemConfigService.getValueByKey("aliyunProductCode"));
        aliyunFaceVeryifyConfig.setAliyunModel(systemConfigService.getValueByKey("aliyunModel"));
        aliyunFaceVeryifyConfig.setAliyunCertType(systemConfigService.getValueByKey("aliyunCertType"));
        aliyunFaceVeryifyConfig.setAliyunReturnUrl(systemConfigService.getValueByKey("aliyunReturnUrl"));
        aliyunFaceVeryifyConfig.setAliyunCallbackUrl(systemConfigService.getValueByKey("aliyunCallbackUrl"));
        aliyunFaceVeryifyConfig.setAliyunType(systemConfigService.getValueByKey("aliyunType"));
        aliyunFaceVeryifyConfig.setAliyunAccessKey(systemConfigService.getValueByKey("aliyunAccessKey"));
        aliyunFaceVeryifyConfig.setAliyunAccessKeySecret(systemConfigService.getValueByKey("aliyunAccessKeySecret"));
        return aliyunFaceVeryifyConfig;
    }

    @Override
    public UserAuth getUserAuthByCertId(String certId, Integer userId) {
        LambdaQueryWrapper<UserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAuth::getCertifyId, certId);
        lambdaQueryWrapper.eq(UserAuth::getUserId, userId);
        lambdaQueryWrapper.last(" limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public UserAuth findApprovedUserAuthByCertNoAndName(String certNo, String name) {
        LambdaQueryWrapper<UserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAuth::getName, name);
        lambdaQueryWrapper.eq(UserAuth::getCerNo, certNo);
        lambdaQueryWrapper.eq(UserAuth::getStatus, CertificateStatus.APPROVED);
        lambdaQueryWrapper.last(" limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public UserAuth getState(Integer userId) {
        LambdaQueryWrapper<UserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAuth::getUserId, userId);
        lambdaQueryWrapper.orderByDesc(UserAuth::getCreateTime);
        lambdaQueryWrapper.last(" limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetAuthenticationStatus() {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 获取当前时间前十分钟的时间
        LocalDateTime tenMinutesAgo = currentTime.minus(10, ChronoUnit.MINUTES);
        LambdaQueryWrapper<UserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.le(UserAuth::getCreateTime, tenMinutesAgo);
        lambdaQueryWrapper.eq(UserAuth::getStatus, CertificateStatus.AUDITING);
        lambdaQueryWrapper.in(UserAuth::getType, CertificateType.MAINLAND_IDENTITY_CARD, CertificateType.HONGKONG_IDENTITY_CARD_OR_PASSPORT, CertificateType.MACAO_IDENTITY_CARD_OR_PASSPORT, CertificateType.TAIWAN_IDENTITY_CARD_OR_PASSPORT);
        lambdaQueryWrapper.last(" limit 100");
        List<UserAuth> userAuthList = baseMapper.selectList(lambdaQueryWrapper);
        for (UserAuth userAuth : userAuthList) {
            User user = userService.getInfoByUid(userAuth.getUserId());
            if (user != null && !user.getAuthenticationStatus().equals(CertificateStatus.APPROVED)) {
                user.setAuthenticationStatus(CertificateStatus.REJECTED);
                userService.updateById(user);
            }
            userAuth.setStatus(CertificateStatus.REJECTED);
            userAuth.setResult("认证超时");
            userAuth.setReviewer("系统");
            userAuth.setReviewTime(new Date());
            baseMapper.updateById(userAuth);
        }
        return true;
    }

    @Override
    public boolean clean() {
        LambdaQueryWrapper<UserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAuth::getStatus, CertificateStatus.APPROVED);
        lambdaQueryWrapper.ge(UserAuth::getUserId, BigDecimal.ZERO);
        return baseMapper.delete(lambdaQueryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserAuthSubmitResponse submitAuth(UserAuthSubmitRequest request, String clientIp) {
        // 获取 userId
        Integer userId = request.getUserId();
        if (userId == null) {
            log.error("用户ID不能为空");
            throw new CrmebException("用户ID不能为空");
        }

        // 查询用户
        User user = userService.getInfoByUid(userId);
        if (user == null) {
            log.error("用户数据异常，uid: {}", userId);
            throw new CrmebException("用户数据异常");
        }

        // 使用Redis分布式锁保护用户数据更新
        return distributedLockService.executeWithLock(Constants.USER_LOCK_PREFIX + user.getUid(), () -> {
            // 检查认证状态
            if (user.getAuthenticationStatus() == CertificateStatus.APPROVED) {
                log.info("用户已认证，uid: {}, cerNo: {}", user.getUid(), request.getCerNo());
                throw new CrmebException("已认证成功，无需再次认证！");
            } else if (user.getAuthenticationStatus() == CertificateStatus.AUDITING) {
                log.info("用户认证审核中，uid: {}, cerNo: {}", user.getUid(), request.getCerNo());
                throw new CrmebException("正在审核中，无需再次提交！");
            }

            // 检查证件
            UserAuth auth = findApprovedUserAuthByCertNoAndName(request.getCerNo(), request.getName());
            if (auth != null) {
                log.info("证件已认证，uid: {}, cerNo: {}, name: {}", user.getUid(), request.getCerNo(), request.getName());
                throw new CrmebException("该证件已完成认证！");
            }

            // 构建 UserAuth
            UserAuth userAuth = buildUserAuth(user.getUid(), request, clientIp);
            UserAuthSubmitResponse response = new UserAuthSubmitResponse();

            // 认证成功
            response.setMsg("认证成功！");
            response.setType(CertificateResponseType.FINISH);
            userAuth.setStatus(CertificateStatus.APPROVED);
            save(userAuth);
            user.setRealName(userAuth.getName());
            user.setAuthenticationStatus(CertificateStatus.APPROVED);
            userService.updateById(user);

            log.info("认证成功，uid: {}, cerNo: {}, ip: {}", user.getUid(), request.getCerNo(), clientIp);
            return response;
        });
    }

    private UserAuth buildUserAuth(Integer userId, UserAuthSubmitRequest request, String clientIp) {
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(userId);
        userAuth.setType(request.getType());
        userAuth.setName(request.getName());
        userAuth.setCerNo(request.getCerNo());
        userAuth.setCerPic(systemAttachmentService.clearPrefix(request.getCerPic()));
        userAuth.setCreateTime(new Date());
        userAuth.setStatus(CertificateStatus.AUDITING);
        userAuth.setIp(clientIp);
        try {
            String metaInfo = request.getMetaInfo();
            JSONObject jsonObject = JSON.parseObject(metaInfo);
            String deviceType = jsonObject.getString("deviceType");
            userAuth.setDeviceType(deviceType);
        } catch (Exception e) {
            userAuth.setDeviceType("unknown");
        }
        return userAuth;
    }

}
