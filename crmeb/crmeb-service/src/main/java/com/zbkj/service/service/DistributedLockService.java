package com.zbkj.service.service;

import java.util.function.Supplier;

/**
 * 分布式锁服务接口
 * 统一管理各种分布式锁操作
 * 
 * <AUTHOR>
 */
public interface DistributedLockService {

    /**
     * 执行带锁的操作
     * 
     * @param lockKey 锁键
     * @param operation 需要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    <T> T executeWithLock(String lockKey, Supplier<T> operation);

    /**
     * 执行带锁的操作（无返回值）
     * 
     * @param lockKey 锁键
     * @param operation 需要执行的操作
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    void executeWithLock(String lockKey, Runnable operation);

}
