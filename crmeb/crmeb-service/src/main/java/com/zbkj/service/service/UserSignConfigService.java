package com.zbkj.service.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.UserSignConfig;
import com.zbkj.common.request.UserSignConfigSearchRequest;

/**
 * 用户签到配置 业务接口
 */
public interface UserSignConfigService extends IService<UserSignConfig> {
    /**
     * UserSignConfig 列表查询
     * @param request 查询条件对象
     * @return
     */
    PageInfo<UserSignConfig> getList(UserSignConfigSearchRequest request);

    /**
     * 根据等级获取配置
     * @param level
     * @return
     */
    UserSignConfig getConfigByLevel(Integer level);
}

