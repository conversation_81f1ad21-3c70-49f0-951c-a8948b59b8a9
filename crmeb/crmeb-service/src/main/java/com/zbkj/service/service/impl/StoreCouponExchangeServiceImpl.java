package com.zbkj.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.coupon.StoreCoupon;
import com.zbkj.common.model.coupon.StoreCouponExchange;
import com.zbkj.common.request.StoreCouponExchangeAddRequest;
import com.zbkj.common.request.StoreCouponExchangeSearchRequest;
import com.zbkj.common.utils.ExchangeCodeGenerator;
import com.zbkj.service.dao.StoreCouponExchangeDao;
import com.zbkj.service.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * StoreCouponServiceImpl 接口实现

 */
@Service
public class StoreCouponExchangeServiceImpl extends ServiceImpl<StoreCouponExchangeDao, StoreCouponExchange> implements StoreCouponExchangeService {

    @Override
    public List<StoreCouponExchange> getList(StoreCouponExchangeSearchRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());
        //带 StoreCoupon 类的多条件查询
        LambdaQueryWrapper<StoreCouponExchange> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreCouponExchange::getPid, request.getPid());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(request.getCode()),StoreCouponExchange::getCode, request.getCode());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()),StoreCouponExchange::getStatus, request.getStatus());
        lambdaQueryWrapper.orderByDesc(StoreCouponExchange::getPickupTime);
        return this.list(lambdaQueryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean create(Integer pid,Integer num) {
        if (Objects.isNull(pid)) {
            throw new CrmebException("请提供有效的优惠券ID！");
        }
        if (Objects.isNull(num) || num <= 0) {
            num = 1000;
        }
        List<StoreCouponExchange> storeCouponExchangeList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            StoreCouponExchange storeCouponExchange = new StoreCouponExchange();
            storeCouponExchange.setPid(pid);
            String exchangeCode = ExchangeCodeGenerator.generateExchangeCode(20);
            storeCouponExchange.setCode(exchangeCode);
            storeCouponExchange.setCreateTime(new Date());
            storeCouponExchangeList.add(storeCouponExchange);
        }
        return this.saveBatch(storeCouponExchangeList);
    }

    @Override
    public Boolean delete(Integer pid) {
        if (Objects.isNull(pid)) {
            throw new CrmebException("请提供有效的优惠券ID！");
        }
        LambdaQueryWrapper<StoreCouponExchange> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreCouponExchange::getPid, pid);
        return this.remove(lambdaQueryWrapper);
    }

    @Override
    public StoreCouponExchange getByCode(String code) {
        return this.getOne(new LambdaQueryWrapper<StoreCouponExchange>().eq(StoreCouponExchange::getCode,code).last("limit 1"));
    }
}

