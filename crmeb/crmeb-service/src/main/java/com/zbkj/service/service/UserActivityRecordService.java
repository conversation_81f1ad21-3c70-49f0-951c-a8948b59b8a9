package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.ActivitySwitch;
import com.zbkj.common.model.user.UserActivityRecord;
import com.zbkj.common.request.UserActivityRecordRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户活动记录表 业务接口
 */
public interface UserActivityRecordService extends IService<UserActivityRecord> {

    /**
     * 根据参数查询
     * @param request
     * @return
     */
    PageInfo<UserActivityRecord> getList(UserActivityRecordRequest request);

    ActivitySwitch activityStatus();

    /**
     * 红包统计
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal hongBaoTotal(String startTime,String endTime,Integer groupId);
}

