package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.model.user.UserBalanceInterestReport;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.service.dao.UserBalanceInterestReportDao;
import com.zbkj.service.service.UserBalanceInterestReportService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * UserBillServiceImpl 接口实现

 */
@Service
public class UserBalanceInterestReportServiceImpl extends ServiceImpl<UserBalanceInterestReportDao, UserBalanceInterestReport> implements UserBalanceInterestReportService {
    @Override
    public BigDecimal getBalanceInterest(String date) {
        LambdaQueryWrapper<UserBalanceInterestReport> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserBalanceInterestReport::getCreateDate,date);
        List<UserBalanceInterestReport> list = list(queryWrapper);
        if (list == null){
            return BigDecimal.ZERO;
        }
        return list.stream().map(UserBalanceInterestReport::getTotalInterest).reduce(BigDecimal.ZERO,BigDecimal::add);
    }

    @Override
    public BigDecimal getBalanceInterest(String startTime, String endTime,Integer groupId) {
        startTime = DateUtil.formatToYearMonthDay(startTime);
        endTime = DateUtil.formatToYearMonthDay(endTime);
        QueryWrapper<UserBalanceInterestReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge(StringUtils.isNotEmpty(startTime),"create_date",startTime);
        queryWrapper.le(StringUtils.isNotEmpty(endTime),"create_date",endTime);
        queryWrapper.select("sum(total_interest) as totalInterest");
        queryWrapper.eq(Objects.nonNull(groupId),"group_id",groupId);
        List<Object> list = baseMapper.selectObjs(queryWrapper);
        if (list != null && list.size() > 0 && list.get(0) != null){
            return new BigDecimal(list.get(0).toString());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public List<UserBalanceInterestReport> getList(Integer days) {
        LambdaQueryWrapper<UserBalanceInterestReport> queryWrapper = Wrappers.lambdaQuery();
        LocalDate currentDate = LocalDate.now();
        LocalDate daysAgo = currentDate.minusDays(days);
        queryWrapper.ge(UserBalanceInterestReport::getCreateDate,daysAgo);
        queryWrapper.orderByAsc(UserBalanceInterestReport::getId);
        List<UserBalanceInterestReport> list = list(queryWrapper);

        // 使用一个 Map 来存储相同 createDate 的 totalInterest 的总和
        Map<LocalDate, UserBalanceInterestReport> hashMap = new HashMap<>();
        // 遍历原始数据列表，计算相同 createDate 的 totalInterest 的总和
        for (UserBalanceInterestReport report : list) {
            LocalDate createDate = report.getCreateDate();
            BigDecimal totalInterest = report.getTotalInterest();

            // 如果 Map 中已经有了相同 createDate 的记录，则累加总和
            if (hashMap.containsKey(createDate)) {
                report.setTotalInterest(hashMap.get(createDate).getTotalInterest().add(totalInterest));
                hashMap.put(createDate, report);
            } else {
                hashMap.put(createDate, report);
            }
        }
        // 创建新的列表，将相同 createDate 的 totalInterest 组合成一条数据
        List<UserBalanceInterestReport> combinedList = new ArrayList<>();
        for (Map.Entry<LocalDate, UserBalanceInterestReport> entry : hashMap.entrySet()) {
            // 将新的对象添加到新的列表中
            combinedList.add(entry.getValue());
        }
        return combinedList;
    }

    @Override
    public Boolean hasProcessedBalanceInterestToday() {
        LambdaQueryWrapper<UserBalanceInterestReport> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserBalanceInterestReport::getCreateDate,LocalDate.now());
        return count(queryWrapper) > 0;
    }
}

