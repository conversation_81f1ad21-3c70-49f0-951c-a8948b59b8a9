package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.constants.PayConstants;
import com.zbkj.common.constants.TaskConstants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.combination.StoreCombination;
import com.zbkj.common.model.combination.StorePink;
import com.zbkj.common.model.finance.UserRecharge;
import com.zbkj.common.model.order.StoreOrder;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserToken;
import com.zbkj.common.model.wechat.WechatPayInfo;
import com.zbkj.common.response.AiPayCreatePaymentResponse;
import com.zbkj.common.utils.*;
import com.zbkj.common.vo.*;
import com.zbkj.service.service.*;
import com.zbkj.service.util.OkHttpUtils;
import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


/**
 * 微信支付

 */
@Data
@Service
public class AiPayServiceImpl implements AiPayService {
    private static final Logger logger = LoggerFactory.getLogger(AiPayServiceImpl.class);
    @Autowired
    private SystemConfigService systemConfigService;
    @Override
    public AiPayCreatePaymentResponse request(String price,String orderId) {
        String aiUrl = systemConfigService.getValueByKeyException("aiUrl");
        String aiMerchantId = systemConfigService.getValueByKeyException("aiMerchantId");
        String aiApiKey = systemConfigService.getValueByKeyException("aiApiKey");
        String aiReqBackUrl = systemConfigService.getValueByKeyException("aiReqBackUrl");

        //ai pay参数组装
        Map<String, Object> stringStringMap = new HashMap<>();
        stringStringMap.put("orderAmount", price);
        stringStringMap.put("callbackUrl", aiReqBackUrl);
        stringStringMap.put("merchantId", aiMerchantId);
        stringStringMap.put("outOrderNo",orderId);

        // 构建参数字符串
        String parameters = "callbackUrl=" + stringStringMap.get("callbackUrl") +
                "&merchantId=" + aiMerchantId +
                "&orderAmount=" + stringStringMap.get("orderAmount") +
                "&outOrderNo=" + stringStringMap.get("outOrderNo") +
                "&apiKey=" + aiApiKey;

        String sign = DigestUtils.md5Hex(parameters);
        stringStringMap.put("sign", sign);

        try {
            String result = new OkHttpUtils().post(aiUrl + "/trade/ai-pay/api/V2/payment/create", stringStringMap);
            if (StringUtils.isNotEmpty(result) && result.contains("code")) {
                AiPayCreatePaymentResponse aiPayCreatePaymentResponse = JSONObject.parseObject(result, AiPayCreatePaymentResponse.class);
                if ("0".equals(aiPayCreatePaymentResponse.getCode())) {
                    if ("CREATE".equals(aiPayCreatePaymentResponse.getData().getStatus())) {
                        return aiPayCreatePaymentResponse;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("API PAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }
}
