package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.UserAccessList;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserAccessListSearchRequest;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.UserAccessListDao;
import com.zbkj.service.service.UserAccessListService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 前台IP或国家地区名单 接口实现类
 */

@Service
public class UserAccessListServiceImpl extends ServiceImpl<UserAccessListDao, UserAccessList> implements UserAccessListService {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * UserAccessList列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserAccessList> getList(UserAccessListSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserAccessList 类的多条件查询
        LambdaQueryWrapper<UserAccessList> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(StringUtils.isNotBlank(request.getRegion()), UserAccessList::getRegion, request.getRegion());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getType()), UserAccessList::getType, request.getType());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()), UserAccessList::getStatus, request.getStatus());
        lambdaQueryWrapper.orderByDesc(UserAccessList::getId);
        return CommonPage.copyPageInfo(startPage, baseMapper.selectList(lambdaQueryWrapper));
    }

    @Override
    public UserAccessList getByRegion(String region) {
        LambdaQueryWrapper<UserAccessList> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAccessList::getRegion, region);
        lambdaQueryWrapper.last(" limit 1");
        UserAccessList userAccessList = baseMapper.selectOne(lambdaQueryWrapper);
        return userAccessList;
    }

    @PostConstruct
    public void init() {
        try {
            redisUtil.deleteKeysWithPrefix(Constants.USER_ACCESS_IP_TEMP);
            redisUtil.deleteKeysWithPrefix(Constants.USER_ACCESS_IP_LIST);
            redisUtil.deleteKeysWithPrefix(Constants.USER_ACCESS_REGION_LIST);
            redisUtil.deleteKeysWithPrefix(Constants.USER_DENY_IP_TEMP);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        LambdaQueryWrapper<UserAccessList> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAccessList::getStatus, true);
        List<UserAccessList> userAccessLists = baseMapper.selectList(lambdaQueryWrapper);
        if (!userAccessLists.isEmpty() && userAccessLists.size() > 0){
            List<UserAccessList> collect = userAccessLists.stream().filter(userAccessList -> CrmebUtil.checkIsIp(userAccessList.getRegion())).collect(Collectors.toList());
            for (UserAccessList userAccessList : collect) {
                redisUtil.set(Constants.USER_ACCESS_IP_LIST+userAccessList.getRegion(),JSON.toJSONString(userAccessList));
            }
            List<UserAccessList> collect1 = userAccessLists.stream().filter(userAccessList -> !CrmebUtil.checkIsIp(userAccessList.getRegion())).collect(Collectors.toList());
            for (UserAccessList userAccessList : collect1) {
                redisUtil.set(Constants.USER_ACCESS_REGION_LIST+userAccessList.getRegion(), JSON.toJSONString(userAccessList));
            }

        }
    }
}
