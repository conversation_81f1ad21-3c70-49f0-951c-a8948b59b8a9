package com.zbkj.service.service;

import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.response.*;


/**
 * 微信支付

 */
public interface PayService {
    /**
     * 发起支付请求
     * @param price
     * @param orderId
     * @return
     */
    MPayCreatePaymentResponse mPayRequest(String price, String orderId);

    MPayCreatePaymentResponse mPayAgentRequest(String price, String orderId, String address);

    FPayCreatePaymentResponse fPayRequest(String price, String orderId);

    WanbPayCreatePaymentResponse wanbPayRequest(String price, String orderId);

    /**
     * FY 支付请求
     * @param price
     * @param orderId
     * @return
     */
    Boolean fyPayRequest(String price, String orderId);

    /**
     * CBPAY
     * @param price
     * @param orderId
     * @return
     */
    CBPayCreatePaymentResponse cbPayRequest(String price, String orderId);

    /**
     * CBPAY
     * @param price
     * @param orderId
     * @return
     */
    CBPayCreatePaymentResponse kdPayRequest(String price, String orderId);

    /**
     * AA钱包
     * @param price
     * @param orderId
     * @return
     */
    AAPayCreatePaymentResponse aaPayRequest(String price, String orderId);

    /**
     * FPay代付
     * @param price
     * @param orderId
     * @param address 钱包地址
     * @return
     */
    FPayCreatePaymentResponse fPayAgentRequest(String price, String orderId,String address);

    /**
     * k豆代付
     * @param price
     * @param orderId
     * @return
     */
    CBPayAgentPaymentResponse kdAgentPayRequest(String price, String orderId, String address);


    /**
     * CB代付
     * @param price
     * @param orderId
     * @return
     */
    CBPayAgentPaymentResponse cbAgentPayRequest(String price, String orderId, String address);
    /**
     * 博启代付
     * @return
     */
    BQPayAgentPaymentResponse bqAgentPayRequest(UserExtract userExtract);

    /**
     * 钻石代付
     * @param userExtract
     * @return
     */
    DMAgentPaymentResponse dmAgentPayRequest(UserExtract userExtract);

    /**
     * 八达通代付
     * @param userExtract
     * @return
     */
    BDTPayAgentPaymentResponse bdtAgentPayRequest(UserExtract userExtract);
     /* aa代付
     * aa代付
     * @param price
     * @param orderId
     * @param address
     * @return
     */
    AAPayWithdrawalPaymentResponse aaAgentPayRequest(String price, String orderId, String address);

    /**
     * 新鹰代付
     * @param userExtract
     * @return
     */
    XYPayAgentPaymentResponse xyAgentPayRequest(UserExtract userExtract);

    /**
     * OKPAY
     * @param price
     * @param orderId
     * @return
     */
    ToPayCreatePaymentResponse toPayRequest(String price, String orderId);

    /**
     * TOPAY
     * @param price
     * @param orderId
     * @return
     */
    OkPayCreatePaymentResponse okPayRequest(String price, String orderId);

    /**
     * TO代付
     * @param userExtract
     * @return
     */
    ToAgentResponse toAgentRequest(UserExtract userExtract, String orderId);

    /**
     * OK代付
     * @param userExtract
     * @return
     */
    OkAgentResponse okAgentRequest(UserExtract userExtract, String orderId);

    //处理mpay 主动查询
    String checkOrder(String apiPath, String type) throws Exception;
}
