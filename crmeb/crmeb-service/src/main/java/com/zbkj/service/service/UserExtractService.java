package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.user.User;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserExtractUpdateRequest;
import com.zbkj.common.response.UserExtractRecordResponse;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.request.UserExtractRequest;
import com.zbkj.common.request.UserExtractSearchRequest;
import com.zbkj.common.response.BalanceResponse;
import com.zbkj.common.response.UserExtractResponse;

import java.math.BigDecimal;
import java.util.List;

/**
* UserExtractService 接口

*/
public interface UserExtractService extends IService<UserExtract> {

    List<UserExtract> getList(UserExtractSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 提现总金额
     */
    BalanceResponse getBalance(String dateLimit,Integer groupId);

    /**
     * 提现总金额
     * <AUTHOR>
     * @since 2020-05-11
     * @return BalanceResponse
     */
    BigDecimal getWithdrawn(String startTime,String endTime,Integer groupId);

    UserExtractResponse getUserExtractByUserId(Integer userId);

    /**
     * 提现审核
     * @param userExtract
     * @param status 审核状态 -1 未通过 0 审核中 1 已提现
     * @param backMessage   驳回原因
     * @return  审核结果
     */
    Boolean updateStatus(UserExtract userExtract,Integer status,String backMessage);

    /**
     * 提现审核 处理余额提现
     * @param userExtract
     * @param status
     * @param backMessage
     * @return
     */
    Boolean updateStatusAccountBalance(UserExtract userExtract,Integer status,String backMessage);

    /**
     * 提现审核 商户处理
     * @param userExtract
     * @param status
     * @return
     */
    Boolean updateStatusMerchantBalance(UserExtract userExtract,Integer status,String backMessage);

    /**
     * 获取提现记录列表
     * @param userId 用户uid
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<UserExtractRecordResponse> getExtractRecord(Integer userId,Integer queryType,PageParamRequest pageParamRequest);

    BigDecimal getExtractTotalMoney(Integer userId,Integer type,Integer groupId);

    /**
     * 提现申请
     * @return Boolean
     */
    Boolean extractApply(UserExtractRequest request, User user,String account);

    /**
     * 提现申请账户余额
     * @param request
     * @return
     */
    Boolean extractApplyAccountBalance(UserExtractRequest request,User user,String account);

    /**
     * 修改提现申请
     * @param id 申请id
     * @param userExtractRequest 具体参数
     */
    Boolean updateExtract(Integer id, UserExtractUpdateRequest userExtractRequest);

    /**
     * 提现申请待审核数量
     * @return Integer
     */
    Integer getNotAuditNum();

    /**
     * 根据uid和type查询对象 最新一条
     * @param uid
     * @param type
     * @return
     */
    UserExtract getByUidAndType(Integer uid,Integer type);

    /**
     * 修改用户组
     * @param userId
     * @param groupId
     * @return
     */
    Boolean updateGroup(Integer userId,Integer groupId);

    /**
     * 根据订单ID查询
     * @param orderId
     * @return
     */
    UserExtract getByOrderId(String orderId);

    List<UserExtract> findPendingOrder();

    List<UserExtract> findPendingOrder(int page, int pageSize);

    Integer getWithdrawUserCount(String startTime, String endTime, Integer groupId);

}
