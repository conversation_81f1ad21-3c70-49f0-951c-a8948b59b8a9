package com.zbkj.service.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.ProductLikeConfig;
import com.zbkj.common.request.PageParamRequest;

import java.util.List;
import java.util.Map;

/**
 * 商品点赞配置 业务接口
 */
public interface ProductLikeConfigService extends IService<ProductLikeConfig> {

    /**
     * ProductLikeConfig 列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<ProductLikeConfig> getList(PageParamRequest request);

    /**
     * 初始化缓存数据
     */
    void init();

    /**
     * 获取商品配置
     * @param id
     * @return
     */
    ProductLikeConfig getProductConfig(Integer id);
}

