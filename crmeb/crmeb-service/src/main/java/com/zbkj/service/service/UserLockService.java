package com.zbkj.service.service;

import java.util.function.Supplier;

/**
 * 用户锁服务接口
 * 统一管理用户相关的分布式锁操作
 *
 * <AUTHOR>
 */
public interface UserLockService {

    /**
     * 执行带用户锁的操作
     *
     * @param userId 用户ID
     * @param operation 需要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    <T> T executeWithUserLock(Integer userId, Supplier<T> operation);

    /**
     * 执行带用户锁的操作（带锁类型标识）
     *
     * @param userId 用户ID
     * @param lockType 锁类型标识（用于日志和监控）
     * @param operation 需要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    <T> T executeWithUserLock(Integer userId, String lockType, Supplier<T> operation);

    /**
     * 执行带用户锁的操作（无返回值）
     * 
     * @param userId 用户ID
     * @param operation 需要执行的操作
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    void executeWithUserLock(Integer userId, Runnable operation);

    /**
     * 尝试获取用户锁
     * 
     * @param userId 用户ID
     * @param requestId 请求ID（用于释放锁时验证）
     * @param timeoutSeconds 锁超时时间（秒）
     * @return 是否成功获取锁
     */
    boolean tryLockUser(Integer userId, String requestId, long timeoutSeconds);

    /**
     * 释放用户锁
     * 
     * @param userId 用户ID
     * @param requestId 请求ID（必须与获取锁时的requestId一致）
     * @return 是否成功释放锁
     */
    boolean unlockUser(Integer userId, String requestId);

    /**
     * 生成锁的键名
     * 
     * @param userId 用户ID
     * @return 锁的键名
     */
    String getUserLockKey(Integer userId);

    /**
     * 生成请求ID
     * 
     * @return 唯一的请求ID
     */
    String generateRequestId();
}
