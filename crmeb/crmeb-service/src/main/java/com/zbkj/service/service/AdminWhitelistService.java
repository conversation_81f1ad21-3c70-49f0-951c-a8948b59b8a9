package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.AdminWhitelist;
import com.zbkj.common.request.AdminWhitelistSearchRequest;

/**
 * 后台日志记录 业务接口
 */
public interface AdminWhitelistService extends IService<AdminWhitelist> {

    /**
     * AdminWhitelist 列表查询
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<AdminWhitelist> getList(AdminWhitelistSearchRequest request);

    void init();

    /**
     * 删除IP
     * @return
     */
    boolean deleteWithCache(Integer id);

    /**
     * 删除IP缓存
     * @param ip
     * @return
     */
    boolean deleteIPCache(String ip);
}

