package com.zbkj.service.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.InvestItems;
import com.zbkj.common.request.InvestItemsResponse;
import com.zbkj.common.request.InvestItemsSearchRequest;

import java.math.BigDecimal;

/**
 *  业务接口
 */
public interface InvestItemsService extends IService<InvestItems> {
    /**
     * InvestItems 列表查询
     * @param request 查询条件对象
     * @return
     */
    PageInfo<InvestItems> getList(InvestItemsSearchRequest request);

    /**
     * 查询列表前台使用
     * @param request
     * @return
     */
    PageInfo<InvestItemsResponse> getListResponse(InvestItemsSearchRequest request);

    /**
     * 根据id获取项目
     * @param id
     * @return
     */
    InvestItemsResponse getItems(Integer id);

    /**
     * 根据项目ID获取
     * @param id
     * @return
     */
    InvestItems getByItemsId(Integer id);

    /**
     * 跟新项目缓存
     * @param
     * @return
     */
    Boolean updateByItemInvest(Integer id, BigDecimal amount);
    /**
     * 初始化缓存
     * @return
     */
    void init();
}

