package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.UserExclusive;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserExclusiveSearchRequest;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.UserExclusiveDao;
import com.zbkj.service.service.UserExclusiveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 专属用户 接口实现类
 */

@Service
public class UserExclusiveServiceImpl extends ServiceImpl<UserExclusiveDao, UserExclusive> implements UserExclusiveService {

    @Resource
    private RedisUtil redisUtil;

    /**
     * UserExclusive列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserExclusive> getList(UserExclusiveSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserExclusive 类的多条件查询
        LambdaQueryWrapper<UserExclusive> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getName()), UserExclusive::getName, request.getName());
        lambdaQueryWrapper.orderByAsc(UserExclusive::getId);
        return CommonPage.copyPageInfo(startPage, baseMapper.selectList(lambdaQueryWrapper));
    }

    @Override
    public void init() {
        List<UserExclusive> list = this.list();
        try {
            redisUtil.deleteKeysWithPrefix(Constants.USER_EXCLUSIVE_SETTING);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        for (UserExclusive userExclusive : list) {
            redisUtil.set(Constants.USER_EXCLUSIVE_SETTING + userExclusive.getId(), JSON.toJSONString(userExclusive));
        }
    }

    @Override
    public UserExclusive getInfoById(Long id) {
        Object object = redisUtil.get(Constants.USER_EXCLUSIVE_SETTING + id);
        if (object == null){
            UserExclusive userExclusive = this.getById(id);
            if (userExclusive != null){
                redisUtil.set(Constants.USER_EXCLUSIVE_SETTING + userExclusive.getId(), JSON.toJSONString(userExclusive));
                return userExclusive;
            }
            return null;
        }
        return JSON.parseObject(object.toString(),UserExclusive.class);
    }

}
