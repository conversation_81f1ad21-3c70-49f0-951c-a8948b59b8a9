package com.zbkj.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zbkj.common.model.InvestItems;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 *  DAO 映射层
 */
public interface InvestItemsDao extends BaseMapper<InvestItems> {
    @Update("UPDATE eb_invest_items SET invested_amount = invested_amount + #{amount} WHERE id = #{id}")
    boolean incrementInvestedAmount(@Param("id") Integer id, @Param("amount") BigDecimal amount);

}
