package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.ProductLikeConfig;
import com.zbkj.common.model.ProductLikeRecord;
import com.zbkj.common.request.ProductLikeRecordSearchRequest;

import java.math.BigDecimal;

/**
 * 商品点赞记录 业务接口
 */
public interface ProductLikeRecordService extends IService<ProductLikeRecord> {

    /**
     * ProductLikeRecord 列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<ProductLikeRecord> getList(ProductLikeRecordSearchRequest request);

    /**
     * 今日商品的点赞次数
     * @param productId
     * @param userId
     * @return
     */
    Integer likeCount(Integer productId, Integer userId, ProductLikeConfig productLikeConfig);

    /**
     * 获取上次点赞信息
     * @param productId
     * @param userId
     * @return
     */
    ProductLikeRecord lastLikeInfo(Integer productId,Integer userId);

    /**
     * 检查今日的奖励
     * @param productId
     * @param userId
     * @return
     */
    Boolean awad(Integer productId, Integer userId, ProductLikeConfig productLikeConfig);

    /**
     * 获取今日已奖励多少人
     * @param productId
     * @return
     */
    Integer awadTodayCount(Integer productId,ProductLikeConfig productLikeConfig);

    /**
     * 统计余额
     * @param startTime
     * @param endTime
     * @param groupId
     * @return
     */
    BigDecimal totalLikeBalance(String startTime,String endTime,Integer groupId);


}

