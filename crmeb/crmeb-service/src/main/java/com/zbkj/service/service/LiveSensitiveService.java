package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.LiveSensitive;
import com.zbkj.common.request.LiveSensitiveSearchRequest;

/**
 * 直播间敏感词 业务接口
 */
public interface LiveSensitiveService extends IService<LiveSensitive> {

    /**
     * LiveSensitive 列表查询
     *
     * @param request 默认是是体类 根据自己需求修改或者创建自己的request
     * @return
     */
    PageInfo<LiveSensitive> getList(LiveSensitiveSearchRequest request);

    /**
     * 根据关键词查询
     * @param word
     * @return
     */
    LiveSensitive getByWords(String word);
}

