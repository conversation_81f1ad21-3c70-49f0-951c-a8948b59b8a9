package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.SystemGroupDataCardModel;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.constants.SysGroupDataConstants;
import com.zbkj.common.request.SystemFormItemCheckRequest;
import com.zbkj.common.request.SystemGroupDataRequest;
import com.zbkj.common.request.SystemGroupDataSearchRequest;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.model.system.SystemGroupData;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.SystemGroupDataDao;
import com.zbkj.service.service.SystemAttachmentService;
import com.zbkj.service.service.SystemFormTempService;
import com.zbkj.service.service.SystemGroupDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static cn.hutool.core.bean.BeanUtil.setFieldValue;

/**
 * SystemGroupDataServiceImpl 接口实现

 */
@Service
public class SystemGroupDataServiceImpl extends ServiceImpl<SystemGroupDataDao, SystemGroupData> implements SystemGroupDataService {

    @Resource
    private SystemGroupDataDao dao;

    @Autowired
    private SystemFormTempService systemFormTempService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private RedisUtil redisUtil;

    /**
    * 列表
    * @param request 请求参数
    * @param pageParamRequest 分页类参数
    * @return List<SystemGroupData>
    */
    @Override
    public List<SystemGroupData> getList(SystemGroupDataSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        //带 SystemGroupData 类的多条件查询
        LambdaQueryWrapper<SystemGroupData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        SystemGroupData model = new SystemGroupData();
        BeanUtils.copyProperties(request, model);
        lambdaQueryWrapper.setEntity(model);
        lambdaQueryWrapper.orderByAsc(SystemGroupData::getSort).orderByAsc(SystemGroupData::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    public List<SystemGroupData> getList(SystemGroupDataSearchRequest request,List<Integer> integerList) {
        //带 SystemGroupData 类的多条件查询
        LambdaQueryWrapper<SystemGroupData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        SystemGroupData model = new SystemGroupData();
        BeanUtils.copyProperties(request, model);
        lambdaQueryWrapper.setEntity(model);
        lambdaQueryWrapper.orderByAsc(SystemGroupData::getSort).orderByAsc(SystemGroupData::getId);
        if (integerList!=null && !integerList.isEmpty()){
            lambdaQueryWrapper.in(SystemGroupData::getId,integerList);
        }
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 修改组合数据详情表
     * @param id integer id
     * @param request 修改参数
     * @return bool
     */
    @Override
    public Boolean update(Integer id, SystemGroupDataRequest request) {
        //检测form表单，并且返回需要添加的数据
        systemFormTempService.checkForm(request.getForm());

        SystemGroupData systemGroupData = new SystemGroupData();
        systemGroupData.setId(id);
        systemGroupData.setGid(request.getGid());

        String value = JSONObject.toJSONString(request.getForm());
        value = systemAttachmentService.clearPrefix(value);
        systemGroupData.setValue(value);
        systemGroupData.setSort(request.getForm().getSort());
        systemGroupData.setStatus(request.getForm().getStatus());
        redisUtil.delete(Constants.SYSTEM_GROUP_DATA_TYPE_1 + request.getGid());
        return updateById(systemGroupData);
    }

    /**
     * 保存数据
     * @param systemGroupDataRequest SystemGroupDataRequest 数据保存
     * @return Boolean
     */
    @Override
    public Boolean create(SystemGroupDataRequest systemGroupDataRequest) {
        //检测form表单，并且返回需要添加的数据
        systemFormTempService.checkForm(systemGroupDataRequest.getForm());

        SystemGroupData systemGroupData = new SystemGroupData();
        systemGroupData.setGid(systemGroupDataRequest.getGid());

        String value = JSONObject.toJSONString(systemGroupDataRequest.getForm());
        value = systemAttachmentService.clearPrefix(value);
        systemGroupData.setValue(value);
        systemGroupData.setSort(systemGroupDataRequest.getForm().getSort());
        systemGroupData.setStatus(systemGroupDataRequest.getForm().getStatus());
        return save(systemGroupData);
    }

    /**
     * 通过gid获取列表 推荐二开使用
     * @param gid Integer group id
     * @return List<T>
     */
    @Override
    public <T> List<T> getListByGid(Integer gid, Class<T> cls) {
        SystemGroupDataSearchRequest systemGroupDataSearchRequest = new SystemGroupDataSearchRequest();
        systemGroupDataSearchRequest.setGid(gid);
        systemGroupDataSearchRequest.setStatus(true);
        List<SystemGroupData> list = getList(systemGroupDataSearchRequest, new PageParamRequest());

        List<T> arrayList = new ArrayList<>();
        if (list.size() < 1) {
            return null;
        }

        for (SystemGroupData systemGroupData : list) {
            JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
            List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = CrmebUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
            if (systemFormItemCheckRequestList.size() < 1) {
                continue;
            }
            HashMap<String, Object> map = new HashMap<>();
            T t;
            for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
                map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
            }
            map.put("id", systemGroupData.getId());
            t = CrmebUtil.mapToObj(map, cls);
            arrayList.add(t);
        }

        return arrayList;
    }

    /**
      * 通过gid获取列表
      * @param gid Integer group id
      * <AUTHOR>
      * @since 2020-05-15
      * @return List<HashMap<String, Object>>
      */
    @Override
    public List<HashMap<String, Object>> getListMapByGid(Integer gid) {
        Object object = redisUtil.get(Constants.SYSTEM_GROUP_DATA_TYPE_1 + gid);
        if (object == null){
            SystemGroupDataSearchRequest systemGroupDataSearchRequest = new SystemGroupDataSearchRequest();
            systemGroupDataSearchRequest.setGid(gid);
            systemGroupDataSearchRequest.setStatus(true);
            List<SystemGroupData> list = getList(systemGroupDataSearchRequest, new PageParamRequest());

            List<HashMap<String, Object>> arrayList = new ArrayList<>();
            if (list.size() < 1) {
                return null;
            }

            for (SystemGroupData systemGroupData : list) {
                JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
                List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = CrmebUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
                if (systemFormItemCheckRequestList.size() < 1) {
                    continue;
                }
                HashMap<String, Object> map = new HashMap<>();

                for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
                    map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
                }
                map.put("id", systemGroupData.getId());
                arrayList.add(map);
            }
            redisUtil.set(Constants.SYSTEM_GROUP_DATA_TYPE_1 + gid, JSON.toJSONString(arrayList));
            return arrayList;
        }
        List<HashMap<String, Object>> maps = JSON.parseObject(object.toString(), new TypeReference<>() {});
        return maps;
    }

    @Override
    public List<HashMap<String, Object>> getListMapByGid(Integer gid, List<Integer> integerList) {
        SystemGroupDataSearchRequest systemGroupDataSearchRequest = new SystemGroupDataSearchRequest();
        systemGroupDataSearchRequest.setGid(gid);
        systemGroupDataSearchRequest.setStatus(true);
        List<SystemGroupData> list = getList(systemGroupDataSearchRequest,integerList);

        List<HashMap<String, Object>> arrayList = new ArrayList<>();
        if (list.size() < 1) {
            return null;
        }

        for (SystemGroupData systemGroupData : list) {
            JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
            List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = CrmebUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
            if (systemFormItemCheckRequestList.size() < 1) {
                continue;
            }
            HashMap<String, Object> map = new HashMap<>();

            for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
                map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
            }
            map.put("id", systemGroupData.getId());
            arrayList.add(map);
        }

        return arrayList;
    }

    /**
     * 通过gid获取列表
     * @param groupDataId Integer group id
     * @return <T>
     */
    public <T> T getNormalInfo(Integer groupDataId, Class<T> cls) {
        SystemGroupData systemGroupData = getById(groupDataId);
        if (null == systemGroupData || !systemGroupData.getStatus()) {
            return null;
        }

        JSONObject jsonObject = JSONObject.parseObject(systemGroupData.getValue());
        List<SystemFormItemCheckRequest> systemFormItemCheckRequestList = CrmebUtil.jsonToListClass(jsonObject.getString("fields"), SystemFormItemCheckRequest.class);
        if (systemFormItemCheckRequestList.size() < 1) {
            return null;
        }
        HashMap<String, Object> map = new HashMap<>();
        T t;
        for (SystemFormItemCheckRequest systemFormItemCheckRequest : systemFormItemCheckRequestList) {
            map.put(systemFormItemCheckRequest.getName(), systemFormItemCheckRequest.getValue());
        }
        map.put("id", systemGroupData.getId());
        t = CrmebUtil.mapToObj(map, cls);

        return t;
    }

    /**
     * 获取个人中心菜单
     * @return HashMap<String, Object>
     */
    @Override
    public HashMap<String, Object> getMenuUser() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("routine_my_menus", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_USER_CENTER_MENU));
        map.put("routine_my_banner", getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_USER_CENTER_BANNER));
        return map;
    }

    /**
     * 获取列表通过gid
     * @param gid gid
     * @return 列表
     */
    @Override
    public List<SystemGroupData> findListByGid(Integer gid) {
        LambdaQueryWrapper<SystemGroupData> lqw = Wrappers.lambdaQuery();
        lqw.eq(SystemGroupData::getGid, gid);
        lqw.orderByAsc(SystemGroupData::getSort).orderByAsc(SystemGroupData::getId);
        return dao.selectList(lqw);
    }

    /**
     * 删除通过gid
     * @param gid gid
     * @return Boolean
     */
    @Override
    public Boolean deleteByGid(Integer gid) {
        LambdaUpdateWrapper<SystemGroupData> luw = Wrappers.lambdaUpdate();
        luw.eq(SystemGroupData::getGid, gid);
        int delete = dao.delete(luw);
        if (delete > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public SystemGroupDataCardModel getModelById(Integer id) {
        SystemGroupData systemGroupData = baseMapper.selectById(id);
        if (systemGroupData == null){
            return null;
        }
        String value = systemGroupData.getValue();
        JSONObject jsonObject = JSONObject.parseObject(value);
        JSONArray jsonArray = jsonObject.getJSONArray("fields");
        SystemGroupDataCardModel systemGroupDataCardModel = new SystemGroupDataCardModel();
        for (Object object : jsonArray) {
            JSONObject jsonObject1 = JSONObject.parseObject(object.toString());
            String name = jsonObject1.getString("name");
            String value1 = jsonObject1.getString("value");
            try {
                setFieldValue(systemGroupDataCardModel, name, value1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        systemGroupDataCardModel.setId(systemGroupData.getId());
        return systemGroupDataCardModel;
    }

    @Override
    public List<SystemGroupDataCardModel> getModelByGid(Integer gid) {
        LambdaQueryWrapper<SystemGroupData> lambdaQueryWrapper = new LambdaQueryWrapper();
        LambdaQueryWrapper<SystemGroupData> queryWrapper = lambdaQueryWrapper.eq(SystemGroupData::getGid, gid);
        List<SystemGroupData> systemGroupDataList = baseMapper.selectList(queryWrapper);
        if (systemGroupDataList == null){
            return List.of();
        }
        List<SystemGroupDataCardModel> groupDataList = new ArrayList<>();
        for (SystemGroupData systemGroupData : systemGroupDataList) {
            String value = systemGroupData.getValue();
            JSONObject jsonObject = JSONObject.parseObject(value);
            JSONArray jsonArray = jsonObject.getJSONArray("fields");
            SystemGroupDataCardModel systemGroupDataCardModel = new SystemGroupDataCardModel();
            for (Object object : jsonArray) {
                JSONObject jsonObject1 = JSONObject.parseObject(object.toString());
                String name = jsonObject1.getString("name");
                String value1 = jsonObject1.getString("value");
                try {
                    setFieldValue(systemGroupDataCardModel, name, value1);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            systemGroupDataCardModel.setId(systemGroupData.getId());
            groupDataList.add(systemGroupDataCardModel);
        }
        return groupDataList;
    }

    @Override
    public <T> List<T> getEntityByGid(Integer gid,Class<T> clazz) {
        LambdaQueryWrapper<SystemGroupData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<SystemGroupData> queryWrapper = lambdaQueryWrapper.eq(SystemGroupData::getGid, gid);
        List<SystemGroupData> systemGroupDataList = baseMapper.selectList(queryWrapper);

        if (systemGroupDataList == null || systemGroupDataList.isEmpty()) {
            return Collections.emptyList();
        }

        List<T> groupDataList = new ArrayList<>();
        for (SystemGroupData systemGroupData : systemGroupDataList) {
            String value = systemGroupData.getValue();
            JSONObject jsonObject = JSONObject.parseObject(value);
            JSONArray jsonArray = jsonObject.getJSONArray("fields");

            try {
                T newInstance = clazz.getDeclaredConstructor().newInstance();

                for (Object object : jsonArray) {
                    JSONObject jsonObject1 = JSONObject.parseObject(object.toString());
                    String name = jsonObject1.getString("name");
                    String value1 = jsonObject1.getString("value");

                    try {
                        setFieldValue(newInstance, name, value1);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                setFieldValue(newInstance, "id", systemGroupData.getId());
                groupDataList.add(newInstance);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return groupDataList;
    }

    @Override
    public <T> T getEntityByid(Integer id, Class<T> tClass) {
        SystemGroupData systemGroupData = baseMapper.selectById(id);
        if (systemGroupData == null) {
            return null;
        }
        String value = systemGroupData.getValue();
        JSONObject jsonObject = JSONObject.parseObject(value);
        JSONArray jsonArray = jsonObject.getJSONArray("fields");

        try {
            T newInstance = tClass.getDeclaredConstructor().newInstance();
            for (Object object : jsonArray) {
                JSONObject jsonObject1 = JSONObject.parseObject(object.toString());
                String name = jsonObject1.getString("name");
                String value1 = jsonObject1.getString("value");

                try {
                    setFieldValue(newInstance, name, value1);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            setFieldValue(newInstance, "id", systemGroupData.getId());

            return newInstance;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}

