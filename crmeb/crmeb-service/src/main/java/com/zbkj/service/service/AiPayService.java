package com.zbkj.service.service;

import com.zbkj.common.model.finance.UserRecharge;
import com.zbkj.common.response.AiPayCreatePaymentResponse;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 微信支付

 */
public interface AiPayService {
    /**
     * 发起支付请求
     * @param price
     * @param orderId
     * @return
     */
    AiPayCreatePaymentResponse request(String price, String orderId);
}
