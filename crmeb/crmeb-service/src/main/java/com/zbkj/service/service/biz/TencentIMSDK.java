package com.zbkj.service.service.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zbkj.common.enums.LiveSystemMsgType;
import com.zbkj.service.util.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.awt.SystemColor.text;

@Slf4j
@Service
public class TencentIMSDK {

    @Value("${crmeb.im-sdk-appid}")
    private String appid;

    @Value("${crmeb.im-sdk-secret}")
    private String secret;

    @Value("${crmeb.im-sdk-api}")
    private String api;

    /**
     * 获取usersig
     *
     * @param userId
     * @return
     * @throws JSONException
     */
    public String getUsersig(String userId) {
        TLSSigAPIv2 apIv2 = new TLSSigAPIv2(Long.parseLong(appid), secret);
        return apIv2.genUserSig(userId, 365 * 24 * 60 * 60);
    }

    /**
     * 创建直播间
     *
     * @param roomId
     * @param roomName
     */
    public void createAVChatRoom(Long roomId, String roomName) {
        try {
            String admin = getUsersig("administrator");
            OkHttpUtils utils = new OkHttpUtils();
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("Type", "AVChatRoom");
            parameters.put("Name", roomName);
            parameters.put("GroupId", roomId.toString());
            String post = utils.post(String.format(api, "group_open_http_svc/create_group", appid, admin, new SecureRandom().nextInt(429496729)), parameters);
            log.info("创建直播间IM群组{}-{}响应：{}", roomId, roomName, post);
            JSONObject response = JSONObject.parseObject(post);
            Integer errorCode = response.getInteger("ErrorCode");
            if (errorCode != 0) {
                log.warn("创建直播间IM群组{}-{}失败，原因：{}", roomId, roomName, response.getString("ErrorInfo"));
            }
        } catch (Exception e) {
            log.warn("创建直播间IM群组{}-{}失败，", roomId, roomName, e);
        }
    }

    /**
     * 销毁直播间
     *
     * @param roomId
     */
    public void destroyAVChatRoom(Long roomId) {
        try {
            String usersig = getUsersig("administrator");
            OkHttpUtils utils = new OkHttpUtils();
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("GroupId", roomId.toString());
            String post = utils.post(String.format(api, "group_open_http_svc/destroy_group", appid, usersig, new SecureRandom().nextInt(429496729)), parameters);
            log.info("删除直播间IM群组{}响应：{}", roomId, post);
            JSONObject response = JSONObject.parseObject(post);
            Integer errorCode = response.getInteger("ErrorCode");
            if (errorCode != 0) {
                log.warn("删除直播间IM群组{}失败，原因：{}", roomId, response.getString("ErrorInfo"));
            }
        } catch (Exception e) {
            log.warn("删除直播间IM群组{}失败，", roomId, e);
        }
    }

    /**
     * 发送系统通知
     *
     * @param roomId
     * @param text
     * @return
     */
    public boolean sendGroupSystemNotification(Long roomId, LiveSystemMsgType liveSystemMsgType, String text) {
        try {
            String usersig = getUsersig("administrator");
            OkHttpUtils utils = new OkHttpUtils();
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("GroupId", roomId.toString());
            parameters.put("Content", JSON.toJSONString(Map.of("type", liveSystemMsgType.getType(), "message", text)));
            String post = utils.post(String.format(api, "group_open_http_svc/send_group_system_notification", appid, usersig, new SecureRandom().nextInt(429496729)), parameters);
            JSONObject response = JSONObject.parseObject(post);
            Integer errorCode = response.getInteger("ErrorCode");
            return errorCode == 0;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送系统通知
     *
     * @param roomId
     * @return
     */
    public boolean sendGroupSystemNotification(Long roomId, LiveSystemMsgType liveSystemMsgType) {
        try {
            String usersig = getUsersig("administrator");
            OkHttpUtils utils = new OkHttpUtils();
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("GroupId", roomId.toString());
            parameters.put("Content", JSON.toJSONString(Map.of("type", liveSystemMsgType.getType(), "message", "")));
            String post = utils.post(String.format(api, "group_open_http_svc/send_group_system_notification", appid, usersig, new SecureRandom().nextInt(429496729)), parameters);
            JSONObject response = JSONObject.parseObject(post);
            Integer errorCode = response.getInteger("ErrorCode");
            return errorCode == 0;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 添加敏感词
     *
     * @return
     */
    public boolean addCloudAuditKeywords() {
        try {
            String usersig = getUsersig("administrator");
            OkHttpUtils utils = new OkHttpUtils();
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("SdkAppId", appid);
            parameters.put("LibID", "as4da4s56d4as56d4a5s6");
            parameters.put("UserKeywords", List.of(
                    Map.of("Content", "我是广告", "Label", "广告")
            ));
            String post = utils.post(String.format(api, "group_open_http_svc/send_group_system_notification", appid, usersig, new SecureRandom().nextInt(429496729)), parameters);
            JSONObject response = JSONObject.parseObject(post);
            Integer errorCode = response.getInteger("ErrorCode");
            return errorCode == 0;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
