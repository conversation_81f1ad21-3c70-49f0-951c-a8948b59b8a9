package com.zbkj.service.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.zbkj.common.enums.LiveRecordType;
import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.response.LiveRecordResponse;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: mall
 * @package: com.zbkj.service.service
 * @className: LiveLeaderboardService
 * @author: Gavin
 * @description: TODO
 * @date: 2024/3/6 19:54
 * @version: 1.0
 */
@Component
public class UserLivePlayRecordService {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    //KEY
    private static String KEY_PREFIX = "LIVE:PLAY:USER:{}";

    // 添加记录
    public void addRecord(Integer userId, Long roomId, LiveRecordType liveRecordType) {
        String key = StrUtil.format(KEY_PREFIX, userId);
        Double score = redisTemplate.opsForZSet().score(key, roomId);
        if (score == null) {
            // 设置分数为当前时间戳
            Long timeMillis = System.currentTimeMillis();
            if (liveRecordType == LiveRecordType.previous){
                Double firstRecordScore = getFirstRecordScore(key);
                if (firstRecordScore != null){
                    timeMillis = (long) (firstRecordScore - 1.0);
                }

            }
            redisTemplate.opsForZSet().add(key, roomId, timeMillis);
            // 设置有序集合键的过期时间
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
        }
    }

    public List<Long> getAllPlayRecords(Integer userId) {
        String key = StrUtil.format(KEY_PREFIX, userId);
        // 获取 key 的所有元素
        Set<Object> records = redisTemplate.opsForZSet().range(key, 0, -1);
        List<Long> recordIds = new ArrayList<>();
        if (records != null) {
            // 将所有元素转换为 Long 类型的记录 ID
            for (Object record : records) {
                recordIds.add(Long.parseLong(record.toString()));
            }
        }
        return recordIds;
    }

    // 获取上一个播放记录
    public Long getPreviousPlayRecord(Integer userId, Long roomId) {
        String key = StrUtil.format(KEY_PREFIX, userId);
        // 获取 key 的分数
        Double score = redisTemplate.opsForZSet().score(key, roomId);
        if (score == null) {
            return null; // 如果 key 不存在，则返回 null
        }
        // 获取分数小于 key 的分数的最大值的元素（即上一个数据）
        Set<Object> previousKeys = redisTemplate.opsForZSet().reverseRangeByScore(key, Double.NEGATIVE_INFINITY, score, 1, 1);
        if (previousKeys.isEmpty()) {
            return null;
        }
        Object object = previousKeys.iterator().next();
        if (object != null) {
            return Long.parseLong(object.toString());
        }
        return null;
    }


    // 获取下一个播放记录
    public Long getNextPlayRecord(Integer userId, Long roomId) {
        // 构造记录的key
        String key = StrUtil.format(KEY_PREFIX, userId);
        // 获取 key 的分数
        Double score = redisTemplate.opsForZSet().score(key, roomId);
        if (score == null) {
            return null; // 如果 key 不存在，则返回 null
        }
        // 获取分数大于 key 的分数的最小值的元素（即下一个数据）
        Set<Object> nextKeys = redisTemplate.opsForZSet().rangeByScore(key, score, Double.POSITIVE_INFINITY, 1, 1);
        if (nextKeys.isEmpty()) {
            return null;
        }
        Object object = nextKeys.iterator().next();
        // 返回后一行数据
        if (object != null) {
            return Long.parseLong(object.toString());
        }
        return null;
    }
    public Double getFirstRecordScore(String key) {
        // 获取有序集合中的第一个元素及其分数
        Set<ZSetOperations.TypedTuple<Object>> firstRecordWithScores = redisTemplate.opsForZSet().rangeWithScores(key, 0, 0);
        if (!CollectionUtils.isEmpty(firstRecordWithScores)) {
            ZSetOperations.TypedTuple<Object> firstTuple = firstRecordWithScores.iterator().next();
            return firstTuple.getScore();
        }
        return null;
    }


    /**
     * 删除观看记录，用户退出总直播间时
     *
     * @return
     */
    public void deleteRecord(Integer userId) {
        String key = StrUtil.format(KEY_PREFIX, userId);
        redisTemplate.delete(key);
    }
}
