package com.zbkj.service.dao;

import com.zbkj.common.model.finance.UserRecharge;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zbkj.common.vo.DateLimitUtilVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 用户充值表 Mapper 接口

 */
public interface UserRechargeDao extends BaseMapper<UserRecharge> {

    /**
     * 根据类型获取该类型充值总金额
     * @return      该类型充值总金额
     */
    BigDecimal getSumByType(@Param("dateLimitUtilVo") DateLimitUtilVo dateLimitUtilVo,@Param("groupId") Integer groupId);

    /**
     * 获取退款总金额
     * @return 退款总金额
     */
    BigDecimal getSumByRefund();
}
