package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zbkj.common.model.system.SystemUserGoogleAuth;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.SystemUserGoogleAuthDao;
import com.zbkj.service.service.SystemUserGoogleAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SystemCityServiceImpl 接口实现

 */
@Service
public class SystemUserGoogleAuthServiceImpl extends ServiceImpl<SystemUserGoogleAuthDao, SystemUserGoogleAuth> implements SystemUserGoogleAuthService {

}

