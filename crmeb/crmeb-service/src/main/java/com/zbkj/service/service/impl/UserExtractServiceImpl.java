package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.BrokerageRecordConstants;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.enums.HierarchyLevel;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.UserExclusive;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserBill;
import com.zbkj.common.model.user.UserBrokerageRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserExtractRequest;
import com.zbkj.common.request.UserExtractSearchRequest;
import com.zbkj.common.request.UserExtractUpdateRequest;
import com.zbkj.common.response.BalanceResponse;
import com.zbkj.common.response.UserExtractRecordResponse;
import com.zbkj.common.response.UserExtractResponse;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.UserExtractDao;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

/**
 * UserExtractServiceImpl 接口实现
 */
@Slf4j
@Service
public class UserExtractServiceImpl extends ServiceImpl<UserExtractDao, UserExtract> implements UserExtractService {

    @Resource
    private UserExtractDao dao;


    @Autowired
    private UserService userService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserBrokerageRecordService userBrokerageRecordService;

    @Autowired
    private MerchantShopService merchantShopService;

    @Autowired
    private UserBalanceInterestReportService userBalanceInterestReportService;

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private UserActivityRecordService userActivityRecordService;
    @Autowired
    private ProductLikeRecordService productLikeRecordService;

    @Autowired
    private UserExclusiveService userExclusiveService;

    @Autowired
    private InvestItemsOrderService investItemsOrderService;

    @Autowired
    private UserLockService userLockService;

    /**
     * 列表
     *
     * @param request          请求参数
     * @param pageParamRequest 分页类参数
     * @return List<UserExtract>
     * <AUTHOR>
     * @since 2020-05-11
     */
    @Override
    public List<UserExtract> getList(UserExtractSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        //带 UserExtract 类的多条件查询
        LambdaQueryWrapper<UserExtract> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isBlank(request.getOrderId())) {
            lambdaQueryWrapper.eq(UserExtract::getOrderId, request.getOrderId());
        }
        if (!StringUtils.isBlank(request.getKeywords())) {
            User userByAccount = userService.getUserByAccount(request.getKeywords());
            if (userByAccount == null) {
                lambdaQueryWrapper.and(i -> i
                        .or(j -> j.like(UserExtract::getWechat, request.getKeywords()))        // 微信号
                        .or(j -> j.like(UserExtract::getRealName, request.getKeywords()))      // 名称
                        .or(j -> j.like(UserExtract::getBankCode, request.getKeywords()))      // 银行卡
                        .or(j -> j.like(UserExtract::getBankAddress, request.getKeywords()))   // 开户行
                        .or(j -> j.like(UserExtract::getAlipayCode, request.getKeywords()))    // 支付宝
                        .or(j -> j.like(UserExtract::getFailMsg, request.getKeywords()))        // 失败原因
                        .or(j -> j.eq(UserExtract::getUid, request.getKeywords()))               // ID
                );
            } else {
                lambdaQueryWrapper.eq(UserExtract::getUid, userByAccount.getUid());
            }
        }

        if (!StringUtils.isBlank(request.getReviewBy())) {
            lambdaQueryWrapper.like(UserExtract::getReviewBy, request.getReviewBy());
        }

        if (request.getSort() != null) {
            switch (request.getSort()) {
                case TOTAL_WITHDRAWAL_ASC:
                    lambdaQueryWrapper.orderByAsc(UserExtract::getExtractPrice, UserExtract::getCreateTime);
                    break;
                case TOTAL_WITHDRAWAL_DESC:
                    lambdaQueryWrapper.orderByDesc(UserExtract::getExtractPrice, UserExtract::getCreateTime);
                    break;
                default:
                    //按创建时间降序排列
                    lambdaQueryWrapper.orderByDesc(UserExtract::getCreateTime, UserExtract::getId);
                    break;
            }
        } else {
            lambdaQueryWrapper.orderByDesc(UserExtract::getCreateTime, UserExtract::getId);
        }

        lambdaQueryWrapper.eq(Objects.nonNull(request.getGroupId()), UserExtract::getGroupId, request.getGroupId());

        if (request.getLevelUid() != null) {
            User byId = userService.getById(request.getLevelUid());
            if (byId == null) {
                return new ArrayList<>();
            }
            //上级
            if (request.getLevelType() == HierarchyLevel.SUPERVISOR) {
                User parentUser = userService.getById(byId.getSpreadUid());
                if (parentUser == null) {
                    return new ArrayList<>();
                }
                lambdaQueryWrapper.eq(UserExtract::getUid, parentUser.getUid());
            }
            //下级
            else if (request.getLevelType() == HierarchyLevel.SUBORDINATES) {
                List<User> userListBySpread = userService.getUserListBySpread(byId.getUid());
                if (userListBySpread == null || userListBySpread.size() == 0) {
                    return new ArrayList<>();
                }
                List<Integer> integerList = userListBySpread.stream().map(User::getUid).collect(Collectors.toList());
                lambdaQueryWrapper.in(UserExtract::getUid, integerList);
            }
        }
        //提现状态
        if (request.getStatus() != null) {
            lambdaQueryWrapper.eq(UserExtract::getStatus, request.getStatus());
        }

        //提现方式
        if (!StringUtils.isBlank(request.getExtractType())) {
            lambdaQueryWrapper.eq(UserExtract::getExtractType, request.getExtractType());
        }

        //时间范围
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            DateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(UserExtract::getCreateTime, dateLimit.getStartTime(), dateLimit.getEndTime());
        }

        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (systemAdmin.getGroupId() != null) {
            lambdaQueryWrapper.eq(UserExtract::getGroupId, systemAdmin.getGroupId());
        } else {
            lambdaQueryWrapper.eq(Objects.nonNull(request.getGroupId()), UserExtract::getGroupId, request.getGroupId());
        }


        List<UserExtract> extractList = dao.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(extractList)) {
            return extractList;
        }
        List<Integer> uidList = extractList.stream()
                .filter(o -> o.getType() == 1 || o.getType() == 2)
                .map(o -> o.getUid()).distinct().collect(Collectors.toList());
        HashMap<Integer, User> userMap = userService.getMapListInUid(uidList);

        List<Integer> merchantList = extractList.stream()
                .filter(o -> o.getType() == 3)
                .map(o -> o.getUid()).distinct().collect(Collectors.toList());
        Map<Integer, MerchantShop> merchantMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(merchantList) && merchantList.size() > 0) {
            merchantMap = merchantShopService.getListById(merchantList);
        }
        for (UserExtract userExtract : extractList) {
            if (userExtract.getType() == 1 || userExtract.getType() == 2) {
                Integer uid = userExtract.getUid();
                User user = userMap.get(uid);
                if (Objects.isNull(user)) {
                    User user1 = userService.getById(uid);
                    log.error("发生异常，用户数据不存在eb_user表中，uid={},user1={}", userExtract.getUid(), user1);
                    userExtract.setNickName("");
                    userExtract.setRealNameAuth("");
                    userExtract.setCount(0);
                    userExtract.setAccount("");
                    userExtract.setUserMark("");
                    userExtract.setExchangeAmount(BigDecimal.ZERO);
                    userExtract.setPhone(user1.getPhone());
                    continue;
                }
                userExtract.setNickName(Optional.ofNullable(user.getNickname()).orElse(""));
                userExtract.setRealNameAuth(Optional.ofNullable(user.getRealName()).orElse(""));
                userExtract.setCount(user.getWithdrawCount());
                userExtract.setAccount(user.getAccount());
                userExtract.setUserMark(user.getMark());
                if (userExtract.getExchangeRate().compareTo(ZERO) > 0) {
                    BigDecimal exchangeAmount = userExtract.getExtractPrice().divide(userExtract.getExchangeRate(), 2, RoundingMode.HALF_UP);
                    userExtract.setExchangeAmount(exchangeAmount);
                }
            } else if (userExtract.getType() == 3) {
                MerchantShop merchantShop = merchantMap.get(userExtract.getUid());
                if (ObjectUtil.isNull(merchantShop)) {
                    userExtract.setNickName("--");
                } else {
                    userExtract.setNickName(merchantShop.getShopName());
                }
            }
        }
        return extractList;
    }

    /**
     * 提现总金额
     * 总佣金 = 已提现佣金 + 未提现佣金
     * 已提现佣金 = 用户成功提现的金额
     * 未提现佣金 = 用户未提现的佣金 = 可提现佣金 + 冻结佣金 = 用户佣金
     * 可提现佣金 = 包括解冻佣金、提现未通过的佣金 = 用户佣金 - 冻结期佣金
     * 待提现佣金 = 待审核状态的佣金
     * 冻结佣金 = 用户在冻结期的佣金，不包括退回佣金
     * 退回佣金 = 因退款导致的冻结佣金退回
     * 未体现金额 = 所有用户当前余额
     */
    @Override
    public BalanceResponse getBalance(String dateLimit, Integer groupId) {
        String startTime = "";
        String endTime = "";
        if (StringUtils.isNotBlank(dateLimit)) {
            DateLimitUtilVo dateRage = DateUtil.getDateLimit(dateLimit);
            startTime = dateRage.getStartTime();
            endTime = dateRage.getEndTime();
        }

        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (systemAdmin.getGroupId() != null) {
            groupId = systemAdmin.getGroupId();
        }

        //未提现金额
        BalanceResponse balanceResponse = new BalanceResponse();
        balanceResponse.setWithdrawn(getWithdrawn(startTime, endTime, groupId));
        balanceResponse.setUnDrawn(userService.getBalanceAll(startTime, endTime, groupId));
        balanceResponse.setCommissionTotal(userBrokerageRecordService.getTotalSpreadPriceBydateLimit(startTime, endTime, groupId));
        balanceResponse.setToBeWithdrawn(getWithdrawning(startTime, endTime, groupId));
        balanceResponse.setWithdrawalFee(getWithdrawalFee(startTime, endTime, groupId));
        balanceResponse.setNewUserNum(userService.getRegisterNumByDate(startTime, endTime, groupId));
        balanceResponse.setBalanceInterest(userBalanceInterestReportService.getBalanceInterest(startTime, endTime, groupId));
        balanceResponse.setRechargerAmount(userRechargeService.getRechargeAmount(startTime, endTime, groupId));
        balanceResponse.setFrozenBalance(userService.getFrozenBalance(startTime, endTime, groupId));
        balanceResponse.setHongBaoTotal(userActivityRecordService.hongBaoTotal(startTime, endTime, groupId));
        balanceResponse.setTotalLikeBalance(productLikeRecordService.totalLikeBalance(startTime, endTime, groupId));
        balanceResponse.setTotalRechargeUserCount(userRechargeService.getRechargeUserCount(startTime, endTime, groupId));
        balanceResponse.setTotalWithdrawUserCount(this.getWithdrawUserCount(startTime, endTime, groupId));
        balanceResponse.setInvestorCount(investItemsOrderService.getInvestorCount(startTime, endTime));
        //现金盈余 充值 - 提现
        BigDecimal cashBalance = balanceResponse.getRechargerAmount().subtract(balanceResponse.getWithdrawn());
        cashBalance = cashBalance.setScale(2, RoundingMode.HALF_UP); // 保留两位小数，四舍五入
        balanceResponse.setCashBalance(cashBalance);
        balanceResponse.setFirstTotalAmount(userRechargeService.getFirstRechargeAmount(startTime, endTime, groupId));
        balanceResponse.setFirstTotalUserNum(userRechargeService.getFirstRechargeUserCount(startTime, endTime, groupId));

        return balanceResponse;
    }


    /**
     * 提现总金额
     *
     * @return BalanceResponse
     * <AUTHOR>
     * @since 2020-05-11
     */
    @Override
    public BigDecimal getWithdrawn(String startTime, String endTime, Integer groupId) {
        return getSum(null, 1, startTime, endTime, 0, groupId);
    }

    /**
     * 审核中总金额
     *
     * @return BalanceResponse
     * <AUTHOR>
     * @since 2020-05-11
     */
    private BigDecimal getWithdrawning(String startTime, String endTime, Integer groupId) {
        return getSum(null, 0, startTime, endTime, 0, groupId);
    }

    /**
     * 统计手续费
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private BigDecimal getWithdrawalFee(String startTime, String endTime, Integer groupId) {
        QueryWrapper wrapper = Wrappers.query();
        wrapper.in("status", 1, 3);
        wrapper.select("SUM(fee)");
        wrapper.ge(StringUtils.isNotEmpty(startTime), "create_time", startTime);
        wrapper.le(StringUtils.isNotEmpty(endTime), "create_time", endTime);
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        List<Object> list = dao.selectObjs(wrapper);
        if (list != null && list.size() > 0 && list.get(0) != null) {
            return new BigDecimal(list.get(0).toString());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据状态获取总额
     *
     * @return BigDecimal
     */
    private BigDecimal getSum(Integer userId, int status, String startTime, String endTime, Integer type, Integer groupId) {
        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        if (type > 0) {
            lqw.eq(UserExtract::getType, type);
        }
        if (null != userId) {
            lqw.eq(UserExtract::getUid, userId);
        }
        if (status == 1) {
            lqw.in(UserExtract::getStatus, 1, 3);
        } else if (status == -1) {
            lqw.in(UserExtract::getStatus, -1, 4);
        } else if (status == 0) {
            lqw.in(UserExtract::getStatus, 0, 2);

        }
        lqw.eq(Objects.nonNull(groupId), UserExtract::getGroupId, groupId);
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            lqw.between(UserExtract::getCreateTime, startTime, endTime);
        }
        List<UserExtract> userExtracts = dao.selectList(lqw);
        BigDecimal sum = ZERO;
        if (CollUtil.isNotEmpty(userExtracts)) {
            sum = userExtracts.stream().map(UserExtract::getExtractPrice).reduce(ZERO, BigDecimal::add);
        }
        return sum;
    }

    /**
     * 获取用户对应的提现数据
     *
     * @param userId 用户id
     * @return 提现数据
     */
    @Override
    public UserExtractResponse getUserExtractByUserId(Integer userId) {
        QueryWrapper<UserExtract> qw = new QueryWrapper<>();
        qw.select("SUM(extract_price) as extract_price,count(id) as id, uid");
        qw.ge("status", 1);
        qw.eq("uid", userId);
        qw.groupBy("uid");
        UserExtract ux = dao.selectOne(qw);
        UserExtractResponse uexr = new UserExtractResponse();
//        uexr.setEuid(ux.getUid());
        if (null != ux) {
            uexr.setExtractCountNum(ux.getId()); // 这里的id其实是数量，借变量传递
            uexr.setExtractCountPrice(ux.getExtractPrice());
        } else {
            uexr.setExtractCountNum(0); // 这里的id其实是数量，借变量传递
            uexr.setExtractCountPrice(ZERO);
        }

        return uexr;
    }

    /**
     * 提现审核
     *
     * @param status      审核状态 -1 未通过 0 审核中 1 已提现
     * @param backMessage 驳回原因
     * @return 审核结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(UserExtract userExtract, Integer status, String backMessage) {
        // 使用Redis分布式锁保护用户数据更新
        return userLockService.executeWithUserLock(userExtract.getUid(), () -> {
            UserExtract userExtractById = this.getById(userExtract);
            if (userExtractById == null) {
                throw new CrmebException("无效的ID");
            }
            if (status == -1 && StringUtils.isBlank(backMessage))
                throw new CrmebException("驳回时请填写驳回原因");

            if (ObjectUtil.isNull(userExtract)) {
                throw new CrmebException("提现申请记录不存在");
            }
            userExtract.setStatus(status);

            User user = userService.getById(userExtract.getUid());
            if (ObjectUtil.isNull(user)) {
                throw new CrmebException("提现用户数据异常");
            }

        Boolean execute = transactionTemplate.execute(status1 -> {
            userExtract.setUpdateTime(cn.hutool.core.date.DateUtil.date());
            // 拒绝
            if (status == -1) {//未通过时恢复用户总金额
                userExtract.setFailMsg(backMessage);
                // 添加提现申请拒绝佣金记录
                UserBrokerageRecord brokerageRecord = new UserBrokerageRecord();
                brokerageRecord.setUid(user.getUid());
                brokerageRecord.setLinkId(userExtract.getId().toString());
                brokerageRecord.setLinkType(BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_WITHDRAW);
                brokerageRecord.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
                brokerageRecord.setTitle(BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_WITHDRAW_FAIL);
                brokerageRecord.setPrice(userExtract.getExtractPrice());
                brokerageRecord.setBalance(user.getBrokeragePrice().add(userExtract.getExtractPrice()).add(userExtract.getFee()));
                brokerageRecord.setMark(StrUtil.format("提现申请拒绝返还佣金{}", userExtract.getExtractPrice().add(userExtract.getFee())));
                brokerageRecord.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
                brokerageRecord.setCreateTime(DateUtil.nowDateTime());
                User updateUser = new User();
                //拒绝时恢复设定的提现利息
                if (user.getWithdrawFeeBak().compareTo(ZERO) > 0) {
                    updateUser.setWithdrawFee(user.getWithdrawFeeBak());
                    updateUser.setWithdrawFeeBak(ZERO);
                }
                // 返还佣金
                BigDecimal amount = user.getNowMoney().add(userExtract.getExtractPrice().add(userExtract.getFee()));
                updateUser.setNowMoney(amount);
                userService.updateInfo(updateUser, user.getUid());
                userBrokerageRecordService.save(brokerageRecord);
                monitorLog(userExtract);
                return true;
            }

            // 同意
            if (status == 1 || status == 3) {
                user.setWithdrawAmount(user.getWithdrawAmount().add(userExtract.getExtractPrice()).add(userExtract.getFee()));
                if (user.getWithdrawFeeBak().compareTo(ZERO) > 0) {
                    user.setWithdrawFeeBak(ZERO);
                }
                userService.updateById(user);
                updateById(userExtract);
                return Boolean.TRUE;
            }
            return false;
        });

            if (!execute) {
                throw new CrmebException("处理失败，请重试！");
            }
            return true;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusAccountBalance(UserExtract userExtract, Integer status, String backMessage) {
        // 使用Redis分布式锁保护用户数据更新
        return userLockService.executeWithUserLock(userExtract.getUid(), () -> {
            if (status == -1 && StringUtils.isBlank(backMessage))
                throw new CrmebException("驳回时请填写驳回原因");

            if (ObjectUtil.isNull(userExtract)) {
                throw new CrmebException("提现申请记录不存在");
            }
            userExtract.setStatus(status);

            User user = userService.getById(userExtract.getUid());
            if (ObjectUtil.isNull(user)) {
                throw new CrmebException("提现用户数据异常");
            }
        Boolean execute = transactionTemplate.execute(status1 -> {
            userExtract.setUpdateTime(cn.hutool.core.date.DateUtil.date());
            // 拒绝
            if (status == -1) {//未通过时恢复用户总金额
                userExtract.setFailMsg(backMessage);
                //拒绝时恢复设定的提现利息
                User updateUser = new User();
                if (user.getWithdrawFeeBak().compareTo(ZERO) > 0) {
                    updateUser.setWithdrawFee(user.getWithdrawFeeBak());
                    updateUser.setWithdrawFeeBak(ZERO);
                }
                // 返还佣金
                BigDecimal amount = user.getNowMoney().add(userExtract.getExtractPrice().add(userExtract.getFee()));
                updateUser.setNowMoney(amount);
                userService.updateInfo(updateUser, user.getUid());
                monitorLog(userExtract);
                updateById(userExtract);
                return Boolean.TRUE;
            }

            // 同意
            if (status == 1 || status == 3) {
                user.setWithdrawAmount(user.getWithdrawAmount().add(userExtract.getExtractPrice()).add(userExtract.getFee()));
                if (user.getWithdrawFeeBak().compareTo(ZERO) > 0) {
                    user.setWithdrawFeeBak(ZERO);
                }
                userService.updateById(user);
                updateById(userExtract);
                return Boolean.TRUE;
            }
            return false;
        });

            if (!execute) {
                throw new CrmebException("处理失败，请重试！");
            }
            return true;
        });
    }

    public void monitorLog(UserExtract userExtract) {
        // 余额变动对象
        BigDecimal decimal = userExtract.getExtractPrice().add(userExtract.getFee());
        UserBill userBill = new UserBill();
        userBill.setUid(userExtract.getUid());
        userBill.setLinkId("");
        userBill.setPm(1);
        userBill.setTitle("提现失败退款");
        userBill.setCategory(Constants.USER_BILL_CATEGORY_USER_WITHDRAWAL_RETURN);
        userBill.setType(Constants.USER_BILL_TYPE_EXTRACT);
        userBill.setNumber(decimal);
        userBill.setBalance(userExtract.getBalance().add(decimal));
        userBill.setMark(StrUtil.format("余额增加了{}元", decimal));
        userBill.setStatus(1);
        userBill.setCreateTime(DateUtil.nowDateTime());
        userBillService.save(userBill);
    }

    @Override
    public Boolean updateStatusMerchantBalance(UserExtract userExtract, Integer status, String backMessage) {
        if (status == -1 && StringUtils.isBlank(backMessage))
            throw new CrmebException("驳回时请填写驳回原因");

        if (ObjectUtil.isNull(userExtract)) {
            throw new CrmebException("提现申请记录不存在");
        }
        userExtract.setStatus(status);

        MerchantShop merchantShop = merchantShopService.getById(userExtract.getUid());
        if (ObjectUtil.isNull(merchantShop)) {
            throw new CrmebException("提现店铺数据异常");
        }

        Boolean execute = transactionTemplate.execute(status1 -> {
            userExtract.setUpdateTime(cn.hutool.core.date.DateUtil.date());
            // 拒绝
            if (status == -1) {//未通过时恢复用户总金额
                userExtract.setFailMsg(backMessage);
                // 返还
                BigDecimal amount = merchantShop.getBalance().add(userExtract.getExtractPrice()).add(userExtract.getFee());
                merchantShop.setBalance(amount);
                merchantShopService.updateById(merchantShop);
                updateById(userExtract);
                monitorLog(userExtract);
                return Boolean.TRUE;
            }

            // 同意
            if (status == 1 || status == 3) {
                updateById(userExtract);
                return Boolean.TRUE;
            }
            return false;
        });
        if (!execute) {
            throw new CrmebException("处理失败，请重试！");
        }
        return true;
    }

    /**
     * 获取提现记录列表
     *
     * @param userId           用户uid
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    @Override
    public PageInfo<UserExtractRecordResponse> getExtractRecord(Integer userId, Integer queryType, PageParamRequest pageParamRequest) {
        Page<UserExtract> userExtractPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        QueryWrapper<UserExtract> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", userId);
        if (queryType > 0) {
//            queryWrapper.eq("type", queryType);
        }
        queryWrapper.groupBy("left(create_time, 7)");
        queryWrapper.orderByDesc("left(create_time, 7)");

        List<UserExtract> list = dao.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return new PageInfo<>();
        }
        ArrayList<UserExtractRecordResponse> userExtractRecordResponseList = CollectionUtil.newArrayList();
        for (UserExtract userExtract : list) {
            String date = DateUtil.dateToStr(userExtract.getCreateTime(), Constants.DATE_FORMAT_MONTH);
            userExtractRecordResponseList.add(new UserExtractRecordResponse(date, getListByMonth(userId, date, queryType)));
        }

        return CommonPage.copyPageInfo(userExtractPage, userExtractRecordResponseList);
    }

    private List<UserExtract> getListByMonth(Integer userId, String date, Integer type) {
        QueryWrapper<UserExtract> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "extract_price", "status", "create_time", "update_time", "type", "fee", "fail_msg");
        queryWrapper.eq("uid", userId);
        if (type > 0) {
            //queryWrapper.eq("type", type);
        }
        queryWrapper.apply(StrUtil.format(" left(create_time, 7) = '{}'", date));
        queryWrapper.orderByDesc("create_time");
        return dao.selectList(queryWrapper);
    }

    /**
     * 获取用户提现总金额
     *
     * @param userId 用户uid
     * @return BigDecimal
     */
    @Override
    public BigDecimal getExtractTotalMoney(Integer userId, Integer type, Integer groupId) {
        return getSum(userId, 1, null, null, type, groupId);
    }


    /**
     * 提现申请
     *
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean extractApply(UserExtractRequest request, User user, String account) {
        // 使用Redis分布式锁保护用户数据更新
        return userLockService.executeWithUserLock(user.getUid(), () -> {
            //添加判断，提现金额不能后台配置金额
            String minPriceCnyConfig = systemConfigService.getValueByKeyException(Constants.CONFIG_EXTRACT_MIN_PRICE);
            BigDecimal minPriceCny = new BigDecimal(minPriceCnyConfig);
            String userWithdrawFeeConfig = systemConfigService.getValueByKeyException("userWithdrawFee");
            String userUSDTFeeConfig = systemConfigService.getValueByKey("userUSDTFee");
            if (StringUtils.isBlank(userWithdrawFeeConfig)) {
                userWithdrawFeeConfig = "0";
            }
            if (ObjectUtil.isNull(user)) {
                throw new CrmebException("提现用户信息异常");
            }
            if (request.getExtractType().equals("usdt")) {
                if (StringUtils.isBlank(userUSDTFeeConfig)) {
                    throw new CrmebException("未设定USDT汇率，无法提现。");
                }
                BigDecimal userUSDTRate = new BigDecimal(userUSDTFeeConfig);
                BigDecimal amount = request.getExtractPrice().multiply(userUSDTRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                request.setExtractPrice(amount);
                if (request.getExtractPrice().compareTo(minPriceCny) < 0) {
                        BigDecimal usdt = minPriceCny.divide(userUSDTRate, 2, RoundingMode.HALF_UP);
                        throw new CrmebException(StrUtil.format("最低提现金额{}USDT", usdt));
                }
            } else {
                if (request.getExtractPrice().compareTo(minPriceCny) < 0) {
                    throw new CrmebException(StrUtil.format("最低提现金额{}元", minPriceCny));
                }
            }
            BigDecimal money = user.getBrokeragePrice();//可提现总金额
            if (money.compareTo(ZERO) < 1) {
                throw new CrmebException("您当前没有金额可以提现");
            }

            if (money.compareTo(request.getExtractPrice()) < 0) {
                throw new CrmebException("你当前最多可提现 " + money + "元");
            }
            user.setWithdrawCount(user.getWithdrawCount() + 1);
            BigDecimal balance = money.subtract(request.getExtractPrice());

            BigDecimal userWithdrawFee_;
            if (user.getWithdrawFee().compareTo(ZERO) > 0) {
                userWithdrawFee_ = user.getWithdrawFee();
                user.setWithdrawFeeBak(user.getWithdrawFee());
                user.setWithdrawFee(ZERO);
            } else {
                userWithdrawFee_ = new BigDecimal(userWithdrawFeeConfig);
            }
            userService.updateById(user);
            BigDecimal fee;
            if (userWithdrawFee_.compareTo(ZERO) > 0) {
                fee = request.getExtractPrice().multiply(userWithdrawFee_.divide(new BigDecimal(100))).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                fee = ZERO;
            }

            UserExtract userExtract = new UserExtract();
            BeanUtils.copyProperties(request, userExtract);
            userExtract.setUid(user.getUid());
            userExtract.setBalance(balance);
            userExtract.setFee(fee);
            userExtract.setGroupId(user.getGroupId());
            userExtract.setExtractPrice(request.getExtractPrice().subtract(fee));
            userExtract.setExchangeRate(new BigDecimal(userUSDTFeeConfig));
            userExtract.setRealName(user.getRealName());
            userExtract.setBankCode(account);
            userExtract.setBankName(request.getBankName());
            //存入银行名称
            if (StrUtil.isNotBlank(userExtract.getQrcodeUrl())) {
                userExtract.setQrcodeUrl(systemAttachmentService.clearPrefix(userExtract.getQrcodeUrl()));
            }

            // 添加佣金记录
            UserBrokerageRecord brokerageRecord = new UserBrokerageRecord();
            brokerageRecord.setUid(user.getUid());
            brokerageRecord.setLinkType(BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_WITHDRAW);
            brokerageRecord.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_SUB);
            brokerageRecord.setTitle(BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_WITHDRAW_APPLY);
            brokerageRecord.setPrice(userExtract.getExtractPrice().add(fee));
            brokerageRecord.setBalance(money.subtract(userExtract.getExtractPrice()));
            brokerageRecord.setMark(StrUtil.format("提现申请扣除佣金{}", userExtract.getExtractPrice().add(fee)));
            brokerageRecord.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_WITHDRAW);
            brokerageRecord.setCreateTime(DateUtil.nowDateTime());
            brokerageRecord.setGroupId(user.getGroupId());

            Boolean execute = transactionTemplate.execute(e -> {

                // 余额变动对象
                UserBill userBill = new UserBill();
                userBill.setUid(user.getUid());
                userBill.setLinkId("");
                userBill.setPm(0);
                userBill.setTitle("申请提现");
                userBill.setCategory(Constants.USER_BILL_CATEGORY_USER_WITHDRAWAL);
                userBill.setType(Constants.USER_BILL_TYPE_EXTRACT);
                userBill.setNumber(request.getExtractPrice());
                userBill.setBalance(balance);
                userBill.setMark(StrUtil.format("余额减少了{}元", request.getExtractPrice()));
                userBill.setStatus(1);
                userBill.setCreateTime(DateUtil.nowDateTime());
                userBillService.save(userBill);

                // 保存提现记录
                save(userExtract);
                // 修改用户佣金
                userService.operationBrokerage(user.getUid(), userExtract.getExtractPrice().add(fee), money, "sub");
                // 添加佣金记录
                brokerageRecord.setLinkId(userExtract.getId().toString());
                userBrokerageRecordService.save(brokerageRecord);
                return Boolean.TRUE;
            });
            // 此处可添加提现申请通知

            return execute;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean extractApplyAccountBalance(UserExtractRequest request, User user, String account) {
        // 使用Redis分布式锁保护用户数据更新
        return userLockService.executeWithUserLock(user.getUid(), () -> {
            //添加判断，提现金额不能低于后台配置金额
            String minPriceCnyConfig = systemConfigService.getValueByKey(Constants.CONFIG_EXTRACT_MIN_PRICE);
            BigDecimal minPriceCny = new BigDecimal(minPriceCnyConfig);
            String userWithdrawFeeConfig = systemConfigService.getValueByKey("userWithdrawFee");
            String userUSDTFeeConfig = systemConfigService.getValueByKey("userUSDTFee");
            if (StringUtils.isBlank(userWithdrawFeeConfig)) {
                userWithdrawFeeConfig = "0";
            }
            if (ObjectUtil.isNull(user)) {
                throw new CrmebException("提现用户信息异常");
            }

            if (Objects.equals(BanksType.USDT.getValue(), request.getExtractType())) {
                if (StringUtils.isBlank(userUSDTFeeConfig)) {
                    throw new CrmebException("未设定USDT汇率，无法提现。");
                }
                BigDecimal userUSDTRate = new BigDecimal(userUSDTFeeConfig);
                if (request.getExtractPrice().compareTo(minPriceCny) < 0) {
                        BigDecimal usdt = minPriceCny.divide(userUSDTRate, 2, RoundingMode.HALF_UP);
                        throw new CrmebException(StrUtil.format("最低提现金额{}USDT", usdt));
                }
            } else {
                if (request.getExtractPrice().compareTo(minPriceCny) < 0) {
                    throw new CrmebException(StrUtil.format("最低提现金额{}元", minPriceCny));
                }
            }
            BigDecimal money = user.getNowMoney();//可提现总金额
            if (money.compareTo(ZERO) < 1) {
                throw new CrmebException("您当前没有金额可以提现");
            }

            if (money.compareTo(request.getExtractPrice()) < 0) {
                throw new CrmebException("你当前最多可提现 " + money + "元");
            }

            user.setWithdrawCount(user.getWithdrawCount() + 1);

            BigDecimal balance = money.subtract(request.getExtractPrice());
            BigDecimal userWithdrawFee_ = null;
            //设定了专属用户手续费
            if (Objects.nonNull(user.getExclusiveId())) {
                UserExclusive userExclusive = userExclusiveService.getInfoById(user.getExclusiveId());
                if (userExclusive != null) {
                    userWithdrawFee_ = userExclusive.getFee();
                }
            } else {
                if (user.getWithdrawFee().compareTo(ZERO) > 0) {
                    userWithdrawFee_ = user.getWithdrawFee();
                    user.setWithdrawFeeBak(user.getWithdrawFee());
                    user.setWithdrawFee(ZERO);
                } else {
                    userWithdrawFee_ = new BigDecimal(userWithdrawFeeConfig);
                }
            }

            userService.updateById(user);
            BigDecimal fee = ZERO;
            if (userWithdrawFee_.compareTo(ZERO) > 0) {
                fee = request.getExtractPrice().multiply(userWithdrawFee_.divide(new BigDecimal(100))).setScale(4, BigDecimal.ROUND_HALF_UP);
            }

            UserExtract userExtract = new UserExtract();
            BeanUtils.copyProperties(request, userExtract);
            userExtract.setUid(user.getUid());
            userExtract.setBalance(balance);
            userExtract.setFee(fee);
            userExtract.setGroupId(user.getGroupId());
            userExtract.setExtractPrice(request.getExtractPrice().subtract(fee));
            userExtract.setExchangeRate(new BigDecimal(userUSDTFeeConfig));
            userExtract.setRealName(user.getRealName());
            userExtract.setBankCode(account);
            userExtract.setBankName(request.getBankName());

            if (userExtract.getExtractPrice().compareTo(ZERO) <= 0) {
                throw new CrmebException("手续费配置异常，请联系客服！");
            }

            //存入银行名称
            if (StrUtil.isNotBlank(userExtract.getQrcodeUrl())) {
                userExtract.setQrcodeUrl(systemAttachmentService.clearPrefix(userExtract.getQrcodeUrl()));
            }

            Boolean execute = transactionTemplate.execute(e -> {

                // 余额变动对象
                UserBill userBill = new UserBill();
                userBill.setUid(user.getUid());
                userBill.setLinkId("");
                userBill.setPm(0);
                userBill.setTitle("申请提现");
                userBill.setCategory(Constants.USER_BILL_CATEGORY_USER_WITHDRAWAL);
                userBill.setType(Constants.USER_BILL_TYPE_EXTRACT);
                userBill.setNumber(request.getExtractPrice());
                userBill.setBalance(balance);
                userBill.setMark(StrUtil.format("余额减少了{}元", request.getExtractPrice()));
                userBill.setStatus(1);
                userBill.setCreateTime(DateUtil.nowDateTime());
                userBillService.save(userBill);

                // 保存提现记录
                save(userExtract);
                userService.operationNowMoney(user.getUid(), request.getExtractPrice(), money, "sub");
                return Boolean.TRUE;
            });
            // 此处可添加提现申请通知

            return execute;
        });
    }

    /**
     * 修改提现申请
     *
     * @param id                 申请id
     * @param userExtractRequest 具体参数
     */
    @Override
    public Boolean updateExtract(Integer id, UserExtractUpdateRequest userExtractRequest) {
        UserExtract userExtract = new UserExtract();
        BeanUtils.copyProperties(userExtractRequest, userExtract);
        userExtract.setId(id);
        return updateById(userExtract);
    }

    /**
     * 提现申请待审核数量
     *
     * @return Integer
     */
    @Override
    public Integer getNotAuditNum() {
        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserExtract::getStatus, 0);
        return dao.selectCount(lqw);
    }

    @Override
    public UserExtract getByUidAndType(Integer uid, Integer type) {
        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserExtract::getUid, uid);
        lqw.eq(UserExtract::getType, type);
        lqw.orderByDesc(UserExtract::getCreateTime);
        lqw.last("limit 1");
        return dao.selectOne(lqw);
    }

    @Override
    public Boolean updateGroup(Integer userId, Integer groupId) {
        LambdaUpdateWrapper<UserExtract> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserExtract::getUid, userId);
        UserExtract userExtract = new UserExtract();
        userExtract.setGroupId(groupId);
        return baseMapper.update(userExtract, updateWrapper) > 0;
    }

    @Override
    public UserExtract getByOrderId(String orderId) {
        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserExtract::getOrderId, orderId);
        lqw.last("limit 1");
        return dao.selectOne(lqw);
    }


    @Override
    public List<UserExtract> findPendingOrder() {
        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserExtract::getStatus, 2);
        return dao.selectList(lqw);
    }
    @Override
    public List<UserExtract> findPendingOrder(int page, int pageSize) {
        LambdaQueryWrapper<UserExtract> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserExtract::getStatus, 0);
        // 限制 createTime 为最近 1 天
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(1);
        lambdaQueryWrapper.ge(UserExtract::getCreateTime, threeDaysAgo);
        lambdaQueryWrapper.last("LIMIT " + pageSize + " OFFSET " + (page * pageSize));
        return dao.selectList(lambdaQueryWrapper);
    }

    @Override
    public Integer getWithdrawUserCount(String startTime, String endTime, Integer groupId) {
        QueryWrapper<UserExtract> wrapper = Wrappers.query();
        wrapper.select("id");
        wrapper.ge(StringUtils.isNotBlank(startTime), "create_time", startTime);
        wrapper.le(StringUtils.isNotBlank(endTime), "create_time", endTime);
        wrapper.eq(Objects.nonNull(groupId), "group_id", groupId);
        wrapper.groupBy("uid");
        List<UserExtract> list = dao.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return 0;
        }
        return list.size();
    }

}

