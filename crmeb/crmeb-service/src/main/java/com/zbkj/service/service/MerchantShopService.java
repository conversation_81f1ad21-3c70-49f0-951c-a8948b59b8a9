package com.zbkj.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.request.MerchantExtractRequest;
import com.zbkj.common.request.PageParamRequest;

import java.util.List;
import java.util.Map;

/**
 * SystemCityService 接口

 */
public interface MerchantShopService extends IService<MerchantShop> {
    /**
     * 后台管理员列表
     * @param merchantShop 请求参数
     * @param pageParamRequest 分页参数
     * @return List
     */
    List<MerchantShop> getList(MerchantShop merchantShop, PageParamRequest pageParamRequest);

    /**
     * 初始化缓存
     * @return
     */
    List<MerchantShop> init();

    /**
     * 提供前端商铺接口数据
     * @return
     */
    List<MerchantShop> getList(Integer id);

    /**
     * 商户是否存在
     * @param merchantId
     * @return
     */
    boolean isMerchantExists(Integer merchantId);

    /**
     * 根据商户ID获取店铺
     * @param merchantId
     * @return
     */
    MerchantShop getByMerchant(Integer merchantId);

    /**
     * 商户提现
     * @param request
     * @return
     */
    boolean withdraw(MerchantExtractRequest request);

    /**
     * 根据id批量查询
     * @param ids
     * @return
     */
    Map<Integer,MerchantShop> getListById(List<Integer> ids);
}
