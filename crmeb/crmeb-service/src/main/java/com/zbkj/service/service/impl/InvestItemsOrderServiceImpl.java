package com.zbkj.service.service.impl;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

import cn.hutool.core.collection.CollUtil;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.RepaymentMethod;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.InvestItems;
import com.zbkj.common.model.InvestItemsConfig;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserBill;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.InvestItemsBuyRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.utils.CommonUtil;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.DateLimitUtilVo;
import com.zbkj.service.dao.InvestItemsOrderDao;
import com.zbkj.common.model.InvestItemsOrder;
import com.zbkj.common.request.InvestItemsOrderSearchRequest;
import com.zbkj.service.service.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 投资项目订单表 接口实现类
 */

@Service
public class InvestItemsOrderServiceImpl extends ServiceImpl<InvestItemsOrderDao, InvestItemsOrder> implements InvestItemsOrderService {

    private static final Logger logger = LoggerFactory.getLogger(InvestItemsOrderServiceImpl.class);

    @Autowired
    private UserService userService;
    @Autowired
    private InvestItemsService investItemsService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserBillService userBillService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private InvestItemsConfigService investItemsConfigService;

    @Autowired
    private UserLockService userLockService;

    /**
     * InvestItemsOrder 列表查询
     *
     * @param request 默认生成搜索对象，可根据需求修改或创建新 request
     * @return 分页结果
     */
    @Override
    public PageInfo<InvestItemsOrder> getList(InvestItemsOrderSearchRequest request) {
        // 设置分页
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());

        // 构建查询条件
        LambdaQueryWrapper<InvestItemsOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        // 处理 account 查询
        if (StringUtils.isNotBlank(request.getAccount())) {
            User user = userService.getUserByAccount(request.getAccount());
            if (user == null) {
                return new PageInfo<>();
            }
            request.setUserId(user.getUid());
        }

        // 添加查询条件
        lambdaQueryWrapper.eq(Objects.nonNull(request.getProjectId()), InvestItemsOrder::getProjectId, request.getProjectId());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUserId()), InvestItemsOrder::getUserId, request.getUserId());
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            DateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(InvestItemsOrder::getCreateTime, dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        // 添加 group_id 查询条件
        if (Objects.nonNull(request.getGroupId())) {
            lambdaQueryWrapper.inSql(InvestItemsOrder::getUserId,
                    "SELECT uid FROM eb_user WHERE group_id = " + request.getGroupId());
        }

        // 按创建时间降序排序
        lambdaQueryWrapper.orderByDesc(InvestItemsOrder::getCreateTime);
        List<InvestItemsOrder> list = baseMapper.selectList(lambdaQueryWrapper);

        // 查询关联数据
        List<Integer> userIdList = list.stream().map(InvestItemsOrder::getUserId).collect(Collectors.toList());
        List<Integer> projectIdList = list.stream().map(InvestItemsOrder::getProjectId).collect(Collectors.toList());
        Map<Integer, User> userMap = new HashMap<>();
        Map<Integer, InvestItems> investItemsMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(userIdList)) {
            userMap = userService.getMapListInUid(userIdList);
        }
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            List<InvestItems> investItems = investItemsService.listByIds(projectIdList);
            investItemsMap = investItems.stream().collect(Collectors.toMap(InvestItems::getId, Function.identity()));
        }

        // 设置关联字段
        for (InvestItemsOrder investItemsOrder : list) {
            User user = userMap.get(investItemsOrder.getUserId());
            if (user != null) {
                investItemsOrder.setAccount(user.getAccount());
                investItemsOrder.setGroupId(user.getGroupId());
            }
            InvestItems investItems = investItemsMap.get(investItemsOrder.getProjectId());
            if (investItems != null) {
                investItemsOrder.setProjectName(investItems.getProjectName());
            }
        }

        return CommonPage.copyPageInfo(startPage, list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean buy(InvestItemsBuyRequest request) {
        logger.info("投资项目:{}", JSON.toJSONString(request));
        Integer userId = userService.getUserId();
        // 使用统一的用户锁服务，确保与其他用户数据更新操作使用相同的锁
        return userLockService.executeWithUserLock(userId, Constants.USER_LOCK_INVESTMENT, () -> {

            try {
                Integer projectId = request.getProjectId();
            InvestItems investItems = investItemsService.getByItemsId(projectId);
            if (investItems == null) {
                throw new CrmebException("无效的投资项目ID");
            }
            if (!investItems.getStatus()){
                throw new CrmebException("当前项目已下架！");
            }
            if (investItems.getVirtualProgress().compareTo(new BigDecimal("100")) >= 0){
                throw new CrmebException("当前项目已完成！");
            }
            Date nowTime = new Date();
            if (nowTime.after(investItems.getEndTime())) {
                throw new CrmebException("当前项目已截止投资！");
            }

            if (nowTime.before(investItems.getStartTime())) {
                throw new CrmebException("不是可投资的时间！");
            }
            investItems.setProjectAmount(investItems.getProjectAmount().multiply(new BigDecimal("10000")));
            if (investItems.getInvestedAmount().compareTo(investItems.getProjectAmount()) >= 0) {
                throw new CrmebException("当前项目投资金额已达标，无法继续投入！");
            }
            if (request.getAmount().compareTo(investItems.getMinInvestment()) < 0) {
                throw new CrmebException(StrUtil.format("当前项目最低投资金额为：{}元", investItems.getMinInvestment()));
            }
            if (request.getAmount().compareTo(investItems.getMaxInvestment()) > 0) {
                throw new CrmebException(StrUtil.format("当前项目最高投资金额为：{}元", investItems.getMaxInvestment()));
            }
            Integer investCount = getInvestCount(userId, projectId);
            if (investCount >= investItems.getMaxInvestmentTimes()){
                throw new CrmebException("项目可投资的次数已上限！");
            }
            User user = userService.getInfo();
            if (StringUtils.isBlank(user.getPaymentPwd())){
                throw new CrmebException("未设置支付密码,无法购买！");
            }
            String paymentPwd = CommonUtil.md5(request.getPaymentPwd(), 2);
            if (!user.getPaymentPwd().equals(paymentPwd)){
                throw new CrmebException("支付密码错误!");
            }
            if (user.getNowMoney().compareTo(request.getAmount()) < 0){
                throw new CrmebException("余额不足，无法投资！");
            }
            InvestItemsConfig itemConfig = investItemsConfigService.getItemConfig(investItems.getId(), user.getLevel());
            if (itemConfig != null){
                investItems.setProfitRate(itemConfig.getProfitRate());
            }
            //写入投资订单记录
            Integer days = investItems.getDeadlineDays();
            Date endTime = DateUtil.addDay(nowTime, days);
            InvestItemsOrder investItemsOrder = new InvestItemsOrder();
            investItemsOrder.setProjectId(projectId);
            investItemsOrder.setUserId(userId);
            investItemsOrder.setDeadlineDays(days);
            investItemsOrder.setRepaymentMethod(RepaymentMethod.PRINCIPAL_AND_INTEREST_AT_MATURITY);
            investItemsOrder.setAmount(request.getAmount());
            investItemsOrder.setStartTime(new Date());
            investItemsOrder.setEndTime(endTime);
            investItemsOrder.setCreateTime(nowTime);
            investItemsOrder.setStatus(true);
            investItemsOrder.setProfitRate(investItems.getProfitRate());
            //收益
            investItemsOrder.setExpectedEarnings(calculateProfit(request.getAmount(),investItems.getProfitRate(),investItems.getDeadlineDays()));
            this.save(investItemsOrder);

            //写入日志
            UserBill userBill = new UserBill();
            userBill.setUid(user.getUid());
            userBill.setLinkId(investItemsOrder.getProjectId().toString());
            userBill.setTitle("投项项目");
            userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
            userBill.setNumber(request.getAmount());
            userBill.setStatus(1);
            userBill.setCreateTime(DateUtil.nowDateTime());
            userBill.setPm(0);
            userBill.setType(investItemsOrder.getRepaymentMethod().toString());
            userBill.setBalance(user.getNowMoney().subtract(request.getAmount()));
            userBill.setMark(StrUtil.format("用户投资"));
            userBillService.save(userBill);
            //扣减用户金额
            userService.operationNowMoney(user.getUid(),request.getAmount(),null,"money_out");
            //更新投资金额
            investItemsService.updateByItemInvest(investItems.getId(),investItemsOrder.getAmount());

                return true;
            } catch (Exception e) {
                // 发生异常，回滚事务
                logger.error("投资发生异常：{},{}",JSON.toJSONString(request),e.getMessage(),e);
                throw new CrmebException(e.getMessage());
            }
        });
    }

    @Override
    public PageInfo<InvestItemsOrder> record(PageParamRequest request) {
        Integer userId = userService.getUserId();
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 InvestItemsOrder 类的多条件查询
        LambdaQueryWrapper<InvestItemsOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestItemsOrder::getUserId,userId);
        lambdaQueryWrapper.orderByDesc(InvestItemsOrder::getCreateTime);
        List<InvestItemsOrder> list = this.list(lambdaQueryWrapper);

        List<Integer> itemsList = list.stream().map(l -> l.getProjectId()).collect(Collectors.toList());

        Map<Integer, InvestItems> investItemsMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(itemsList)) {
            List<InvestItems> investItems = investItemsService.listByIds(itemsList);
            investItemsMap = investItems.stream().collect(Collectors.toMap(investItems1 -> investItems1.getId(), Function.identity()));
        }
        User user = userService.getInfo();
        for (InvestItemsOrder investItemsOrder : list) {
            investItemsOrder.setAccount(user.getAccount());
            InvestItems investItems = investItemsMap.get(investItemsOrder.getProjectId());
            if (investItems != null){
                investItemsOrder.setProjectName(investItems.getProjectName());
            }
        }
        return CommonPage.copyPageInfo(startPage, list);
    }

    @Override
    public Boolean detectExpiredOrders() {
        LambdaQueryWrapper<InvestItemsOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestItemsOrder::getStatus,true);
        lambdaQueryWrapper.le(InvestItemsOrder::getEndTime,new Date());
        lambdaQueryWrapper.last(" limit 1000");
        List<InvestItemsOrder> list = this.list(lambdaQueryWrapper);
        for (InvestItemsOrder investItemsOrder : list) {
            handleExpiredOrder(investItemsOrder);
        }
        return true;
    }
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiredOrder(InvestItemsOrder investItemsOrder) {
        BigDecimal amount = BigDecimal.ZERO;
        User user = userService.getById(investItemsOrder.getUserId());
        switch (investItemsOrder.getRepaymentMethod()){
            case PRINCIPAL_AND_INTEREST_AT_MATURITY:
                //到期还本还利息 投资金额 + 利息
                BigDecimal total = investItemsOrder.getAmount().add(investItemsOrder.getExpectedEarnings());
                userService.operationNowMoney(user.getUid(),total,null,"add");
                investItemsOrder.setStatus(false);
                this.updateById(investItemsOrder);
                amount = total;
                break;
        }
        //写入日志
        UserBill userBill = new UserBill();
        userBill.setUid(user.getUid());
        userBill.setLinkId(investItemsOrder.getId().toString());
        userBill.setTitle(investItemsOrder.getRepaymentMethod().getDescription());
        userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
        userBill.setNumber(amount);
        userBill.setStatus(1);
        userBill.setCreateTime(DateUtil.nowDateTime());
        userBill.setPm(1);
        userBill.setType(investItemsOrder.getRepaymentMethod().toString());
        userBill.setBalance(user.getNowMoney().add(amount));
        userBill.setMark(investItemsOrder.getRepaymentMethod().getDescription());
        userBillService.save(userBill);
    }

    public Integer getInvestCount(Integer userId, Integer projectId){
        LambdaQueryWrapper<InvestItemsOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestItemsOrder::getUserId,userId);
        lambdaQueryWrapper.eq(InvestItemsOrder::getProjectId,projectId);
        //lambdaQueryWrapper.eq(InvestItemsOrder::getStatus,true);
        int count = this.count(lambdaQueryWrapper);
        return count;
    }

    /**
     * 计算指定天数的利息，适用于 到期还本还息
     * @param amount
     * @param profitRate
     * @param days
     * @return
     */
    public BigDecimal calculateProfit(BigDecimal amount, BigDecimal profitRate, Integer days) {
        BigDecimal profit = amount.multiply(profitRate).setScale(2, RoundingMode.HALF_UP);
        profit = profit.divide(new BigDecimal("100")); // 一天的收益
        return profit.multiply(new BigDecimal(days.toString()));
    }

    /**
     * 计算指定项目的投资人数
     *
     * @param startTime 开始时间（可为空，格式如 "2025-05-01 00:00:00"）
     * @param endTime 结束时间（可为空，格式如 "2025-05-31 23:59:59"）
     * @return 投资人数
     */
    @Override
    public Integer getInvestorCount(String startTime, String endTime) {
        LambdaQueryWrapper<InvestItemsOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(InvestItemsOrder::getUserId);
        wrapper.eq(InvestItemsOrder::getStatus, true); // 仅统计有效订单（status = 1）
        wrapper.ge(StrUtil.isNotBlank(startTime), InvestItemsOrder::getCreateTime, startTime);
        wrapper.le(StrUtil.isNotBlank(endTime), InvestItemsOrder::getCreateTime, endTime);
        wrapper.groupBy(InvestItemsOrder::getUserId); // 按 user_id 分组去重
        List<InvestItemsOrder> list = baseMapper.selectList(wrapper);
        return CollUtil.isEmpty(list) ? 0 : list.size();
    }

}
