package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.page.CommonPage;
import com.zbkj.service.dao.UserBankCardsRecordDao;
import com.zbkj.common.model.UserBankCardsRecord;
import com.zbkj.common.request.UserBankCardsRecordSearchRequest;
import com.zbkj.service.service.UserBankCardsRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;



/**
 * 用户绑卡申请记录 接口实现类
 */

@Service
public class UserBankCardsRecordServiceImpl extends ServiceImpl<UserBankCardsRecordDao, UserBankCardsRecord> implements UserBankCardsRecordService {

    /**
     * UserBankCardsRecord列表查询
     *
     * @param request 默认生成搜索的对象 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<UserBankCardsRecord> getList(UserBankCardsRecordSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 UserBankCardsRecord 类的多条件查询
        LambdaQueryWrapper<UserBankCardsRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Objects.nonNull(request.getUserId()), UserBankCardsRecord::getUserId, request.getUserId());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getAccount()), UserBankCardsRecord::getAccount, request.getAccount());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(request.getName()), UserBankCardsRecord::getName, request.getName());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getCardType()), UserBankCardsRecord::getCardType, request.getCardType());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(request.getCardNo()), UserBankCardsRecord::getCardNo, request.getCardNo());
        lambdaQueryWrapper.eq(Objects.nonNull(request.getStatus()), UserBankCardsRecord::getStatus, request.getStatus());
        lambdaQueryWrapper.orderByDesc(UserBankCardsRecord::getCreateTime);
        List<UserBankCardsRecord> list = baseMapper.selectList(lambdaQueryWrapper);
        return CommonPage.copyPageInfo(startPage, list);
    }
    @Override
    public UserBankCardsRecord getBankByCardNo(String cardNo, BanksType banksType) {
        LambdaQueryWrapper<UserBankCardsRecord> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(UserBankCardsRecord::getCardNo, cardNo);
        lambdaQueryWrapper.eq(UserBankCardsRecord::getCardType, banksType);
        lambdaQueryWrapper.orderByDesc(UserBankCardsRecord::getCreateTime);
        lambdaQueryWrapper.last("limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }
    @Override
    public UserBankCardsRecord getBankByCardNo(String cardNo) {
        LambdaQueryWrapper<UserBankCardsRecord> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(UserBankCardsRecord::getCardNo, cardNo);
        lambdaQueryWrapper.orderByDesc(UserBankCardsRecord::getId);
        lambdaQueryWrapper.last("limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public List<UserBankCardsRecord> fetchUserBankCards(Integer userId,BanksType cardType) {
        LambdaQueryWrapper<UserBankCardsRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Objects.nonNull(cardType), UserBankCardsRecord::getCardType, cardType);
        lambdaQueryWrapper.eq(UserBankCardsRecord::getStatus, CertificateStatus.APPROVED);
        lambdaQueryWrapper.eq(UserBankCardsRecord::getUserId, userId);
        List<UserBankCardsRecord> userBankCardsRecords = baseMapper.selectList(lambdaQueryWrapper);
        return userBankCardsRecords;
    }

    @Override
    public UserBankCardsRecord getBankByCardNoAndUserId(Integer userId, String type, String cardNo) {
        LambdaQueryWrapper<UserBankCardsRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserBankCardsRecord::getUserId, userId);
        lambdaQueryWrapper.eq(UserBankCardsRecord::getCardType, type);
        lambdaQueryWrapper.eq(UserBankCardsRecord::getCardNo, cardNo);
        lambdaQueryWrapper.eq(UserBankCardsRecord::getStatus, CertificateStatus.APPROVED);
        lambdaQueryWrapper.last(" limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

}
