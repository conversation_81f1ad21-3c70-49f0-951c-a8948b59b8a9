package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.BroBroadcastingStatus;
import com.zbkj.common.enums.RoomStatus;
import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.dao.LiveRoomDao;
import com.zbkj.service.service.LiveRecordService;
import com.zbkj.service.service.LiveRoomService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class LiveRoomServiceImpl extends ServiceImpl<LiveRoomDao, LiveRoom> implements LiveRoomService {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private LiveRecordService liveRecordService;

    @Override
    @PostConstruct
    public void init() {
        try {
            redisUtil.deleteKeysWithPrefix(Constants.MERCHANT_LIVE_ROOM);
        } catch (Exception e) {
            throw new RuntimeException("直播房间缓存更新失败");
        }
        this.list().forEach(room -> redisUtil.set(String.format(Constants.MERCHANT_LIVE_ROOM, room.getMerchant()), room));
    }

    @Override
    public LiveRoom findByMerchant(Integer merchant) {
        LiveRoom liveRoom = redisUtil.get(String.format(Constants.MERCHANT_LIVE_ROOM, merchant));
        if (liveRoom != null) {
            return liveRoom;
        }
        LambdaQueryWrapper<LiveRoom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveRoom::getMerchant, merchant).last("limit 1");
        LiveRoom one = this.getOne(queryWrapper);
        redisUtil.set(String.format(Constants.MERCHANT_LIVE_ROOM, merchant),one);
        return one;
    }

    @Override
    public boolean deleteByMerchant(Integer merchant) {
        LambdaQueryWrapper<LiveRoom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveRoom::getMerchant, merchant);
        return this.remove(queryWrapper);
    }

    @Override
    public List<LiveRoom> getList(LiveRoom liveRoom, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<LiveRoom> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (!systemAdmin.getIsAdmin()) {
            lambdaQueryWrapper.eq(LiveRoom::getMerchant, systemAdmin.getId());//只能查看自己的店铺(不包括管理员的情况下)
        }
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(liveRoom.getRoomName()), LiveRoom::getRoomName, liveRoom.getRoomName());
        lambdaQueryWrapper.eq(Objects.nonNull(liveRoom.getRoomStatus()), LiveRoom::getRoomStatus, liveRoom.getRoomStatus());
        lambdaQueryWrapper.eq(Objects.nonNull(liveRoom.getBroadcastingStatus()), LiveRoom::getBroadcastingStatus, liveRoom.getBroadcastingStatus());
        lambdaQueryWrapper.orderByDesc(LiveRoom::getRoomId);
        return super.baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoomStatus(Long roomId, RoomStatus status) {
        LiveRoom liveRoom = this.getById(roomId);
        if (liveRoom == null) {
            throw new RuntimeException("直播间不存在");
        }
        if (status == RoomStatus.BANNED) {
            //正在直播，需要关闭
            if (liveRoom.getBroadcastingStatus() == BroBroadcastingStatus.STARTED) {
                liveRoom.setBroadcastingStatus(BroBroadcastingStatus.ENDED);
                LiveRecord liveRecord = liveRecordService.findLastetStartedByRoomId(liveRoom.getRoomId());
                if (liveRecord != null) {
                    liveRecord.setBroadcastingStatus(BroBroadcastingStatus.ENDED);
                    liveRecord.setEndTime(new Date());
                    liveRecordService.updateById(liveRecord);
                }
            }
        }
        liveRoom.setRoomStatus(status);
        return this.updateLiveRoom(liveRoom);
    }

    @Override
    public boolean updateBroadcastStatus(Long roomId, BroBroadcastingStatus broadcastingStatus, Boolean storeWindow) {
        LiveRoom liveRoom = this.getById(roomId);
        liveRoom.setBroadcastingStatus(broadcastingStatus);
        liveRoom.setStoreWindow(storeWindow);
        return this.updateLiveRoom(liveRoom);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean forceStop(Long roomId) {
        LiveRoom liveRoom = this.getById(roomId);
        if (liveRoom == null) {
            throw new RuntimeException("直播间不存在");
        }
        if (liveRoom.getBroadcastingStatus() == BroBroadcastingStatus.STARTED) {
            liveRoom.setBroadcastingStatus(BroBroadcastingStatus.ENDED);
            LiveRecord liveRecord = liveRecordService.findLastetStartedByRoomId(liveRoom.getRoomId());
            if (liveRecord != null) {
                liveRecord.setBroadcastingStatus(BroBroadcastingStatus.ENDED);
                liveRecord.setEndTime(new Date());
                liveRecordService.updateById(liveRecord);
            }
            return this.updateLiveRoom(liveRoom);
        }
        return true;
    }

    @Override
    public boolean updateLiveRoom(LiveRoom liveRoom) {
        boolean updated = this.updateById(liveRoom);
        if(updated){
            redisUtil.set(String.format(Constants.MERCHANT_LIVE_ROOM, liveRoom.getMerchant()), liveRoom);
        }
        return updated;
    }
}
