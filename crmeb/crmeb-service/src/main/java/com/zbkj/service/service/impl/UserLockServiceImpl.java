package com.zbkj.service.service.impl;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.service.UserLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.function.Supplier;

/**
 * 用户锁服务实现类
 * 统一管理用户相关的分布式锁操作
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserLockServiceImpl implements UserLockService {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 用户锁的键前缀
     */
    private static final String USER_LOCK_PREFIX = "user_lock:";

    /**
     * 默认锁超时时间（秒）
     */
    private static final long DEFAULT_LOCK_TIMEOUT = 30L;

    /**
     * 获取锁的最大等待时间（毫秒）
     */
    private static final long MAX_WAIT_TIME = 5000L;

    @Override
    public <T> T executeWithUserLock(Integer userId, Supplier<T> operation) {
        if (userId == null) {
            throw new CrmebException("用户ID不能为空");
        }

        String requestId = generateRequestId();
        String lockKey = getUserLockKey(userId);

        log.debug("尝试获取用户锁，userId: {}, requestId: {}", userId, requestId);

        // 尝试获取锁
        boolean lockAcquired = false;
        try {
            lockAcquired = redisUtil.tryLock(lockKey, requestId, DEFAULT_LOCK_TIMEOUT);
            if (!lockAcquired) {
                log.warn("获取用户锁失败，userId: {}, requestId: {}", userId, requestId);
                throw new CrmebException("系统繁忙，请稍后重试");
            }

            log.debug("成功获取用户锁，userId: {}, requestId: {}", userId, requestId);

            // 执行业务操作
            return operation.get();

        } catch (Exception e) {
            log.error("执行用户锁操作失败，userId: {}, requestId: {}, error: {}", 
                     userId, requestId, e.getMessage(), e);
            throw e;
        } finally {
            // 释放锁
            if (lockAcquired) {
                boolean unlocked = redisUtil.releaseLock(lockKey, requestId);
                if (unlocked) {
                    log.debug("成功释放用户锁，userId: {}, requestId: {}", userId, requestId);
                } else {
                    log.warn("释放用户锁失败，userId: {}, requestId: {}", userId, requestId);
                }
            }
        }
    }

    @Override
    public void executeWithUserLock(Integer userId, Runnable operation) {
        executeWithUserLock(userId, () -> {
            operation.run();
            return null;
        });
    }

    @Override
    public boolean tryLockUser(Integer userId, String requestId, long timeoutSeconds) {
        if (userId == null || requestId == null) {
            return false;
        }

        String lockKey = getUserLockKey(userId);
        return redisUtil.tryLock(lockKey, requestId, timeoutSeconds);
    }

    @Override
    public boolean unlockUser(Integer userId, String requestId) {
        if (userId == null || requestId == null) {
            return false;
        }

        String lockKey = getUserLockKey(userId);
        return redisUtil.releaseLock(lockKey, requestId);
    }

    @Override
    public String getUserLockKey(Integer userId) {
        return USER_LOCK_PREFIX + userId;
    }

    @Override
    public String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "") + "_" + Thread.currentThread().getId();
    }
}
