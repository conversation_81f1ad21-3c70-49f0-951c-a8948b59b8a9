package com.zbkj.service.service.impl;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.utils.RedisCache;
import com.zbkj.service.service.UserLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 用户锁服务实现类
 * 统一管理用户相关的分布式锁操作
 *
 * 设计说明：
 * - 所有用户相关操作都使用同一把锁（user_lock_{userId}）
 * - 这样设计确保同一用户的所有数据更新操作都是串行的，保证数据一致性
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserLockServiceImpl implements UserLockService {

    @Autowired
    private RedisCache redisCache;

    /**
     * 用户锁的键前缀 - 统一格式，与项目中其他地方保持一致
     * 所有用户相关操作都使用同一把锁，确保数据一致性
     */
    private static final String USER_LOCK_PREFIX = "user_lock_";

    /**
     * 默认锁超时时间（秒）
     */
    private static final long DEFAULT_LOCK_TIMEOUT = 30L;

    /**
     * 获取锁的最大等待时间（毫秒）
     */
    private static final long MAX_WAIT_TIME = 5000L;

    @Override
    public <T> T executeWithUserLock(Integer userId, Supplier<T> operation) {
        if (userId == null) {
            throw new CrmebException("用户ID不能为空");
        }

        String requestId = generateRequestId();
        String lockKey = getUserLockKey(userId);

        log.debug("尝试获取用户锁，userId: {}, requestId: {}", userId, requestId);

        // 尝试获取锁 - 使用RedisCache的tryLock方法，支持等待时间
        boolean lockAcquired = false;
        try {
            lockAcquired = redisCache.tryLock(lockKey, requestId, MAX_WAIT_TIME, DEFAULT_LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("获取用户锁失败，userId: {}, requestId: {}", userId, requestId);
                throw new CrmebException("系统繁忙，请稍后重试");
            }

            log.debug("成功获取用户锁，userId: {}, requestId: {}", userId, requestId);

            // 执行业务操作
            return operation.get();

        } catch (Exception e) {
            log.error("执行用户锁操作失败，userId: {}, requestId: {}, error: {}",
                     userId, requestId, e.getMessage(), e);
            throw e;
        } finally {
            // 释放锁
            if (lockAcquired) {
                boolean unlocked = redisCache.unlock(lockKey, requestId);
                if (unlocked) {
                    log.debug("成功释放用户锁，userId: {}, requestId: {}", userId, requestId);
                } else {
                    log.warn("释放用户锁失败，userId: {}, requestId: {}", userId, requestId);
                }
            }
        }
    }

    @Override
    public void executeWithUserLock(Integer userId, Runnable operation) {
        executeWithUserLock(userId, () -> {
            operation.run();
            return null;
        });
    }

    @Override
    public boolean tryLockUser(Integer userId, String requestId, long timeoutSeconds) {
        if (userId == null || requestId == null) {
            return false;
        }

        String lockKey = getUserLockKey(userId);
        return redisCache.tryLock(lockKey, requestId, MAX_WAIT_TIME, timeoutSeconds, TimeUnit.SECONDS);
    }

    @Override
    public boolean unlockUser(Integer userId, String requestId) {
        if (userId == null || requestId == null) {
            return false;
        }

        String lockKey = getUserLockKey(userId);
        return redisCache.unlock(lockKey, requestId);
    }

    @Override
    public String getUserLockKey(Integer userId) {
        return USER_LOCK_PREFIX + userId;
    }

    @Override
    public String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "") + "_" + Thread.currentThread().getId();
    }
}
