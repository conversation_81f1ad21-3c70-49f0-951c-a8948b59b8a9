package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.config.CrmebConfig;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.enums.RepaymentMethod;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.InvestItemsOrder;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.*;
import com.zbkj.common.response.*;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.ExportUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.*;
import com.zbkj.service.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ExcelServiceImpl 接口实现
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    @Autowired
    private InvestItemsOrderService investItemsOrderService;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private StoreBargainService storeBargainService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private StoreCombinationService storeCombinationService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private CrmebConfig crmebConfig;

    @Autowired
    private UserService userService;

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private UserExtractService userExtractService;

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 导出砍价商品
     *
     * @param request 请求参数
     * @return 导出地址
     */
    @Override
    public String exportBargainProduct(StoreBargainSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<StoreBargainResponse> page = storeBargainService.getList(request, pageParamRequest);
        if (CollUtil.isEmpty(page.getList())) throw new CrmebException("没有可导出的数据!");
        List<StoreBargainResponse> list = page.getList();
        List<BargainProductExcelVo> voList = list.stream().map(temp -> {
            BargainProductExcelVo vo = new BargainProductExcelVo();
            BeanUtils.copyProperties(temp, vo);
            vo.setPrice("￥".concat(temp.getPrice().toString()));
            vo.setStatus(temp.getStatus() ? "开启" : "关闭");
            vo.setStartTime(temp.getStartTime());
            vo.setStopTime(temp.getStopTime());
            vo.setAddTime(temp.getAddTime());
            return vo;
        }).collect(Collectors.toList());

        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);

        // 文件名
        String fileName = "砍价".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");

        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("title", "砍价活动名称");
        aliasMap.put("info", "砍价活动简介");
        aliasMap.put("price", "砍价金额");
        aliasMap.put("bargainNum", "用户每次砍价的次数");
        aliasMap.put("status", "砍价状态");
        aliasMap.put("startTime", "砍价开启时间");
        aliasMap.put("stopTime", "砍价结束时间");
        aliasMap.put("sales", "销量");
        aliasMap.put("quotaShow", "库存");
        aliasMap.put("giveIntegral", "返多少积分");
        aliasMap.put("addTime", "添加时间");

        return ExportUtil.exportExcel(fileName, "砍价商品导出", voList, aliasMap);
    }

    /**
     * 导出拼团商品
     *
     * @param request 请求参数
     * @return 导出地址
     */
    @Override
    public String exportCombinationProduct(StoreCombinationSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<StoreCombinationResponse> page = storeCombinationService.getList(request, pageParamRequest);
        if (CollUtil.isEmpty(page.getList())) throw new CrmebException("没有可导出的数据!");
        List<StoreCombinationResponse> list = page.getList();
        List<CombinationProductExcelVo> voList = list.stream().map(temp -> {
            CombinationProductExcelVo vo = new CombinationProductExcelVo();
            BeanUtils.copyProperties(temp, vo);
            vo.setIsShow(temp.getIsShow() ? "开启" : "关闭");
            vo.setStopTime(DateUtil.timestamp2DateStr(temp.getStopTime(), Constants.DATE_FORMAT_DATE));
            return vo;
        }).collect(Collectors.toList());

        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);

        // 文件名
        String fileName = "拼团".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");

        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("id", "编号");
        aliasMap.put("title", "拼团名称");
        aliasMap.put("otPrice", "原价");
        aliasMap.put("price", "拼团价");
        aliasMap.put("quotaShow", "库存");
        aliasMap.put("countPeople", "拼团人数");
        aliasMap.put("countPeopleAll", "参与人数");
        aliasMap.put("countPeoplePink", "成团数量");
        aliasMap.put("sales", "销量");
        aliasMap.put("isShow", "商品状态");
        aliasMap.put("stopTime", "拼团结束时间");

        return ExportUtil.exportExcel(fileName, "拼团商品导出", voList, aliasMap);
    }

    /**
     * 商品导出
     *
     * @param request 请求参数
     * @return 导出地址
     */
    @Override
    public String exportProduct(StoreProductSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<StoreProductResponse> storeProductResponsePageInfo = storeProductService.getAdminList(request, pageParamRequest);
        List<StoreProductResponse> list = storeProductResponsePageInfo.getList();
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }

        //产品分类id
        List<String> cateIdListStr = list.stream().map(StoreProductResponse::getCateId).distinct().collect(Collectors.toList());

        HashMap<Integer, String> categoryNameList = new HashMap<Integer, String>();
        if (cateIdListStr.size() > 0) {
            String join = StringUtils.join(cateIdListStr, ",");
            List<Integer> cateIdList = CrmebUtil.stringToArray(join);
            categoryNameList = categoryService.getListInId(cateIdList);
        }
        List<ProductExcelVo> voList = CollUtil.newArrayList();
        for (StoreProductResponse product : list) {
            ProductExcelVo vo = new ProductExcelVo();
            vo.setStoreName(product.getStoreName());
            vo.setStoreInfo(product.getStoreInfo());
            vo.setCateName(CrmebUtil.getValueByIndex(categoryNameList, product.getCateId()));
            vo.setPrice("￥" + product.getPrice());
            vo.setStock(product.getStock().toString());
            vo.setSales(product.getSales().toString());
            vo.setBrowse(product.getBrowse().toString());
            voList.add(vo);
        }

        /**
         * ===============================
         * 以下为存储部分
         * ===============================
         */
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);

        // 文件名
        String fileName = "商品导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");

        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("storeName", "商品名称");
        aliasMap.put("storeInfo", "商品简介");
        aliasMap.put("cateName", "商品分类");
        aliasMap.put("price", "价格");
        aliasMap.put("stock", "库存");
        aliasMap.put("sales", "销量");
        aliasMap.put("browse", "浏览量");

        String path = ExportUtil.exportExcel(fileName, "商品导出", voList, aliasMap);
        return path;
    }

    /**
     * 订单导出
     *
     * @param request 查询条件
     * @return 文件名称
     */
    @Override
    public String exportOrder(StoreOrderSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        CommonPage<StoreOrderDetailResponse> adminList = storeOrderService.getAdminList(request, pageParamRequest);
        List<StoreOrderDetailResponse> list = adminList.getList();
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }

        List<OrderExcelVo> voList = CollUtil.newArrayList();
        for (StoreOrderDetailResponse order : list) {
            try {
                OrderExcelVo vo = new OrderExcelVo();
                vo.setCreateTime(DateUtil.dateToStr(order.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                vo.setOrderId(order.getOrderId());
                vo.setOrderType(order.getOrderType());
                vo.setPayPrice(order.getPayPrice() + "");
                vo.setPayTypeStr(order.getPayTypeStr());
                String productName = order.getProductList() != null ? order.getProductList().stream().map(item -> item.getInfo().getProductName() + " | " + item.getInfo().getSku()).collect(Collectors.joining(",")) : "无商品信息";
                vo.setProductName(productName);
                vo.setRealName(order.getRealName());
                vo.setStatusStr(order.getStatusStr().get("value"));
                vo.setProductCount(order.getTotalNum());
                vo.setUserPhone(order.getUserPhone());
                vo.setUserAddress(order.getUserAddress());
                voList.add(vo);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        /*
          ===============================
          以下为存储部分
          ===============================
         */
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);

        // 文件名
        String fileName = "订单导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");

        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("orderId", "订单号");
        aliasMap.put("payPrice", "实际支付金额");
//        aliasMap.put("payType", "支付方式");
        aliasMap.put("createTime", "创建时间");
//        aliasMap.put("status", "订单状态");
        aliasMap.put("productName", "商品信息");
        aliasMap.put("productCount", "商品数量");
        aliasMap.put("statusStr", "订单状态");
        aliasMap.put("payTypeStr", "支付方式");
//        aliasMap.put("isDel", "是否删除");
//        aliasMap.put("refundReasonWapImg", "退款图片");
//        aliasMap.put("refundReasonWapExplain", "退款用户说明");
//        aliasMap.put("refundReasonTime", "退款时间");
//        aliasMap.put("refundReasonWap", "前台退款原因");
//        aliasMap.put("refundReason", "不退款的理由");
//        aliasMap.put("refundPrice", "退款金额");
//        aliasMap.put("refundStatus", "退款状态状态，0 未退款 1 申请中 2 已退款");
//        aliasMap.put("verifyCode", "核销码");
        aliasMap.put("orderType", "订单类型");
//        aliasMap.put("remark", "订单管理员备注");
        aliasMap.put("realName", "用户姓名");
        aliasMap.put("userPhone", "号码");
        aliasMap.put("userAddress", "地址");
//        aliasMap.put("paid", "支付状态");
//        aliasMap.put("type", "订单类型:0-普通订单，1-视频号订单");
//        aliasMap.put("isAlterPrice", "是否改价,0-否，1-是");

        return ExportUtil.exportExcel(fileName, "订单导出", voList, aliasMap);

    }

    @Override
    public String exportUserInfo(UserSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<UserResponse> userServiceList = userService.getList(request, pageParamRequest);
        List<UserResponse> list = userServiceList.getList();
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }

        List<UserExcelVo> voList = CollUtil.newArrayList();
        for (UserResponse response : list) {
            UserExcelVo vo = new UserExcelVo();
            BeanUtils.copyProperties(response, vo);
            vo.setAuthenticationStatus(response.getAuthenticationStatus().getType());
            voList.add(vo);
        }

        /**
         * ===============================
         * 以下为存储部分
         * ===============================
         */
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);

        // 文件名
        String fileName = "用户信息导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");

        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("uid", "用户id");
        aliasMap.put("account", "用户账号");
        aliasMap.put("realName", "真实姓名");
        aliasMap.put("birthday", "生日");
        aliasMap.put("groupName", "分组名称");
        aliasMap.put("nickname", "用户昵称");
        aliasMap.put("numberCode", "冠号");
        aliasMap.put("phone", "手机号码");
        aliasMap.put("lastIp", "最后一次登录ip");
        aliasMap.put("nowMoney", "用户余额");
        aliasMap.put("brokeragePrice", "佣金金额");
        aliasMap.put("integral", "用户剩余积分");
        aliasMap.put("experience", "用户剩余经验");
        aliasMap.put("signNum", "连续签到天数");
        aliasMap.put("status", "账号状态");
        aliasMap.put("level", "等级");
        aliasMap.put("payCount", "用户购买次数");
        aliasMap.put("addres", "详细地址");
        aliasMap.put("createTime", "注册时间");
        aliasMap.put("lastLoginTime", "最后一次登录时间");
        aliasMap.put("rechargeAmount", "充值总金额");
        aliasMap.put("withdrawAmount", "提现总金额");
        aliasMap.put("frozenBalance", "冻结余额");
        aliasMap.put("balanceInterest", "余额利息%");
        aliasMap.put("withdrawFee", "提现手续费%");
        aliasMap.put("authenticationStatus", "认证状态");
        aliasMap.put("invitationCode", "邀请码");
        return ExportUtil.exportExcel(fileName, "用户信息导出", voList, aliasMap);
    }

    @Override
    public String exportUserAuth(UserAuthSearchRequest request) {
        request.setPage(Constants.DEFAULT_PAGE);
        request.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<UserAuthResponse> userServiceList = userAuthService.getList(request);
        List<UserAuthResponse> list = userServiceList.getList();
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }
        List<UserAuthExcelVo> userAuthExcelVoList = new ArrayList<>(list.size());
        for (UserAuthResponse userAuthResponse : list) {
            //处理图片路径
            String cerPic = userAuthResponse.getCerPic();
            String imageName = cerPic.substring(cerPic.lastIndexOf("/") + 1);
            userAuthResponse.setCerPic(imageName);

            //对象转换
            UserAuthExcelVo userAuthExcelVo = new UserAuthExcelVo();
            BeanUtils.copyProperties(userAuthResponse, userAuthExcelVo);
            //枚举转换字符串
            userAuthExcelVo.setType(userAuthResponse.getType().getType());
            userAuthExcelVo.setStatus(userAuthResponse.getStatus().getType());
            userAuthExcelVoList.add(userAuthExcelVo);
        }
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);
        // 文件名
        String fileName = "实名信息导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");
        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("userId", "用户id");
        aliasMap.put("account", "账号");
        aliasMap.put("type", "类型");
        aliasMap.put("name", "姓名");
        aliasMap.put("cerNo", "证件号码");
        aliasMap.put("cerPic", "证件图片");
        aliasMap.put("createTime", "申请时间");
        aliasMap.put("reviewTime", "审核时间");
        aliasMap.put("status", "状态");
        aliasMap.put("certifyId", "唯一标识");
        aliasMap.put("result", "处理结果");
        aliasMap.put("remark", "备注");
        return ExportUtil.exportExcel(fileName, "实名信息导出", userAuthExcelVoList, aliasMap);
    }

    @Override
    public String exportUserExtract(UserExtractSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        List<UserExtract> list = userExtractService.getList(request, pageParamRequest);
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }
        List<UserExtractExcelVo> voList = new ArrayList<>(list.size());
        for (UserExtract userExtract : list) {
            UserExtractExcelVo userExtractExcelVo = new UserExtractExcelVo();
            BeanUtils.copyProperties(userExtract, userExtractExcelVo);
            if (userExtract.getExtractType() == "alipay") {
                userExtractExcelVo.setBankCode(userExtract.getAlipayCode());
            } else if (userExtract.getExtractType() == "weixin") {
                userExtractExcelVo.setBankCode(userExtract.getWechat());
            }
            if (userExtract.getStatus() == -1) {
                userExtractExcelVo.setStatus("未通过");
            } else if (userExtract.getStatus() == 0) {
                userExtractExcelVo.setStatus("审核中");
            } else if (userExtract.getStatus() == 1) {
                userExtractExcelVo.setStatus("已提现");
            } else if (userExtract.getStatus() == 2) {
                userExtractExcelVo.setStatus("代付中");
            } else if (userExtract.getStatus() == 3) {
                userExtractExcelVo.setStatus("代付完成");
            } else if (userExtract.getStatus() == 4) {
                userExtractExcelVo.setStatus("代付失败");
            } else {
                // 其他未知状态，根据需求进行处理
                userExtractExcelVo.setStatus("未知状态");
            }
            BanksType byValue = BanksType.getByValue(userExtract.getExtractType());
            userExtractExcelVo.setExtractType(byValue.getType());
            voList.add(userExtractExcelVo);
        }
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);
        // 文件名
        String fileName = "用户提现导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");
        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("uid", "用户ID");
        aliasMap.put("account", "账号");
        aliasMap.put("extractPrice", "提现金额");
        aliasMap.put("fee", "手续费");
        aliasMap.put("extractType", "提现方式");
        aliasMap.put("realName", "真实姓名");
        aliasMap.put("bankCode", "收款账号");
        aliasMap.put("qrcodeUrl", "收款码");
        aliasMap.put("status", "审核状态");
        aliasMap.put("updateTime", "更新时间");
        aliasMap.put("createTime", "创建时间");
        aliasMap.put("failMsg", "处理结果");
        return ExportUtil.exportExcel(fileName, "用户提现导出", voList, aliasMap);
    }

    @Override
    public String exportUserRecharge(UserRechargeSearchRequest request) {
        PageParamRequest pageParamRequest = new PageParamRequest();
        pageParamRequest.setPage(Constants.DEFAULT_PAGE);
        pageParamRequest.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<UserRechargeResponse> userServiceList = userRechargeService.getList(request, pageParamRequest);
        List<UserRechargeResponse> list = userServiceList.getList();
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }
        List<UserRechargeExcelVo> userRechargeExcelVoList = new ArrayList<>(list.size());
        for (UserRechargeResponse userRechargeResponse : list) {
            UserRechargeExcelVo userRechargeExcelVo = new UserRechargeExcelVo();
            BeanUtils.copyProperties(userRechargeResponse, userRechargeExcelVo);
            if (userRechargeResponse.getPaid() == 0) {
                userRechargeExcelVo.setRechargeState("提交充值");
            } else if (userRechargeResponse.getPaid() == 1) {
                userRechargeExcelVo.setRechargeState("已充值到账");
            } else if (userRechargeResponse.getPaid() == 2) {
                userRechargeExcelVo.setRechargeState("待审核");
            } else if (userRechargeResponse.getPaid() == 3) {
                userRechargeExcelVo.setRechargeState("充值失败");
            }else {
                userRechargeExcelVo.setRechargeState("未知状态");
            }
            userRechargeExcelVoList.add(userRechargeExcelVo);
        }
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);
        // 文件名
        String fileName = "用户充值导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");
        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("uid", "用户id");
        aliasMap.put("account", "账号");
        aliasMap.put("orderId", "订单号");
        aliasMap.put("price", "充值金额");
        aliasMap.put("givePrice", "赠送金额");
        aliasMap.put("rechargeType", "充值类型");
        aliasMap.put("rechargeState", "充值状态");
        aliasMap.put("createTime", "创建时间");
        aliasMap.put("payTime", "支付时间");
        aliasMap.put("submitPic", "提交图片");
        aliasMap.put("reviewTime", "审核时间");
        aliasMap.put("reviewBy", "审核人");
        aliasMap.put("remark", "备注");
        return ExportUtil.exportExcel(fileName, "用户充值导出", userRechargeExcelVoList, aliasMap);
    }

    public String exportInvestItemsOrder(InvestItemsOrderSearchRequest request) {
        request.setPage(Constants.DEFAULT_PAGE);
        request.setLimit(Constants.EXPORT_MAX_LIMIT);
        PageInfo<InvestItemsOrder> investItemsOrderPage = investItemsOrderService.getList(request);
        List<InvestItemsOrder> list = investItemsOrderPage.getList();
        if (list.size() < 1) {
            throw new CrmebException("没有可导出的数据！");
        }

        List<InvestItemsOrderVo> voList = CollUtil.newArrayList();
        for (InvestItemsOrder response : list) {
            InvestItemsOrderVo vo = new InvestItemsOrderVo();
            BeanUtils.copyProperties(response, vo);
            if (response.getStatus()) {
                vo.setStatus("开启");
            } else {
                vo.setStatus("已结束");
            }
            if (response.getRepaymentMethod() == RepaymentMethod.PRINCIPAL_AND_INTEREST_AT_MATURITY) {
                vo.setRepaymentMethod("到期还本还息");
            }
            voList.add(vo);
        }

        /**
         * ===============================
         * 以下为存储部分
         * ===============================
         */
        // 上传设置
        //ExportUtil.setUpload(crmebConfig.getImagePath(), Constants.UPLOAD_MODEL_PATH_EXCEL, Constants.UPLOAD_TYPE_FILE);

        // 文件名
        String fileName = "已投项目导出_".concat(DateUtil.nowDateTime(Constants.DATE_TIME_FORMAT_NUM)).concat(CrmebUtil.randomCount(*********, *********).toString()).concat(".xlsx");

        //自定义标题别名
        LinkedHashMap<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("id", "ID");
        aliasMap.put("projectId", "项目ID");
        aliasMap.put("userId", "用户ID");
        aliasMap.put("deadlineDays", "期限天数");
        aliasMap.put("repaymentMethod", "还款方式");
        aliasMap.put("amount", "投资金额");
        aliasMap.put("startTime", "开始时间");
        aliasMap.put("endTime", "结束时间");
        aliasMap.put("remark", "备注");
        aliasMap.put("createTime", "创建时间");
        aliasMap.put("status", "状态");
        aliasMap.put("expectedEarnings", "预计收益");
        aliasMap.put("profitRate", "收益率百分比（%）");
        aliasMap.put("account", "用户账号");
        aliasMap.put("projectName", "项目名称");
        aliasMap.put("groupId", "用户分组id");
        return ExportUtil.exportExcel(fileName, "用户信息导出", voList, aliasMap);
    }

}

