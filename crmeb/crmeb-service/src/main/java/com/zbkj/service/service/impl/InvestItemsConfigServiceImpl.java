package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.system.SystemUserLevel;
import com.zbkj.common.page.CommonPage;
import com.zbkj.service.dao.InvestItemsConfigDao;
import com.zbkj.common.model.InvestItemsConfig;
import com.zbkj.common.request.InvestItemsConfigSearchRequest;
import com.zbkj.service.service.InvestItemsConfigService;
import com.zbkj.service.service.SystemUserLevelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 投资项目等级收益配置 接口实现类
 */

@Service
public class InvestItemsConfigServiceImpl extends ServiceImpl<InvestItemsConfigDao, InvestItemsConfig> implements InvestItemsConfigService {

    @Resource
    private SystemUserLevelService systemUserLevelService;

    /**
     * InvestItemsConfig列表查询
     *
     * @param request 默认生成搜索的对象 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<InvestItemsConfig> getList(InvestItemsConfigSearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 InvestItemsConfig 类的多条件查询
        LambdaQueryWrapper<InvestItemsConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestItemsConfig::getItemId, request.getItemId());
        List<InvestItemsConfig> list = baseMapper.selectList(lambdaQueryWrapper);
        Map<Integer, SystemUserLevel> systemUserLevelMap;
        if (list.size() > 0) {
            List<Integer> integerList = list.stream().map(l -> l.getLevelId()).collect(Collectors.toList());
            List<SystemUserLevel> systemUserLevelList = systemUserLevelService.listByIds(integerList);
            systemUserLevelMap = systemUserLevelList.stream().collect(Collectors.toMap(systemUserLevel -> systemUserLevel.getId(), Function.identity()));
        } else {
            systemUserLevelMap = new HashMap<>();
        }
        list.stream().map(l->{
            SystemUserLevel systemUserLevel = systemUserLevelMap.get(l.getLevelId());
            if (systemUserLevel != null) {
                l.setLevelName(systemUserLevel.getName());
            }
            return l;
        });
        return CommonPage.copyPageInfo(startPage, list);
    }

    @Override
    public Boolean deleteByItemId(Integer itemId) {
        LambdaQueryWrapper<InvestItemsConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestItemsConfig::getItemId, itemId);
        return baseMapper.delete(lambdaQueryWrapper) > 0;
    }

    @Override
    public InvestItemsConfig getItemConfig(Integer itemId, Integer levelId) {
        LambdaQueryWrapper<InvestItemsConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestItemsConfig::getItemId, itemId);
        lambdaQueryWrapper.eq(InvestItemsConfig::getLevelId, levelId);
        lambdaQueryWrapper.last("limit 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public List<InvestItemsConfig> getItemConfig(List<Integer> itemId) {
        LambdaQueryWrapper<InvestItemsConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(InvestItemsConfig::getItemId, itemId);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

}
