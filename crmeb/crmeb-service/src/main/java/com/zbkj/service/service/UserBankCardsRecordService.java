package com.zbkj.service.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.model.UserBankCardsRecord;
import com.zbkj.common.request.UserBankCardsRecordSearchRequest;
import java.util.List;

/**
 * 用户绑卡申请记录 业务接口
 */
public interface UserBankCardsRecordService extends IService<UserBankCardsRecord> {
    /**
     * UserBankCardsRecord 列表查询
     * @param request 查询条件对象
     * @return
     */
    PageInfo<UserBankCardsRecord> getList(UserBankCardsRecordSearchRequest request);

    UserBankCardsRecord getBankByCardNo(String cardNo, BanksType banksType);

    /**
     * 根据账号获取信息
     * @param cardNo
     * @return
     */
    UserBankCardsRecord getBankByCardNo(String cardNo);

    /**
     * 我的账户
     * @param cardType
     * @return
     */
    List<UserBankCardsRecord> fetchUserBankCards(Integer userId, BanksType cardType);

    /**
     * 根据用户ID和卡号和账户类型
     * @param cardNo
     * @param userId
     * @return
     */
    UserBankCardsRecord getBankByCardNoAndUserId( Integer userId,String type,String cardNo);
}

