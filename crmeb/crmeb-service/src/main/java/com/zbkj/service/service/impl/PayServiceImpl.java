package com.zbkj.service.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.model.finance.UserRecharge;
import com.zbkj.common.response.*;
import com.zbkj.common.utils.CommonUtil;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.RSAEncryptionUtil;
import com.zbkj.service.service.*;
import com.zbkj.service.util.OkHttpUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PreDestroy;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;


/**
 * 微信支付
 */
@Data
@Service
@Slf4j
public class PayServiceImpl implements PayService {

    private static final Logger logger = LoggerFactory.getLogger(PayServiceImpl.class);

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private UserRechargeService userRechargeService;
    @Autowired
    private UserExtractService userExtractService;
    @Autowired
    private RechargePayService rechargePayService;
    @Override
    public MPayCreatePaymentResponse mPayRequest(String price, String orderId) {

        String mpApi = systemConfigService.getValueByKeyException("mpApi");
        String mpMerchantId = systemConfigService.getValueByKeyException("mpMerchantId");
        String mpCallbackUrl = systemConfigService.getValueByKeyException("mpCallbackUrl");
        String mpMd5Key = systemConfigService.getValueByKeyException("mpMd5Key");
        String mpPublicKey = systemConfigService.getValueByKeyException("mpPublicKey");
        // 构建表四中的参数
        JSONObject params = new JSONObject();
        params.put("merchOrderId", orderId);
        params.put("amount", Double.parseDouble(price));
        params.put("callBackUrl", mpCallbackUrl);
        // 将参数转换为JSON字符串
        String paramsJson = params.toString();
        // 对参数进行RSA加密并base64编码
        String encryptedParams = RSAEncryptionUtil.encryptAndBase64Encode(paramsJson, mpPublicKey);
        // 计算签名
        long t = System.currentTimeMillis() / 1000; // 当前时间戳
        String signature = DigestUtils.md5Hex(mpMerchantId + encryptedParams + t + mpMd5Key);
        // 构建请求参数
        String param = "?merchId=" + mpMerchantId + "&body=" + encryptedParams + "&t=" + t + "&key=" + signature;
        log.info("mPayRequest，请求参数={}", param);
        String response = null;
        try {
            response = new OkHttpUtils().get(mpApi + "/api/v2.1/merch/user/up" + param);
            log.info("mPayRequest response={}", JSON.toJSONString(response));
            return JSON.parseObject(response, MPayCreatePaymentResponse.class);
        } catch (Exception e) {
            log.error("mPayRequest，json解析異常，response={}，异常讯息={}",
                    JSON.toJSONString(response), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public MPayCreatePaymentResponse mPayAgentRequest(String price, String orderId, String address) {
        String mpApi = systemConfigService.getValueByKeyException("mpApi");
        String mpMerchantId = systemConfigService.getValueByKeyException("mpMerchantId");
        String mpAgentCallbackUrl = systemConfigService.getValueByKeyException("mpAgentCallbackUrl");
        String mpMd5Key = systemConfigService.getValueByKeyException("mpMd5Key");
        String mpPublicKey = systemConfigService.getValueByKeyException("mpPublicKey");
        // 构建表四中的参数
        JSONObject params = new JSONObject();
        params.put("merchOrderId", orderId);
        params.put("amount", Double.parseDouble(price));
        params.put("callBackUrl", mpAgentCallbackUrl);
        params.put("userWalletAddress", address);
        // 将参数转换为JSON字符串
        String paramsJson = params.toString();
        // 对参数进行RSA加密并base64编码
        String encryptedParams = RSAEncryptionUtil.encryptAndBase64Encode(paramsJson, mpPublicKey);
        // 计算签名
        long t = System.currentTimeMillis() / 1000; // 当前时间戳
        String signature = DigestUtils.md5Hex(mpMerchantId + encryptedParams + t + mpMd5Key);
        // 构建请求参数
        String param = "?merchId=" + mpMerchantId + "&body=" + encryptedParams + "&t=" + t + "&key=" + signature;
        log.info("mPayAgentRequest请求参数={}", param);
        String response = null;
        try {
            response = new OkHttpUtils().get(mpApi + "/api/v2.1/merch/user/down" + param);
            log.info("mPayAgentRequest response={}", JSON.toJSONString(response));
            return JSON.parseObject(response, MPayCreatePaymentResponse.class);
        } catch (Exception e) {
            log.error("mPayAgentRequest，json解析異常，response={}，异常讯息={}",
                    JSON.toJSONString(response), e.getMessage(), e);
        }
        return null;
    }

    @Override
    public FPayCreatePaymentResponse fPayRequest(String price, String orderId) {
        String fpApiKey = systemConfigService.getValueByKeyException("fpApiKey");
        String fpApi = systemConfigService.getValueByKeyException("fpApi");
        String fpMerchantId = systemConfigService.getValueByKeyException("fpMerchantId");
        String fpCallbackUrl = systemConfigService.getValueByKeyException("fpCallbackUrl");
        String fpReturnUrl = systemConfigService.getValueByKeyException("fpReturnUrl");


        Map<String, Object> orderData = new HashMap<>();
        orderData.put("merchantid", fpMerchantId);
        orderData.put("orderid", orderId);
        orderData.put("amount", price);
        orderData.put("notify_url", fpCallbackUrl);
        orderData.put("return_url", fpReturnUrl);
        String string = CommonUtil.generateSignature(orderData) + fpApiKey;
        String sign = DigestUtils.md5Hex(string).toUpperCase();
        orderData.put("sign", sign);

        try {
            String response = new OkHttpUtils().post(fpApi + "/v1/payment/add", orderData);
            if (StringUtils.isNotEmpty(response) && response.contains("code")) {
                FPayCreatePaymentResponse fPayCreatePaymentResponse = JSON.parseObject(response, FPayCreatePaymentResponse.class);
                if ("0000".equals(fPayCreatePaymentResponse.getCode())) {
                    return fPayCreatePaymentResponse;
                }
            }

        } catch (Exception e) {
            logger.error("FPAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public WanbPayCreatePaymentResponse wanbPayRequest(String price, String orderId) {
        String ApiKey = systemConfigService.getValueByKeyException("wbApiKey");
        String Api = systemConfigService.getValueByKeyException("wbApi");
        String MerchantId = systemConfigService.getValueByKeyException("wbMerchantId");
        String CallbackUrl = systemConfigService.getValueByKeyException("wbCallbackUrl");

        double amount = Double.parseDouble(price);
        String formattedPrice = String.format("%.2f", amount);

        Map<String, Object> orderData = new HashMap<>();
        orderData.put("userid", MerchantId);
        orderData.put("amount", formattedPrice);
        orderData.put("orderid", orderId);
        orderData.put("notifyurl", CallbackUrl);
        orderData.put("currency", "wanb");
        String sign = DigestUtils.md5Hex(MerchantId + formattedPrice + orderId + CallbackUrl + ApiKey);
        orderData.put("sign", sign);

        try {
            String response = new OkHttpUtils().post(Api + "/order/merchantapi/addrecharge", orderData);
            if (StringUtils.isNotEmpty(response) && response.contains("code")) {
                WanbPayCreatePaymentResponse wanbPayCreatePaymentResponse = JSON.parseObject(response, WanbPayCreatePaymentResponse.class);
                wanbPayCreatePaymentResponse.setDataJSON(wanbPayCreatePaymentResponse.getDataObject());
                if (wanbPayCreatePaymentResponse.getCode() == 1) {
                    return wanbPayCreatePaymentResponse;
                }
            }

        } catch (Exception e) {
            logger.error("WanbPAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public Boolean fyPayRequest(String price, String orderId) {

        String fyMerchantId = systemConfigService.getValueByKey("fyMerchantId");
        String fyCurrencyCode = systemConfigService.getValueByKey("fyCurrencyCode");
        String fyCoinCode = systemConfigService.getValueByKey("fyCoinCode");
        String fyJumpUrl = systemConfigService.getValueByKey("fyJumpUrl");
        String fyNoticeUrl = systemConfigService.getValueByKey("fyNoticeUrl");
        String fyLanguage = systemConfigService.getValueByKey("fyLanguage");
        String fyMethod = systemConfigService.getValueByKey("fyMethod");
        String fyRequestUrl = systemConfigService.getValueByKey("fyRequestUrl");
        String fyApiKey = systemConfigService.getValueByKey("fyApiKey");


        // Body parameters
        Map<String, Object> bodyParams = new LinkedHashMap<>();
        bodyParams.put("user_id", fyMerchantId);
        bodyParams.put("currency_money", price);
        bodyParams.put("currency_code", fyCurrencyCode);
        bodyParams.put("coin_code", fyCoinCode);
        bodyParams.put("asyn_notice_url", fyNoticeUrl);
        bodyParams.put("sync_jump_url", fyJumpUrl);
        bodyParams.put("user_order_id", orderId);
        bodyParams.put("language", fyLanguage);
        bodyParams.put("user_custom_id", "1");
        bodyParams.put("remark", "");

        long timestamp = System.currentTimeMillis() / 1000;

        String bodyJson = new Gson().toJson(bodyParams);

        String REQUEST_PATH = "/api/order/create";


        String dataToSign = timestamp + fyMerchantId + fyMethod + REQUEST_PATH + bodyJson;
        String signature = generateHmacSHA256Signature(dataToSign, fyApiKey);

        // Create the request
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, bodyJson);
        Request request = new Request.Builder()
                .url(fyRequestUrl + REQUEST_PATH)
                .method("POST", body)
                .addHeader("X-ACCESS-SIGN", signature)
                .addHeader("X-ACCESS-TIMESTAMP", String.valueOf(timestamp))
                .addHeader("X-ACCESS-USERID", fyMerchantId)
                .build();

        // Send the request
        try (Response response = client.newCall(request).execute()) {
            System.out.println(response.body().string());
        } catch (Exception e) {
            logger.error("FYPay:ERROR:{}", e.getMessage(), e);
        }
        return true;

    }

    @Override
    public CBPayCreatePaymentResponse cbPayRequest(String price, String orderId) {
        String merchantId = systemConfigService.getValueByKey("cbMerchantId");
        String key = systemConfigService.getValueByKey("cbKey");
        String callBackUrl = systemConfigService.getValueByKey("cbCallBackUrl");
        String url = systemConfigService.getValueByKey("cbUrl");
        String type = systemConfigService.getValueByKey("cbType");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(orderId + "&").append(price + "&").append(type + "&").append(merchantId + "&").append(key);

        String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();

        // 构建请求体
        RequestBody requestBody = new FormBody.Builder()
                .add("userCode", merchantId)
                .add("orderCode", orderId)
                .add("amount", price)
                .add("payType", type)
                .add("callbackUrl", callBackUrl)
                .add("sign", sign)
                .build();

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/system/api/pay")
                .post(requestBody)
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String string = response.body().string();
                if (StringUtils.isNotEmpty(string) && string.contains("code")) {
                    CBPayCreatePaymentResponse paymentResponse = JSON.parseObject(string, CBPayCreatePaymentResponse.class);
                    return paymentResponse;
                }
            }
        } catch (Exception e) {
            logger.error("CBPAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public CBPayCreatePaymentResponse kdPayRequest(String price, String orderId) {
        String merchantId = systemConfigService.getValueByKey("kdMerchantId");
        String key = systemConfigService.getValueByKey("kdKey");
        String callBackUrl = systemConfigService.getValueByKey("kdCallBackUrl");
        String url = systemConfigService.getValueByKey("kdUrl");
        String type = systemConfigService.getValueByKey("kdType");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(orderId + "&").append(price + "&").append(type + "&").append(merchantId + "&").append(key);

        String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();

        // 构建请求体
        RequestBody requestBody = new FormBody.Builder()
                .add("userCode", merchantId)
                .add("orderCode", orderId)
                .add("amount", price)
                .add("payType", type)
                .add("callbackUrl", callBackUrl)
                .add("sign", sign)
                .build();

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/system/api/pay")
                .post(requestBody)
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String string = response.body().string();
                if (StringUtils.isNotEmpty(string) && string.contains("code")) {
                    CBPayCreatePaymentResponse paymentResponse = JSON.parseObject(string, CBPayCreatePaymentResponse.class);
                    return paymentResponse;
                }
            }
        } catch (Exception e) {
            logger.error("KDPAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public AAPayCreatePaymentResponse aaPayRequest(String price, String orderId) {
        String merchantId = systemConfigService.getValueByKey("aaMerchantId");
        String key = systemConfigService.getValueByKey("aaKey");
        String callBackUrl = systemConfigService.getValueByKey("aaCallBackUrl");
        String url = systemConfigService.getValueByKey("aaUrl");
        String language = systemConfigService.getValueByKey("aaLanguage");
        String coinCode = systemConfigService.getValueByKey("aaCoinCode");
        String currencyCode = systemConfigService.getValueByKey("aaCurrencyCode");

        Map<String, Object> objectMap = new LinkedHashMap<>();
        objectMap.put("user_id", Long.parseLong(merchantId));
        objectMap.put("currency_money", price);
        objectMap.put("currency_code", currencyCode);
        objectMap.put("coin_code", coinCode);
        objectMap.put("asyn_notice_url", callBackUrl);
        objectMap.put("sync_jump_url", "");
        objectMap.put("user_order_id", orderId);
        objectMap.put("language", Integer.parseInt(language));
        objectMap.put("user_custom_id", "1");
        objectMap.put("remark", "");
        String jsonString = JSON.toJSONString(objectMap);

        String time = String.valueOf(System.currentTimeMillis() / 1000);
        String signStr = String.format("%s%s%s%s", time, merchantId, "POST/api/v1/order/create", jsonString);
        logger.info("AA支付，请求参数: signStr={}", signStr);
        String sign = CrmebUtil.generateHmacSHA256Signature2(signStr, key);
        sign = sign.replace('/', '_').replace('+', '-');
        OkHttpClient client = new OkHttpClient().newBuilder().build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(url + "/api/v1/order/create")
                .method("POST", body)
                .addHeader("Accept-Language", "zh-CN")
                .addHeader("X-ACCESS-SIGN", sign)
                .addHeader("X-ACCESS-TIMESTAMP", time)
                .addHeader("X-ACCESS-USERID", merchantId)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            logger.info("AA支付，响应状态码: {}, 响应内容: {}", response.code(), responseBody);
            if (!response.isSuccessful()) {
                logger.error("AA支付: 请求失败，状态码: {}, 响应内容: {}", response.code(), responseBody);
                return null;
            }
            if (responseBody == null) {
                logger.error("AA支付: 响应体为空");
                return null;
            }
            return JSON.parseObject(responseBody, AAPayCreatePaymentResponse.class);
        } catch (Exception e) {
            logger.error("AA支付，异常发生，请求参数: jsonString={}, sign={}, 异常信息: {}",
                    jsonString, sign, e.getMessage(), e);
            return null;
        }
    }

    private static String generateHmacSHA256Signature(String data, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte item : signData) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            byte[] bytesToEncode = sb.toString().getBytes(StandardCharsets.UTF_8);
            String encodedString2 = Base64.encodeBase64String(bytesToEncode);
            return encodedString2;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public FPayCreatePaymentResponse fPayAgentRequest(String price, String orderId, String address) {
        String fpApiKey = systemConfigService.getValueByKeyException("fpApiKey");
        String fpApi = systemConfigService.getValueByKeyException("fpApi");
        String fpMerchantId = systemConfigService.getValueByKeyException("fpMerchantId");
        String fpCallbackUrlAgent = systemConfigService.getValueByKeyException("fpCallbackUrlAgent");

        Map<String, Object> orderData = new HashMap<>();
        orderData.put("merchantid", fpMerchantId);
        orderData.put("orderid", orderId);
        orderData.put("amount", price);
        orderData.put("address", address);
        orderData.put("notify_url", fpCallbackUrlAgent);
        String string = CommonUtil.generateSignature(orderData) + fpApiKey;
        String sign = DigestUtils.md5Hex(string).toUpperCase();
        orderData.put("sign", sign);

        try {
            String response = new OkHttpUtils().post(fpApi + "/v1/withdrawal/add", orderData);
            if (StringUtils.isNotEmpty(response) && response.contains("code")) {
                FPayCreatePaymentResponse fPayCreatePaymentResponse = JSON.parseObject(response, FPayCreatePaymentResponse.class);
                return fPayCreatePaymentResponse;
            }

        } catch (Exception e) {
            logger.error("FPAY-Agent:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    public CBPayAgentPaymentResponse kdAgentPayRequest(String price, String orderId, String address) {
        String merchantId = systemConfigService.getValueByKey("kdMerchantId");
        String key = systemConfigService.getValueByKey("kdKey");
        String callBackUrl = systemConfigService.getValueByKey("kdAgentCallBackUrl");
        String url = systemConfigService.getValueByKey("kdUrl");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(orderId + "&").append(price + "&").append(address + "&").append(merchantId + "&").append(key);

        String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();

        // 构建请求体
        RequestBody requestBody = new FormBody.Builder()
                .add("userCode", merchantId)
                .add("orderCode", orderId)
                .add("amount", price)
                .add("address", address)
                .add("callbackUrl", callBackUrl)
                .add("sign", sign)
                .build();

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/system/api/remit")
                .post(requestBody)
                .build();

        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new CrmebException("请求异常：" + response);
            }
            if (response.body() == null) {
                log.error("kd代付，Response body is null");
                throw new CrmebException("请求异常：Response body is null");
            }
            responseBody = response.body().string();
            return JSON.parseObject(responseBody, CBPayAgentPaymentResponse.class);
        } catch (Exception e) {
            logger.error("kd代付，json解析異常，responseBody={}，异常讯息={}", responseBody, e.getMessage(), e);
            throw new CrmebException("kd代付，json解析異常，responseBody=" + responseBody + "，异常讯息=" + e.getMessage());
        }
    }

    @Override
    public CBPayAgentPaymentResponse cbAgentPayRequest(String price, String orderId, String address) {
        String merchantId = systemConfigService.getValueByKey("cbMerchantId");
        String key = systemConfigService.getValueByKey("cbKey");
        String callBackUrl = systemConfigService.getValueByKey("cbAgentCallBackUrl");
        String url = systemConfigService.getValueByKey("cbUrl");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(orderId + "&").append(price + "&").append(address + "&").append(merchantId + "&").append(key);

        String sign = DigestUtils.md5Hex(stringBuilder.toString()).toUpperCase();

        // 构建请求体
        RequestBody requestBody = new FormBody.Builder()
                .add("userCode", merchantId)
                .add("orderCode", orderId)
                .add("amount", price)
                .add("address", address)
                .add("callbackUrl", callBackUrl)
                .add("sign", sign)
                .build();

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/system/api/remit")
                .post(requestBody)
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String string = response.body().string();
                if (StringUtils.isNotEmpty(string) && string.contains("code")) {
                    CBPayAgentPaymentResponse paymentResponse = JSON.parseObject(string, CBPayAgentPaymentResponse.class);
                    return paymentResponse;
                }
            }
        } catch (Exception e) {
            logger.error("CBPAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public BQPayAgentPaymentResponse bqAgentPayRequest(UserExtract userExtract) {
        String merchantId = systemConfigService.getValueByKey("bqMerchantId");
        String key = systemConfigService.getValueByKey("bqKey");
        String callBackUrl = systemConfigService.getValueByKey("bqCallBackUrl");
        String url = systemConfigService.getValueByKey("bqUrl");

        Map<String, Object> orderData = new LinkedHashMap<>();
        orderData.put("mchid", merchantId);
        orderData.put("out_trade_no", userExtract.getOrderId());
        orderData.put("money", userExtract.getExtractPrice().toString());
        orderData.put("notifyurl", callBackUrl);
        orderData.put("bankname", userExtract.getBankName());
        orderData.put("subbranch", userExtract.getBankName());
        orderData.put("accountname", userExtract.getRealName());
        orderData.put("cardnumber", userExtract.getBankCode());

        String stringJoin = CommonUtil.generateSignature(orderData) + "&key=" + key;
        String sign = DigestUtils.md5Hex(stringJoin.toString()).toUpperCase();
        orderData.put("sign", sign);
        if (userExtract.getExtractType().equals("bank")) {
            orderData.put("cardtype", "0");
        } else if (userExtract.getExtractType().equals("usdt")) {
            if (userExtract.getBankName().contains("TRC20")) {
                orderData.put("cardtype", "3");
            } else if (userExtract.getBankName().contains("ERC20")) {
                orderData.put("cardtype", "4");
            }
        } else if (userExtract.getExtractType().equals("alipay")) {
            orderData.put("cardtype", "6");
        } else {
            return null;
        }
        // 构建请求体
        FormBody.Builder builder = new FormBody.Builder();
        orderData.forEach((k, v) -> {
            builder.add(k, v.toString());
        });

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/v1/dfapi/add")
                .post(builder.build())
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String string = response.body().string();
                if (StringUtils.isNotEmpty(string) && string.contains("status")) {
                    BQPayAgentPaymentResponse paymentResponse = JSON.parseObject(string, BQPayAgentPaymentResponse.class);
                    return paymentResponse;
                }
            }
        } catch (Exception e) {
            logger.error("BQPAY:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public DMAgentPaymentResponse dmAgentPayRequest(UserExtract userExtract) {
        String merchantId = systemConfigService.getValueByKey("dmMerchantId");
        String key = systemConfigService.getValueByKey("dmKey");
        String callBackUrl = systemConfigService.getValueByKey("dmCallBackUrl");
        String url = systemConfigService.getValueByKey("dmUrl");

        String formattedPrice = String.format("%.2f", userExtract.getExtractPrice());


        Map<String, Object> orderData = new LinkedHashMap<>();
        orderData.put("appId", merchantId);
        orderData.put("appOrderNo", userExtract.getOrderId());
        orderData.put("orderAmt", formattedPrice);
        orderData.put("payId", "401");
        orderData.put("accNo", userExtract.getBankCode());
        orderData.put("accName", userExtract.getRealName());
        orderData.put("bankName", userExtract.getBankName());
        String stringJoin = CommonUtil.generateSignature(orderData) + "&key=" + key;
        String sign = DigestUtils.md5Hex(stringJoin.toString()).toUpperCase();
        orderData.put("notifyURL", callBackUrl);
        orderData.put("sign", sign);
        // 构建请求体
        FormBody.Builder builder = new FormBody.Builder();
        orderData.forEach((k, v) -> {
            builder.add(k, v.toString());
        });

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/newbankPay/crtAgencyOrder.do")
                .post(builder.build())
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String string = response.body().string();
                DMAgentPaymentResponse paymentResponse = JSON.parseObject(string, DMAgentPaymentResponse.class);
                return paymentResponse;
            }
        } catch (Exception e) {
            logger.error("钻石代付:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public BDTPayAgentPaymentResponse bdtAgentPayRequest(UserExtract userExtract) {
        String merchantId = systemConfigService.getValueByKey("bdtMerchantId");
        String key = systemConfigService.getValueByKey("bdtKey");
        String callBackUrl = systemConfigService.getValueByKey("bdtCallBackUrl");
        String url = systemConfigService.getValueByKey("bdtUrl");

        Map<String, Object> orderData = new LinkedHashMap<>();
        orderData.put("merchantId", merchantId);
        orderData.put("merchantUniqueOrderId", userExtract.getOrderId());
        orderData.put("currency", "CNY");
        orderData.put("amount", userExtract.getExtractPrice().toString());
        //orderData.put("note", "ww");
        orderData.put("bankCardNumber", userExtract.getBankCode());
        orderData.put("bankName", userExtract.getBankName());
        orderData.put("bankRealName", userExtract.getRealName());
        orderData.put("notifyUrl", callBackUrl);
        String stringJoin = CommonUtil.generateSignature(orderData) + key;
        String sign = DigestUtils.md5Hex(stringJoin.toString()).toLowerCase();
        orderData.put("sign", sign);
        // 构建请求体
        FormBody.Builder builder = new FormBody.Builder();
        orderData.forEach((k, v) -> {
            builder.add(k, v.toString());
        });

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象
        Request request = new Request.Builder()
                .url(url + "/api/merchant/createWithdrawOrder")
                .post(builder.build())
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String string = response.body().string();
                BDTPayAgentPaymentResponse paymentResponse = JSON.parseObject(string, BDTPayAgentPaymentResponse.class);
                return paymentResponse;
            }
        } catch (Exception e) {
            logger.error("八达通代付:ERROR:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public AAPayWithdrawalPaymentResponse aaAgentPayRequest(String price, String orderId, String address) {
        String merchantId = systemConfigService.getValueByKey("aaMerchantId");
        String key = systemConfigService.getValueByKey("aaKey");
        String aaAgentCallBackUrl = systemConfigService.getValueByKey("aaAgentCallBackUrl");
        String url = systemConfigService.getValueByKey("aaUrl");
        String language = systemConfigService.getValueByKey("aaLanguage");
        String aaCurrencyCode = systemConfigService.getValueByKey("aaCurrencyCode");
        String currencyCode = systemConfigService.getValueByKey("aaAgentCurrencyCode");

        Map<String, Object> objectMap = new LinkedHashMap<>();
        objectMap.put("user_id", Long.parseLong(merchantId));
        objectMap.put("user_withdrawal_id", orderId);
        objectMap.put("user_custom_id", "1");
        objectMap.put("withdrawal_address", address);
        objectMap.put("currency_code", aaCurrencyCode);
        objectMap.put("coin_code", currencyCode);
        objectMap.put("currency_amount", price);
        objectMap.put("asyn_notice_url", aaAgentCallBackUrl);
        objectMap.put("remark", "");
        String jsonString = JSON.toJSONString(objectMap);
        String time = String.valueOf(System.currentTimeMillis() / 1000);
        String signStr = String.format("%s%s%s%s", time, merchantId, "POST/api/v1/withdrawal/create", jsonString);
        logger.info("AA代付，请求参数: signStr={}", signStr);
        String sign = CrmebUtil.generateHmacSHA256Signature2(signStr, key);
        sign = sign.replace('/', '_').replace('+', '-');
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(url + "/api/v1/withdrawal/create")
                .method("POST", body)
                .addHeader("Accept-Language", "zh-CN")
                .addHeader("X-ACCESS-SIGN", sign)
                .addHeader("X-ACCESS-TIMESTAMP", time)
                .addHeader("X-ACCESS-USERID", merchantId)
                .build();
        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            responseBody = response.body() != null ? response.body().string() : null;
            logger.info("AA代付，响应状态码: {}, 响应内容: {}", response.code(), responseBody);
            if (!response.isSuccessful()) {
                logger.error("AA代付，请求异常，响应详情: {}", JSON.toJSONString(response));
                throw new CrmebException("请求异常：response=" + JSON.toJSONString(response) + ", body=" + responseBody);
            }
            if (responseBody == null) {
                logger.error("AA代付，请求异常，响应详情: {}", JSON.toJSONString(response));
                throw new CrmebException("请求异常：Response body is null");
            }
            return JSON.parseObject(responseBody, AAPayWithdrawalPaymentResponse.class);
        } catch (Exception e) {
            logger.error("AA代付，发生异常，请求响应内容: {}, 异常信息: {}", responseBody, e.getMessage(), e);
            throw new CrmebException("发生异常：" + e.getMessage());
        }
    }

    @Override
    public XYPayAgentPaymentResponse xyAgentPayRequest(UserExtract userExtract) {
        String merchantId = systemConfigService.getValueByKey("xyMerchantId");
        String key = systemConfigService.getValueByKey("xyKey");
        String callBackUrl = systemConfigService.getValueByKey("xyAgentCallBackUrl");
        String url = systemConfigService.getValueByKey("xyUrl");

        /*if (userExtract.getExtractPrice().stripTrailingZeros().scale() > 0){
            throw new RuntimeException("新鹰代付支付仅支持整数金额");
        }
        Integer amount =  userExtract.getExtractPrice().intValue();*/
        Map<String, Object> orderData = new LinkedHashMap<>();
        orderData.put("mchid", merchantId);
        orderData.put("out_trade_no", userExtract.getOrderId());
        orderData.put("money", userExtract.getExtractPrice().toString());
        orderData.put("accountname", userExtract.getRealName());
        orderData.put("cardnumber", userExtract.getBankCode());
        orderData.put("bankname", userExtract.getBankName());
        orderData.put("subbranch", userExtract.getBankName());
        orderData.put("notifyurl", callBackUrl);
        String stringJoin = CommonUtil.generateSignature(orderData);
        String signStr = stringJoin + "&key=" + key;
        String sign = DigestUtils.md5Hex(signStr).toUpperCase();
        orderData.put("pay_code", "bank");
        orderData.put("sign", sign);

        // 构建表单参数
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        for (Map.Entry<String, Object> entry : orderData.entrySet()) {
            formBodyBuilder.add(entry.getKey(), entry.getValue().toString());
        }

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        RequestBody body = formBodyBuilder.build();  // 表单形式
        Request request = new Request.Builder()
                .url(url + "/xyapi.html")
                .post(body)  // 使用POST方法
                .addHeader("Content-Type", "application/x-www-form-urlencoded")  // 固定值
                .addHeader("Six-Api", "SixApi")  // 固定值
                .addHeader("Six-Mchid", merchantId)  // 商户号
                .addHeader("Six-Date", DateUtil.dateToStr(new Date(), "yyyy.MM.dd"))  // 请求时间
                .build();

        try (Response response = client.newCall(request).execute()) {
            String string = response.body().string();
            XYPayAgentPaymentResponse paymentResponse = JSON.parseObject(string, XYPayAgentPaymentResponse.class);
            return paymentResponse;
        } catch (Exception e) {
            logger.error("XYPAY:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public ToPayCreatePaymentResponse toPayRequest(String price, String orderId) {
        String merchantId = systemConfigService.getValueByKey("toMerchantId");
        String key = systemConfigService.getValueByKey("toKey");
        String callBackUrl = systemConfigService.getValueByKey("toCallBackUrl");
        String url = systemConfigService.getValueByKey("toUrl");

        BigDecimal amount = new BigDecimal(price).setScale(2, RoundingMode.HALF_UP);

        Map<String, Object> params = new HashMap<>();
        params.put("recvid", merchantId);
        params.put("orderid", orderId);
        params.put("amount", amount.toPlainString());
        params.put("notifyurl", callBackUrl);

        String sign = DigestUtil.md5Hex(
                String.format("%s%s%s%s",
                        merchantId,
                        orderId,
                        amount.toPlainString(),
                        key)
        );
        params.put("sign", sign);

        String jsonString = JSON.toJSONString(params);
        logger.info("TOPAY: 请求参数={}", jsonString);
        // 构建请求体
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(url + "/createpay")
                .method("POST", body).build();

        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                logger.error("TOPAY: Request failed with status code: {}", response.code());
                return null;
            }
            if (response.body() == null) {
                logger.error("TOPAY: Response body is null");
                return null;
            }
            responseBody = response.body().string();
            ToPayCreatePaymentResponse responseObj = JSON.parseObject(responseBody, ToPayCreatePaymentResponse.class);
            ToPayCreatePaymentResponse.DataClass dataClass = JSON.parseObject(
                    responseObj.getData(),
                    ToPayCreatePaymentResponse.DataClass.class
            );
            responseObj.setDataClass(dataClass);
            return responseObj;
        } catch (Exception e) {
            logger.error("TOPAY，json解析異常，responseBody={}，异常讯息={}", responseBody, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public OkPayCreatePaymentResponse okPayRequest(String price, String orderId) {
        String merchantId = systemConfigService.getValueByKey("okMerchantId");
        String key = systemConfigService.getValueByKey("okKey");
        String callBackUrl = systemConfigService.getValueByKey("okCallBackUrl");
        String url = systemConfigService.getValueByKey("okUrl");

        BigDecimal amount = new BigDecimal(price).setScale(2, RoundingMode.HALF_UP);

        Map<String, Object> params = new HashMap<>();
        params.put("recvid", merchantId);
        params.put("orderid", orderId);
        params.put("amount", amount.toPlainString());
        params.put("notifyurl", callBackUrl);

        String sign = DigestUtil.md5Hex(
                String.format("%s%s%s%s",
                        merchantId,
                        orderId,
                        amount.toPlainString(),
                        key)
        );
        params.put("sign", sign);

        String jsonString = JSON.toJSONString(params);
        logger.info("OKPAY: 请求参数={}", jsonString);
        // 构建请求体
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(url + "/createpay")
                .method("POST", body).build();

        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                logger.error("OKPAY: Request failed with status code: {}", response.code());
                return null;
            }
            if (response.body() == null) {
                logger.error("OKPAY: Response body is null");
                return null;
            }
            responseBody = response.body().string();
            OkPayCreatePaymentResponse responseObj = JSON.parseObject(responseBody, OkPayCreatePaymentResponse.class);
            OkPayCreatePaymentResponse.DataClass dataClass = JSON.parseObject(
                    responseObj.getData(),
                    OkPayCreatePaymentResponse.DataClass.class
            );
            responseObj.setDataClass(dataClass);
            return responseObj;
        } catch (Exception e) {
            logger.error("OKPAY，json解析異常，responseBody={}，异常讯息={}", responseBody, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public ToAgentResponse toAgentRequest(UserExtract userExtract, String orderId) {
        String merchantId = systemConfigService.getValueByKey("toMerchantId");
        String key = systemConfigService.getValueByKey("toKey");
        String callBackUrl = systemConfigService.getValueByKey("toAgentCallBackUrl");
        String url = systemConfigService.getValueByKey("toUrl");
        String price = userExtract.getExtractPrice().toPlainString();
        BigDecimal formattedPrice = new BigDecimal(price).setScale(2, RoundingMode.HALF_UP);

        Map<String, Object> params = new HashMap<>();
        params.put("sendid", merchantId);
        params.put("orderid", orderId);
        params.put("amount", formattedPrice.toPlainString());
        params.put("address", userExtract.getBankCode());
        params.put("notifyurl", callBackUrl);

        String sign = DigestUtil.md5Hex(
                String.format("%s%s%s%s",
                        merchantId,
                        orderId,
                        formattedPrice.toPlainString(),
                        key)
        );
        params.put("sign", sign);

        String jsonString = JSON.toJSONString(params);
        logger.info("to代付，请求参数={}", jsonString);
        // 构建请求体
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(url + "/createwd")
                .method("POST", body).build();

        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new CrmebException("请求异常：" + response);
            }
            if (response.body() == null) {
                log.error("to代付，jsonString={}，Response body is null", jsonString);
                return null;
            }
            responseBody = response.body().string();
            ToAgentResponse responseObj = JSON.parseObject(responseBody, ToAgentResponse.class);
            ToAgentResponse.DataClass dataClass = JSON.parseObject(
                    responseObj.getData(),
                    ToAgentResponse.DataClass.class
            );
            responseObj.setDataClass(dataClass);
            return responseObj;
        } catch (Exception e) {
            logger.error("to代付，json解析異常，responseBody={}，异常讯息={}", responseBody, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public OkAgentResponse okAgentRequest(UserExtract userExtract, String orderId) {
        String merchantId = systemConfigService.getValueByKey("okMerchantId");
        String key = systemConfigService.getValueByKey("okKey");
        String callBackUrl = systemConfigService.getValueByKey("okAgentCallBackUrl");
        String url = systemConfigService.getValueByKey("okUrl");
        String price = userExtract.getExtractPrice().toPlainString();
        BigDecimal formattedPrice = new BigDecimal(price).setScale(2, RoundingMode.HALF_UP);

        Map<String, Object> params = new HashMap<>();
        params.put("sendid", merchantId);
        params.put("orderid", orderId);
        params.put("amount", formattedPrice.toPlainString());
        params.put("address", userExtract.getBankCode());
        params.put("notifyurl", callBackUrl);

        String sign = DigestUtil.md5Hex(
                String.format("%s%s%s%s",
                        merchantId,
                        orderId,
                        formattedPrice.toPlainString(),
                        key)
        );
        params.put("sign", sign);

        String jsonString = JSON.toJSONString(params);
        logger.info("ok代付，请求参数={}", jsonString);
        // 构建请求体
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(url + "/createwd")
                .method("POST", body).build();

        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new CrmebException("请求异常：" + response);
            }
            if (response.body() == null) {
                log.error("ok代付，jsonString={}，Response body is null", jsonString);
                return null;
            }
            responseBody = response.body().string();
            OkAgentResponse responseObj = JSON.parseObject(responseBody, OkAgentResponse.class);
            OkAgentResponse.DataClass dataClass = JSON.parseObject(
                    responseObj.getData(),
                    OkAgentResponse.DataClass.class
            );
            responseObj.setDataClass(dataClass);
            return responseObj;
        } catch (Exception e) {
            logger.error("ok代付，json解析異常，responseBody={}，异常讯息={}", responseBody, e.getMessage(), e);
            return null;
        }
    }
    private final ExecutorService executorService = new ThreadPoolExecutor(
            10,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Override
    public String checkOrder(String apiPath, String type) throws Exception {
        long startTime = System.currentTimeMillis();
        int pageSize = 100; // 每页 100 条
        int page = 0;

        try {
            if (type.equals("recharge")) {
                while (true) {
                    List<UserRecharge> userRechargeList = userRechargeService.findPendingSuccessOrder(page, pageSize);
                    if (userRechargeList.isEmpty()) {
                        break;
                    }
                    log.info("MPAY:CHECK_ORDER:recharge:第{}页, 订单数量:{}", page, userRechargeList.size());
                    userRechargeList.forEach(userRecharge -> {
                        executorService.submit(() -> {
                            try {
                                JSONObject mpayData = this.processMpayCheck(apiPath, userRecharge.getOrderId());
                                if (!CollectionUtils.isEmpty(mpayData) && "finish".equals(mpayData.getString("status"))) {
                                    this.processRechargeOrder(userRecharge.getOrderId());
                                }
                            } catch (Exception e) {
                                log.error("MPAY:ORDER:{}:ERROR:{}", userRecharge.getOrderId(), e.getMessage(), e);
                            }
                        });
                    });
                    page++;
                }
            } else if (type.equals("withdraw")) {
                while (true) {
                    List<UserExtract> userExtractList = userExtractService.findPendingOrder(page, pageSize);
                    if (userExtractList.isEmpty()) {
                        break;
                    }
                    log.info("MPAY:CHECK_ORDER:withdraw:第{}页, 订单数量:{}", page, userExtractList.size());
                    userExtractList.forEach(userExtract -> {
                        executorService.submit(() -> {
                            try {
                                JSONObject mpayData = this.processMpayCheck(apiPath, userExtract.getOrderId());
                                if (!CollectionUtils.isEmpty(mpayData) && "finish".equals(mpayData.getString("status"))) {
                                    this.processWithdrawOrder(userExtract.getOrderId());
                                }
                            } catch (Exception e) {
                                log.error("MPAY:ORDER:{}:ERROR:{}", userExtract.getOrderId(), e.getMessage(), e);
                            }
                        });
                    });
                    page++;
                }
            } else {
                log.error("checkOrder type 异常 :{}", type);
            }
            return "success";
        } finally {
            log.info("MPAY:CHECK_ORDER:{}:执行耗时:{} ms", type, System.currentTimeMillis() - startTime);
            ThreadPoolExecutor executor = (ThreadPoolExecutor) executorService;
            log.info("ThreadPool: ActiveThreads={}, QueueSize={}", executor.getActiveCount(), executor.getQueue().size());
        }
    }

    private JSONObject processMpayCheck(String apiPath, String merchOrderId) {
        try {
            String mpApi = systemConfigService.getValueByKeyException("mpApi");
            String mpMerchantId = systemConfigService.getValueByKeyException("mpMerchantId");
            String mpMd5Key = systemConfigService.getValueByKeyException("mpMd5Key");
            String mpPublicKey = systemConfigService.getValueByKeyException("mpPublicKey");

            JSONObject params = new JSONObject();
            params.put("merchOrderId", merchOrderId);
            String paramsJson = params.toString();

            String encryptedParams = RSAEncryptionUtil.encryptAndBase64Encode(paramsJson, mpPublicKey);

            long t = Instant.now().getEpochSecond();
            String signature = DigestUtils.md5Hex(mpMerchantId + encryptedParams + t + mpMd5Key);

            String param = "?merchId=" + mpMerchantId + "&body=" + encryptedParams + "&t=" + t + "&key=" + signature;
            String url = mpApi + apiPath + param;
            String response = new OkHttpUtils().get(url);
            if (StringUtils.isNotEmpty(response) && response.contains("code")) {
                JSONObject jsonResponse = JSONObject.parseObject(response);
                Integer code = jsonResponse.getInteger("code");
                if (code == 0) {
                    return jsonResponse.getJSONObject("data");
                } else {
                    String message = jsonResponse.getString("message");
                    log.warn("MPAY:ORDER:{}:ERROR:{}", merchOrderId, message);
                    return null;
                }
            } else {
                log.warn("MPAY:ORDER:{}:INVALID_RESPONSE:{}", merchOrderId, response);
                return null;
            }
        } catch (Exception e) {
            log.error("MPAY:ORDER:{}:ERROR:{}", merchOrderId, e.getMessage(), e);
            return null;
        }
    }

    private void processRechargeOrder(String orderNo) {
        if (!orderNo.contains(Constants.SERVICE_PAY_TYPE_RECHARGE)) {
            return;
        }
        UserRecharge userRecharge = userRechargeService.getInfoByOrderId(orderNo);
        if (userRecharge == null || userRecharge.getPaid() == 1) {
            return;
        }
        Boolean rechargePayAfter = rechargePayService.paySuccess(userRecharge);
        if (!rechargePayAfter) {
            log.error("mpay pay error : 数据保存失败==》{}", orderNo);
            return;
        }
        rechargePayService.paySuccessSendBonus(userRecharge);
    }

    private void processWithdrawOrder(String orderId) {
        UserExtract userExtract = userExtractService.getByOrderId(orderId);
        if (userExtract == null) {
            log.error("processWithdrawOrder 无效的订单 userExtract={}", JSONObject.toJSONString(userExtract));
            return;
        }
        if (userExtract.getStatus() != 2) {
            log.error("processWithdrawOrder 订单状态不正确==》userExtract={}", JSONObject.toJSONString(userExtract));
            return;
        }
        Boolean result = false;
        if (userExtract.getType().equals(1)) {
            result = userExtractService.updateStatus(userExtract, 3, "");
        } else if (userExtract.getType().equals(2)) {
            result = userExtractService.updateStatusAccountBalance(userExtract, 3, "");
        } else if (userExtract.getType().equals(3)) {
            result = userExtractService.updateStatusMerchantBalance(userExtract, 3, "");
        }
    }

    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
    }
}
