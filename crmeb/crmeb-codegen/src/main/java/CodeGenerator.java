import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.io.FileOutputStream;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成工具，一键生成后端业务代码
 */
public class CodeGenerator {
    //数据库链接
    private final static String url = "********************************************************************************************************************";
    private final static String username = "root";//数据库用户名
    private final static String pwd = "0lmgKY5vv4uoHPIa";//数据库密码
    private final static String tableName = "eb_invest_items_config";  //生成的表
    private final static Boolean redisEnabled = true;//是否启用redis

    public static void main(String[] args) {
        generator();
    }

    public static void generator() {
        String currentDirectory = System.getProperty("user.dir") + "\\crmeb";
        //自定义文件输出路径
        Map<OutputFile, String> outputFileStringMap = Map.of(
                OutputFile.controller, currentDirectory + "\\crmeb-admin\\src\\main\\java\\com\\zbkj\\admin\\controller",
                OutputFile.service, currentDirectory + "\\crmeb-service\\src\\main\\java\\com\\zbkj\\service\\service",
                OutputFile.serviceImpl, currentDirectory + "\\crmeb-service\\src\\main\\java\\com\\zbkj\\service\\service\\impl",
                OutputFile.mapper, currentDirectory + "\\crmeb-service\\src\\main\\java\\com\\zbkj\\service\\dao",
                OutputFile.xml, currentDirectory + "\\crmeb-service\\src\\main\\resources\\mapper",
                OutputFile.entity, currentDirectory + "\\crmeb-common\\src\\main\\java\\com\\zbkj\\common\\model"
        );

        FastAutoGenerator.create(url, username, pwd)
                .globalConfig(builder -> {
                    builder.author("Gavin") // 设置作者
                            .fileOverride() // 覆盖已生成文件
                            .dateType(DateType.ONLY_DATE)
                            .outputDir(currentDirectory) // 指定输出目录
                            .disableOpenDir(); //禁止打开输出目录，默认打开
                })
                .dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                    int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                    if (typeCode == Types.SMALLINT) {
                        // 自定义类型转换
                        return DbColumnType.INTEGER;
                    }
                    return typeRegistry.getColumnType(metaInfo);

                }))

                .packageConfig(builder -> {
                    builder.pathInfo(outputFileStringMap);
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude(tableName) // 设置需要生成的表名
                            .addTablePrefix("eb_") // 设置过滤表前缀
                            // 策略配置
                            .serviceBuilder().formatServiceFileName("%sService").enableFileOverride()
                            .mapperBuilder().formatMapperFileName("%sDao").enableFileOverride()
                            .controllerBuilder().enableFileOverride(); // 覆盖已生成文件

                })
                .injectionConfig(builder -> {
                    builder.beforeOutputFile(((tableInfo, stringObjectMap) -> {
                                stringObjectMap.put("redisEnabled",redisEnabled);
                            }))
                            //自定义搜索对象文件
                            .customFile(new CustomFile.Builder().fileName("SearchRequest.java")
                                    .templatePath("/templates/java/EntitySearch.java.vm")
                                    .filePath(currentDirectory + "\\crmeb-common\\src\\main\\java\\com\\zbkj\\common\\request")
                                    .build()
                            );
                })
                //模板引擎
                .templateEngine(new VelocityTemplateEngine())
                //设置模板
                .templateConfig(builder ->
                        builder
                                .controller("/templates/java/Controller.java.vm")
                                .service("/templates/java/Service.java.vm")
                                .serviceImpl("/templates/java/ServiceImpl.java.vm")
                                .mapper("/templates/java/Dao.java.vm")
                                .xml("/templates/xml/Dao.xml.vm")
                                .entity("/templates/java/Entity.java.vm"))
                .execute();
    }

}
