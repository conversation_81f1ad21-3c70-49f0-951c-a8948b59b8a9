package com.zbkj.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.page.CommonPage;
import com.zbkj.service.dao.${entity}Dao;
import com.zbkj.common.model.${entity};
import com.zbkj.common.request.${entity}SearchRequest;
import com.zbkj.service.service.${entity}Service;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * ${table.comment} 接口实现类
 */

@Service
public class ${entity}ServiceImpl extends ServiceImpl<${entity}Dao, ${entity}> implements ${entity}Service {

    /**
     * ${entity}列表查询
     * @param request 默认生成搜索的对象 根据自己需求修改或者创建自己的request
     * @return
     */
    @Override
    public PageInfo<${entity}> getList(${entity}SearchRequest request) {
        Page<Object> startPage = PageHelper.startPage(request.getPage(), request.getLimit());
        //列表查询 ${entity} 类的多条件查询
        LambdaQueryWrapper<${entity}> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<${entity}> list =  baseMapper.selectList(lambdaQueryWrapper);
        return CommonPage.copyPageInfo(startPage,list);
    }

}
