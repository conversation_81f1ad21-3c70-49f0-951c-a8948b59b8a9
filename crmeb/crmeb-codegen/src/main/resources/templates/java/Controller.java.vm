package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.model.${entity};
import com.zbkj.service.service.${entity}Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import com.zbkj.common.request.${entity}SearchRequest;

/**
 * ${table.comment} 控制器
 */
#set($entityName = ${entity.substring(0,1).toLowerCase()}+${entity.substring(1)})
@Slf4j
@RestController
@Api(tags = "${table.comment}")
@RequestMapping("api/admin/${entityName}")
public class ${entity}Controller {
    @Autowired
    private ${table.serviceName} ${entityName}Service;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:${entityName}:list')")
    public CommonResult<CommonPage<${entity}>> list(${entity}SearchRequest request){
        CommonPage<${entity}> page = CommonPage.restPage(${entityName}Service.getList(request));
        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @GetMapping("/info")
    @ApiOperation(value = "详情数据")
    @PreAuthorize("hasAuthority('admin:${entityName}:info')")
    public CommonResult<${entity}> info(@RequestParam(value = "id") Integer id){
		${entity} ${entityName} = ${entityName}Service.getById(id);
        return CommonResult.success(${entityName});
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:${entityName}:save')")
    public CommonResult<String> save(@RequestBody @Validated ${entity} ${entityName}){
        if (${entityName}Service.save(${entityName})) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:${entityName}:update')")
    public CommonResult<String> update(@RequestBody @Validated ${entity} ${entityName}){
        if (${entityName}Service.updateById(${entityName})) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:${entityName}:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (${entityName}Service.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
