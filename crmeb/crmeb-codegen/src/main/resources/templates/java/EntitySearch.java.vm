package com.zbkj.common.request;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

	#if(${hasBigDecimal})
	import java.math.BigDecimal;
	#end
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * ${table.comment}
 */
@Data
@ApiModel(value="${entity}SearchRequest对象", description="${table.comment}搜索对象")
public class ${entity}SearchRequest extends PageParamRequest{

	#foreach ($field in ${table.fields})
	@ApiModelProperty(value = "$field.comment")
	private $field.propertyType $field.propertyName;
	#end

}
