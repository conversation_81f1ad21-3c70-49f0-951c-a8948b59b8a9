package com.zbkj.service.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.${entity};
import com.zbkj.common.request.${entity}SearchRequest;

/**
 * ${table.comment} 业务接口
 */
public interface ${entity}Service extends IService<${entity}> {
    /**
     * ${entity} 列表查询
     * @param request 查询条件对象
     * @return
     */
    PageInfo<${entity}> getList(${entity}SearchRequest request);
}

