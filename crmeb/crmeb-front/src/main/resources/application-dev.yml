# CRMEB 相关配置
crmeb:
  version: CRMEB-JAVA-KY-v1.3.4 # 当前代码版本
  domain: #配合swagger使用 # 待部署域名
  wechat-api-url:  #请求微信接口中专服务器
  wechat-js-api-debug: false #微信js api系列是否开启调试模式
  wechat-js-api-beta: true #微信js api是否是beta版本
  asyncConfig: true #是否同步config表数据到redis
  asyncWeChatProgramTempList: false #是否同步小程序公共模板库
  imagePath: crmebimage/ # 服务器图片路径配置 斜杠结尾
  im-sdk-appid: 20007373
  im-sdk-secret: 77bdf69f21e3d21c066d0e97f2f229d9a62a347eedf469b17a5b9e96bc9e4062
  im-sdk-api: https://adminapisgp.im.qcloud.com/v4/%s?sdkappid=%s&identifier=administrator&usersig=%s&random=%d&contenttype=json
  live-push-stream: rtmp://devpush-baixing.dtc2ctest.com/live/%d?txSecret=%s&txTime=%s
  live-push-stream-key: 819f2878e2deb55eb4a727113e8affbe
  live-pull-stream: '%s://devpull-baixing.dtc2ctest.com/live/%d%s?txSecret=%s&txTime=%s'
  live-pull-stream-key: yDF5a5DacNfRDipBf6Wz
server:
  port: 9083
  servlet:
    context-path: /

spring:
  profiles:
    #  配置的环境
    active: dev
    #  数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_IP:**************:4000}/crmeb_xbl?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimeZone=GMT+8&autoReconnect=true
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PWD:0lmgKY5vv4uoHPIa}

    #druid配置
    max-active: 20
    max-pool-prepared-statement-per-connection-size: 20
    filters: stat,wall,slf4j,mergeStat
    use-global-data-source-stat: true
    connect-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    druid:
      loginUsername: admin999
      loginPassword: 3qrfuP1s1YTWFjPURiYD


  aop:
    auto: true

  redis:
    host: "k8s-testc2cs-testc2cr-9579a06df1-0d37c840749cd7d6.elb.ap-northeast-3.amazonaws.com" #地址
    port: 6379 #端口
    password: rlfg04eIFR2F80T7
    timeout: 10000 # 连接超时时间（毫秒）
    database: 7 #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
application-prod:
  yml:
