package com.zbkj.front.filter;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.AccessEnum;
import com.zbkj.common.model.UserAccessList;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.front.utils.Ip2region;
import com.zbkj.front.utils.cz88;
import com.zbkj.service.service.SystemConfigService;
import com.zbkj.service.service.UserAccessListService;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 返回值输出过滤器
 */
public class IPFilter implements Filter {

    @Value("${spring.profiles.active}")
    private List<String> profiles;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserAccessListService userAccessListService;

    @Resource
    private UserService userService;

    @Resource
    private SystemConfigService systemConfigService;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response; // 强制转换为HttpServletResponse
        // 检查请求方法是否为OPTIONS
        if (httpServletRequest.getMethod().equalsIgnoreCase("OPTIONS")) {
            // 如果是OPTIONS请求，直接放过
            filterChain.doFilter(request, response);
            return;
        }
        if (httpServletRequest.getRequestURI().equals("/api/front/kefu")) {
            // 联系客服链接直接开放
            filterChain.doFilter(request, response);
            return;
        }

        //后台开关控制
        String valueByKey = systemConfigService.getValueByKey("frontUserIpLimitAreaSwitch");
        if (StringUtils.isBlank(valueByKey) || "0".equals(valueByKey)){
            filterChain.doFilter(request, response);
            return;
        }

        String clientIP = CrmebUtil.getClientIp(httpServletRequest);
        synchronized (clientIP) {
            //拒绝临时名单 无需重复校验
            String objectDeny = redisUtil.get(Constants.USER_DENY_IP_TEMP + clientIP);
            if (objectDeny != null) {
                denyMsg(httpServletResponse, clientIP, objectDeny);
                return;
            }
            //通过临时名单 无需重复校验
            String objectAllow = redisUtil.get(Constants.USER_ACCESS_IP_TEMP + clientIP);
            if (objectAllow != null) {
                filterChain.doFilter(request, response);
                return;
            }
            String region = cz88.getRegionOfCZ88(clientIP);
            if (shouldAllowAccess(clientIP,region)) {
                redisUtil.set(Constants.USER_ACCESS_IP_TEMP + clientIP, region,7L, TimeUnit.DAYS);
                filterChain.doFilter(request, response);
            } else {
                redisUtil.set(Constants.USER_DENY_IP_TEMP + clientIP, region,7L, TimeUnit.HOURS);
                denyMsg(httpServletResponse, clientIP, region);
            }
        }
    }

    /**
     * 拒绝输出打印
     *
     * @param httpServletResponse
     * @param clientIP
     * @param region
     */
    public static void denyMsg(HttpServletResponse httpServletResponse, String clientIP, String region) {
        try {
            httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
            httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            httpServletResponse.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
            httpServletResponse.setStatus(HttpServletResponse.SC_FORBIDDEN); // 设置403状态码
            httpServletResponse.setContentType("text/html;charset=UTF-8");
            httpServletResponse.getWriter().write(StrUtil.format("您的IP来自于：{}({})", clientIP, region));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * IP是否允许访问
     * @param clientIP
     * @param region
     * @return
     */
    private boolean shouldAllowAccess(String clientIP,String region) {
        Object ipObject = redisUtil.get(Constants.USER_ACCESS_IP_LIST + clientIP);
        Object regionObject = redisUtil.get(Constants.USER_ACCESS_REGION_LIST + region);
        if (ipObject == null && regionObject == null) {
            return true;
        }
        if (ipObject != null) {
            UserAccessList userAccessList = JSON.parseObject(ipObject.toString(), UserAccessList.class);
            if (userAccessList.getType() == AccessEnum.ALLOW) {
                return true;
            }else {
                return false;
            }
        }
        if (regionObject != null) {
            UserAccessList userAccessList = JSON.parseObject(regionObject.toString(), UserAccessList.class);
            if (userAccessList.getType() == AccessEnum.ALLOW) {
                return true;
            }else {
                return false;
            }
        }
        return false;
    }
}
