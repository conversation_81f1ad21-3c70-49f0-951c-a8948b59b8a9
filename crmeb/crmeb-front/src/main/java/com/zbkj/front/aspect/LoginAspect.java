package com.zbkj.front.aspect;
import com.zbkj.common.enums.LoginMethod;
import com.zbkj.service.service.UserLoginLogsService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
/**
 * @projectName: mall
 * @package: aspect
 * @className: LoginAspect
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/24 18:28
 * @version: 1.0
 */


@Aspect
@Component
public class LoginAspect {
    @Resource
    private UserLoginLogsService userLoginLogsService;

    @Pointcut("execution(* com.zbkj.service.service.LoginService.login(..))")
    public void pointCut(){

    }
    @Around("pointCut()")
    public Object aroundLogin(ProceedingJoinPoint joinPoint) throws Throwable {
        return userLoginLogsService.saveLoginLogs(joinPoint,LoginMethod.PASSWORD);
    }

}
