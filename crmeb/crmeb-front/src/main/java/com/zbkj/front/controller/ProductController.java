package com.zbkj.front.controller;


import cn.hutool.core.util.StrUtil;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.ProductLikeConfig;
import com.zbkj.common.model.ProductLikeRecord;
import com.zbkj.common.model.category.Category;
import com.zbkj.common.model.product.StoreProduct;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserBill;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.ProductListRequest;
import com.zbkj.common.request.ProductRequest;
import com.zbkj.common.response.*;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.CategoryTreeVo;
import com.zbkj.service.service.*;
import com.zbkj.service.service.impl.StoreBargainServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * 用户 -- 用户中心
 */
@Slf4j
@RestController("ProductController")
@RequestMapping("api/front")
@Api(tags = "商品")
public class ProductController {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductLikeConfigService productLikeConfigService;

    @Autowired
    private ProductLikeRecordService productLikeRecordService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private MerchantShopService merchantShopService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private RedisUtil redisUtil;

    private static final Logger logger = LoggerFactory.getLogger(StoreBargainServiceImpl.class);

    /**
     * 热门商品推荐
     */
    @ApiOperation(value = "热门商品推荐")
    @RequestMapping(value = "/product/hot", method = RequestMethod.GET)
    public CommonResult<CommonPage<IndexProductResponse>> getHotProductList(@Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(productService.getHotProductList(pageParamRequest));
    }

    /**
     * 优选商品推荐
     */
    @ApiOperation(value = "优选商品推荐")
    @RequestMapping(value = "/product/good", method = RequestMethod.GET)
    public CommonResult<CommonPage<IndexProductResponse>> getGoodProductList() {
        return CommonResult.success(productService.getGoodProductList());
    }

    /**
     * 获取分类
     */
    @ApiOperation(value = "获取分类")
    @RequestMapping(value = "/category", method = RequestMethod.GET)
    public CommonResult<List<CategoryTreeVo>> getCategory(@RequestParam(required = false, defaultValue = "0") Integer merchant) {
        if (merchant > 0) {
            MerchantShop merchantShop = merchantShopService.getByMerchant(merchant);
            if (merchantShop == null) {
                throw new CrmebException("店铺不存在!");
            }
            if (!merchantShop.getShopStatus()) {
                throw new CrmebException("店铺已关闭!");
            }
        }
        return CommonResult.success(productService.getCategory(merchant));
    }

    /**
     * 获取顶部分类
     */
    @ApiOperation(value = "获取顶部分类")
    @RequestMapping(value = "/category/top", method = RequestMethod.GET)
    public CommonResult<List<Category>> getCategoryByTop() {
        return CommonResult.success(productService.getCategoryByTop());
    }

    /**
     * 商品列表
     */
    @ApiOperation(value = "商品列表")
    @RequestMapping(value = "/products", method = RequestMethod.GET)
    public CommonResult<CommonPage<IndexProductResponse>> getList(@Validated ProductRequest request, @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(productService.getList(request, pageParamRequest));
    }

    /**
     * 商品详情
     */
    @ApiOperation(value = "商品详情")
    @RequestMapping(value = "/product/detail/{id}", method = RequestMethod.GET)
    @ApiImplicitParam(name = "type", value = "normal-正常，video-视频")
    public CommonResult<ProductDetailResponse> getDetail(@PathVariable Integer id, @RequestParam(value = "type", defaultValue = "normal") String type) {
        return CommonResult.success(productService.getDetail(id, type));
    }

    /**
     * 商品评论列表
     */
    @ApiOperation(value = "商品评论列表")
    @RequestMapping(value = "/reply/list/{id}", method = RequestMethod.GET)
    @ApiImplicitParam(name = "type", value = "评价等级|0=全部,1=好评,2=中评,3=差评", allowableValues = "range[0,1,2,3]")
    public CommonResult<CommonPage<ProductReplyResponse>> getReplyList(@PathVariable Integer id,
                                                                       @RequestParam(value = "type") Integer type, @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage(productService.getReplyList(id, type, pageParamRequest)));
    }

    /**
     * 商品评论数量
     */
    @ApiOperation(value = "商品评论数量")
    @RequestMapping(value = "/reply/config/{id}", method = RequestMethod.GET)
    public CommonResult<StoreProductReplayCountResponse> getReplyCount(@PathVariable Integer id) {
        return CommonResult.success(productService.getReplyCount(id));
    }

    /**
     * 商品详情评论
     */
    @ApiOperation(value = "商品详情评论")
    @RequestMapping(value = "/reply/product/{id}", method = RequestMethod.GET)
    public CommonResult<ProductDetailReplyResponse> getProductReply(@PathVariable Integer id) {
        return CommonResult.success(productService.getProductReply(id));
    }

    /**
     * 商品列表
     */
    @ApiOperation(value = "商品列表(个别分类模型使用)")
    @RequestMapping(value = "/product/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<IndexProductResponse>> getProductList(@Validated ProductListRequest request, @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(productService.getCategoryProductList(request, pageParamRequest));
    }

    /**
     * 商品规格详情
     */
    @ApiOperation(value = "商品规格详情")
    @RequestMapping(value = "/product/sku/detail/{id}", method = RequestMethod.GET)
    public CommonResult<ProductDetailResponse> getSkuDetail(@PathVariable Integer id) {
        return CommonResult.success(productService.getSkuDetail(id));
    }

    /**
     * 商品排行榜
     */
    @ApiOperation(value = "商品排行榜")
    @RequestMapping(value = "/product/leaderboard", method = RequestMethod.GET)
    public CommonResult<List<StoreProduct>> getLeaderboard() {
        return CommonResult.success(productService.getLeaderboard());
    }

    /**
     * 商品点赞
     */
    @ApiOperation(value = "商品点赞")
    @RequestMapping(value = "/product/like/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> like(@PathVariable Integer id) {
        User user = userService.getInfo();

        String lockKey = "user_lock_" + user.getUid(); // 使用统一的用户锁名，与UserLockService保持一致
        String requestId = UUID.randomUUID().toString(); // 生成唯一的请求标识
        // 尝试获取分布式锁
        boolean locked = redisUtil.tryLock(lockKey, requestId, 30); // 设置锁的过期时间为30秒
        if (!locked) {
            return CommonResult.failed("点赞失败，系统繁忙，请稍后重试");
        }
        try {

            if (id == null) {
                throw new CrmebException("无效的商品ID");
            }
            if (!user.getLikeSwitch()) {
                return CommonResult.success("点赞成功", "点赞成功");
            }
            String balanceInterestRequestRechager = systemConfigService.getValueByKey("balance_interest_request_rechager");
            if (StringUtils.isNotBlank(balanceInterestRequestRechager)) {
                BigDecimal bigDecimal = new BigDecimal(balanceInterestRequestRechager);
                if (user.getRechargeAmount().compareTo(bigDecimal) < 0) {
                    throw new CrmebException("参与条件不符，提升入驻资格");
                }
            }
            ProductLikeConfig productConfig = productLikeConfigService.getProductConfig(id);
            if (productConfig == null) {
                return CommonResult.success("点赞成功", "点赞成功");
            }
            if (productConfig.getLimitCount() > 0) {
                Integer awadTodayCount = productLikeRecordService.awadTodayCount(id, productConfig);
                if (awadTodayCount >= productConfig.getLimitCount()) {
                    throw new CrmebException("该商品的点赞奖励已上限");
                }
            }

            //设置了点赞间隔时间要求
            if (productConfig.getLikeInterval() > 0) {
                ProductLikeRecord productLikeRecord = productLikeRecordService.lastLikeInfo(id, user.getUid());
                if (productLikeRecord != null) {
                    //相差的秒数
                    long seconds = DateUtil.calculateDifferenceInSeconds(new Date(), productLikeRecord.getCreateTime());
                    Integer parsed = Integer.parseInt(String.valueOf(seconds / 60));
                    if (parsed < productConfig.getLikeInterval()) {
                        int m = productConfig.getLikeInterval() - parsed;
                        throw new CrmebException(StrUtil.format("下次点赞需在{}分钟后", m));
                    }
                }
            }
            //该商品是否已领取奖励
            Boolean awadToday = productLikeRecordService.awad(id, user.getUid(), productConfig);
            if (awadToday) {
                throw new CrmebException("该商品的点赞奖励已完成");
            }
            //获取该用户点赞次数
            Integer count = productLikeRecordService.likeCount(id, user.getUid(), productConfig);

            ProductLikeRecord productLikeRecord = new ProductLikeRecord();

            if ((count + 1) >= productConfig.getAwardCount()) {
                //满足奖励条件
                //总余额
                BigDecimal totalMoney = user.getNowMoney().add(user.getFrozenBalance());
                //利率
                BigDecimal interest = productConfig.getInterest().divide(new BigDecimal("100")).setScale(4, BigDecimal.ROUND_HALF_UP);
                //奖励金额
                BigDecimal giveMoney = totalMoney.multiply(interest).setScale(2, BigDecimal.ROUND_HALF_UP);
                //计算总余额
                BigDecimal nowMoney = user.getNowMoney().add(giveMoney);
                // 生成UserBill
                UserBill userBill = new UserBill();
                userBill.setUid(user.getUid());
                userBill.setLinkId("0");
                userBill.setTitle("点赞奖励");
                userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                userBill.setNumber(giveMoney);
                userBill.setStatus(1);
                userBill.setCreateTime(DateUtil.nowDateTime());
                userBill.setPm(1);
                userBill.setType(Constants.PRODUCT_LIKE_REWARD);
                userBill.setBalance(nowMoney);
                userBill.setMark(StrUtil.format("点赞奖励余额增加{}元，指定返利：{}%", giveMoney, productConfig.getInterest()));
                userBillService.save(userBill);
                userService.operationNowMoney(user.getUid(), giveMoney, user.getNowMoney(), "add");
                productLikeRecord.setStatus(true);
                productLikeRecord.setMoney(giveMoney);
                productLikeRecord.setInterest(productConfig.getInterest());
            } else {
                productLikeRecord.setStatus(false);
                productLikeRecord.setInterest(BigDecimal.ZERO);
                productLikeRecord.setMoney(BigDecimal.ZERO);
            }
            productLikeRecord.setUserId(user.getUid());
            productLikeRecord.setProductId(id);
            productLikeRecord.setCreateTime(new Date());
            productLikeRecord.setGroupId(user.getGroupId());
            productLikeRecordService.save(productLikeRecord);
            if (productLikeRecord.getStatus()) {
                String string = StrUtil.format("恭喜获得点赞奖励{}元", productLikeRecord.getMoney());
                return CommonResult.success(string, string);
            }
            return CommonResult.success("点赞成功", "点赞成功");
        } catch (Exception e) {
            logger.error("用户点赞错误：{},{}", user.getUid(), e.getMessage(), e);
            throw new CrmebException("点赞失败，请稍后重试！");
        } finally {
            redisUtil.releaseLock(lockKey, requestId);
        }
    }
}



