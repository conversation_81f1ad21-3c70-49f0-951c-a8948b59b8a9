package com.zbkj.front.controller;

import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponse;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponseBody;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.AliyunFaceVeryifyConfig;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserAuth;
import com.zbkj.common.request.UserAuthSubmitRequest;
import com.zbkj.common.request.UserAuthSubmitResponseRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.UserAuthSubmitResponse;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.DescribeFaceVerify;
import com.zbkj.service.service.SystemConfigService;
import com.zbkj.service.service.UserAuthService;
import com.zbkj.service.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/front/auth")
@Api(tags = "用户 -- 实名认证")
public class UserAuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private SystemConfigService systemConfigService;


    @ApiOperation(value = "是否需要认证")
    @RequestMapping(value = "/userAuthSwitch", method = RequestMethod.GET)
    public CommonResult<Boolean> userAuthSwitch() {
        String userAuthSwitch = systemConfigService.getValueByKey("userAuthSwitch");
        return CommonResult.success("1".equals(userAuthSwitch));
    }

    @ApiOperation(value = "获取认证状态")
    @RequestMapping(value = "/state", method = RequestMethod.GET)
    public CommonResult<UserAuth> state() {
        UserAuth userAuth = userAuthService.getState(userService.getUserId());
        if (userAuth!=null){
            User user = userService.getInfo();
            userAuth.setStatus(user.getAuthenticationStatus());
        }
        return CommonResult.success(userAuth);
    }

    @ApiOperation(value = "提交认证")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public CommonResult<UserAuthSubmitResponse> submit(@RequestBody @Validated UserAuthSubmitRequest request, HttpServletRequest httpServletRequest) {
        String clientIp = CrmebUtil.getClientIp(httpServletRequest);
        Integer userId = userService.getInfo().getUid();
        request.setUserId(userId);
        UserAuthSubmitResponse response = userAuthService.submitAuth(request, clientIp);
        return CommonResult.success(response, response.getMsg());
    }

    @ApiOperation(value = "处理结果响应")
    @RequestMapping(value = "/submit/response", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> submitResponse(@RequestBody @Validated UserAuthSubmitResponseRequest request) {
        Map<String, String> extInfo = request.getExtInfo();
        if (extInfo.size() == 0 && StringUtils.isBlank(request.getCertifyId())){
            throw new CrmebException("请提供有效的certifyId");
        }
        String certifyId = null;
        if (StringUtils.isNotBlank(request.getCertifyId())){
            certifyId = request.getCertifyId();
        }else if (extInfo.containsKey("certifyId")){
            certifyId = extInfo.get("certifyId");
        }
        if (StringUtils.isBlank(certifyId)) {
            throw new CrmebException("无效的certifyId参数");
        }

        User user = userService.getInfo();
        if (user.getAuthenticationStatus() == CertificateStatus.APPROVED){
            return CommonResult.success("认证已成功");
        }

        AliyunFaceVeryifyConfig aliyunFaceVeryifyConfig = userAuthService.getAliyunFaceVeryifyConfig();
        DescribeFaceVerifyResponse describeFaceVerifyResponse = DescribeFaceVerify.describeFaceVerifyResult(aliyunFaceVeryifyConfig, certifyId);
        DescribeFaceVerifyResponseBody responseBody = describeFaceVerifyResponse.getBody();

        UserAuth userAuth = userAuthService.getUserAuthByCertId(certifyId, user.getUid());
        if (userAuth == null) {
            throw new CrmebException("无效的提交信息");
        }

        if ("200".equals(responseBody.getCode())) {
            user.setAuthenticationStatus(CertificateStatus.APPROVED);
            user.setRealName(userAuth.getName());
            userAuth.setStatus(CertificateStatus.APPROVED);
        } else {
            user.setAuthenticationStatus(CertificateStatus.REJECTED);
            userAuth.setStatus(CertificateStatus.REJECTED);
        }
        userAuth.setReviewer("系统");
        userAuth.setReviewTime(new Date());
        userAuth.setResult(responseBody.getMessage());
        userService.updateById(user);
        userAuthService.updateById(userAuth);
        if (user.getAuthenticationStatus() == CertificateStatus.APPROVED) {
            return CommonResult.success("认证已成功");
        }
        return CommonResult.failed("认证失败");
    }
}