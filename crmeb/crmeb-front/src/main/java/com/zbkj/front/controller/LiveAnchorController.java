package com.zbkj.front.controller;

import com.zbkj.common.request.AnchorStartBroadcastRequest;
import com.zbkj.common.request.LiveLoginRequest;
import com.zbkj.common.response.AnchorHomeResponse;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.AnchorLoginResponse;
import com.zbkj.common.response.LiveRecordResponse;
import com.zbkj.common.token.AnchorTokenComponent;
import com.zbkj.service.service.LiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户登陆 前端控制器

 */
@Slf4j
@RestController
@RequestMapping("api/front/live/anchor")
@Api(tags = "直播-主播-前台")
public class LiveAnchorController {

    @Resource
    private LiveService liveService;

    @Resource
    private AnchorTokenComponent anchorTokenComponent;

    /**
     * 账号密码登录
     */
    @ApiOperation(value = "直播账号登录")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public CommonResult<AnchorLoginResponse> login(@RequestBody @Validated LiveLoginRequest request) {
        return CommonResult.success(liveService.login(request.getAccount(), request.getPassword()));
    }

    /**
     * 账号密码登录
     */
    @ApiOperation(value = "直播账号登出")
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        Integer merchant = anchorTokenComponent.getMerchant(request);
        anchorTokenComponent.logout(request);
        return CommonResult.success(liveService.logout(merchant));
    }

    @ApiOperation(value = "首页")
    @RequestMapping(value = "/home", method = RequestMethod.POST)
    public CommonResult<AnchorHomeResponse> home(HttpServletRequest request) {
        Integer merchant = anchorTokenComponent.getMerchant(request);
        if(merchant == null){
            return CommonResult.failed("您不是主播");
        }
        return CommonResult.success(liveService.home(merchant));
    }
    @ApiOperation(value = "开始直播")
    @RequestMapping(value = "/startBroadcasting", method = RequestMethod.POST)
    public CommonResult<LiveRecordResponse> startBroadcasting(@RequestBody @Validated AnchorStartBroadcastRequest param, HttpServletRequest request) {
        Integer merchant = anchorTokenComponent.getMerchant(request);
        if(merchant == null){
            return CommonResult.failed("您不是主播");
        }
        return CommonResult.success(liveService.startBroadcast(param, merchant));
    }

    @ApiOperation(value = "结束直播")
    @RequestMapping(value = "/stopBroadcasting", method = RequestMethod.POST)
    public CommonResult<Boolean> stopBroadcasting(HttpServletRequest request) {
        Integer merchant = anchorTokenComponent.getMerchant(request);
        if(merchant == null){
            return CommonResult.failed("您不是主播");
        }
        return CommonResult.success(liveService.endBroadcast(merchant));
    }

    @ApiOperation(value = "商品橱窗开关")
    @RequestMapping(value = "/storeWindowSwitch", method = RequestMethod.POST)
    public CommonResult<Boolean> storeWindowSwitch(HttpServletRequest request) {
        Integer merchant = anchorTokenComponent.getMerchant(request);
        if(merchant == null){
            return CommonResult.failed("您不是主播");
        }
        return CommonResult.success(liveService.storeWindowSwitch(merchant));
    }
}



