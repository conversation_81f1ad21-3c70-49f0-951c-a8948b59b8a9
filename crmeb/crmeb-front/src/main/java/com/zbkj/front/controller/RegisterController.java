package com.zbkj.front.controller;

import com.zbkj.common.request.RegisterRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.RegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 用户登陆 前端控制器

 */
@Slf4j
@RestController
@RequestMapping("api/front")
@Api(tags = "用户 -- 注册")
public class RegisterController {

    @Autowired
    private RegisterService registerService;
    /**
     * 用户注册
     */
    @ApiOperation(value = "用户注册账户")
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public CommonResult<String> register(@RequestBody @Validated RegisterRequest registerRequest) {
        Boolean result = registerService.register(registerRequest);
        return result?CommonResult.success("注册成功"):CommonResult.failed("失败");
    }
}



