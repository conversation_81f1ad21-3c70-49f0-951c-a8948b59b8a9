package com.zbkj.front.controller;

import com.zbkj.common.model.user.UserCardRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;


/**
 * 购物车 前端控制器

 */
@Slf4j
@RestController
@RequestMapping("api/front/card")
@Api(tags = "用户 -- 卡片")
public class UserCardController {
    @Autowired
    private UserService userService;
    @Autowired
    private UserCardService userCardService;
    @Autowired
    private UserCardShareService userCardShareService;
    @Autowired
    private UserCardRecordService userCardRecordService;

    /**
     * 获取所有卡片册
     */
    @ApiOperation(value = "获取所有卡片册")
    @RequestMapping(value = "/cardList", method = RequestMethod.GET)
    public CommonResult<List<HashMap<String, Object>>> cardList() {
        return  CommonResult.success(userCardService.cardList(null));
    }

    /**
     * 今日签到状态
     */
    @ApiOperation(value = "今日签到状态")
    @RequestMapping(value = "/signStatus", method = RequestMethod.GET)
    public CommonResult<Boolean> signStatus() {
        return  CommonResult.success(userCardRecordService.signStatus(userService.getUserId()));
    }

    /**
     * 签到领卡片
     */
    @ApiOperation(value = "签到领卡片")
    @RequestMapping(value = "/sign", method = RequestMethod.GET)
    public CommonResult<HashMap<String, Object>> sign() {
       return  CommonResult.success(userCardService.sign());
    }

    /**
     * 获取我的卡片
     */
    @ApiOperation(value = "获取我的卡片")
    @RequestMapping(value = "/myCard", method = RequestMethod.GET)
    public CommonResult<List<Object>> myCard() {
        return  CommonResult.success(userCardService.myCard());
    }

    /**
     * 全部卡片包含我的卡片
     */
    @ApiOperation(value = "全部卡片包含我的卡片")
    @RequestMapping(value = "/isAllCardsContainMyCard", method = RequestMethod.GET)
    public CommonResult<List<HashMap<String, Object>>> isAllCardsContainMyCard() {
        return  CommonResult.success(userCardService.isAllCardsContainMyCard());
    }

    /**
     * 分享我的卡片
     * @param cardId
     * @return
     */
    @ApiOperation(value = "分享我的卡片")
    @RequestMapping(value = "/share", method = RequestMethod.GET)
    public CommonResult<Boolean> share(@RequestParam Integer cardId) {
        return  CommonResult.success(userCardShareService.share(cardId));
    }

    /**
     * 领取分享的卡片
     * @param code
     * @return
     */
    @ApiOperation(value = "领取分享的卡片")
    @RequestMapping(value = "/receive", method = RequestMethod.GET)
    public CommonResult<HashMap<String, Object>> share(@RequestParam String code) {
        return  CommonResult.success(userCardShareService.receive(code));
    }
    /**
     * 历史获得记录
     * @return
     */
    @ApiOperation(value = "获得卡片记录")
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserCardRecord>> history(@ModelAttribute PageParamRequest pageParamRequest) {
        List<UserCardRecord> history = userCardRecordService.getHistory(pageParamRequest);
        return  CommonResult.success(CommonPage.restPage(history));
    }

}



