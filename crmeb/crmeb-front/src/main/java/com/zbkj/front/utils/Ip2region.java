package com.zbkj.front.utils;

import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * @projectName: mall
 * @package: com.zbkj.front.utils
 * @className: IPData
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/29 11:17
 * @version: 1.0
 */
public class Ip2region {
    public static Searcher searcher = null;

    // 私有化构造方法，禁止外部创建实例
    private Ip2region() {
    }

    // 静态内部类实现单例模式，保证线程安全
    private static class Holder {
        // 单例对象
        private static final Searcher INSTANCE = reloadSearcher();
    }

    // 获取单例实例的方法
    public static Searcher getInstance() {
        return Holder.INSTANCE;
    }

    /**
     * 查询归属地
     *
     * @param ip
     * @return
     */
    public static String getRegion(String ip) {
        try {
            if (ip.contains("111.90.202")){
                return "菲律宾";
            }
            String region = getInstance().search(ip);
            return processString(region);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    // 如果需要重新加载 IP2Region 数据库文件的方法
    public static Searcher reloadSearcher() {
        try {
            byte[] cBuff = StreamUtils.copyToByteArray(Ip2region.class.getResourceAsStream("/ipData/ip2region.xdb"));
            return Searcher.newWithBuffer(cBuff);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws IOException {
        System.out.println(getRegion("**********"));
    }

    public static String processString(String input) {
        // 删除特定子字符串
        String result = input.replace("0|", "");
        // 去除重复值
        Set<String> set = new HashSet<>();
        String[] strings = result.split("\\|");
        if (strings.length == 1){
            return strings[0];
        }else if (strings.length >= 2){
            if (strings[0].equals(strings[1])){
                return strings[0];
            }else {
                return strings[0]+","+strings[1];
            }
        }
        return  "";
    }
}
