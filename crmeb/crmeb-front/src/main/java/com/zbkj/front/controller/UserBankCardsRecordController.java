package com.zbkj.front.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.UserBankCardsRecord;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 购物车 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/bank/record")
@Api(tags = "用户 -- 绑卡")
public class UserBankCardsRecordController {

    @Autowired
    private UserBankCardsRecordService userBankCardsRecordService;

    @Autowired
    private UserService userService;

    /**
     * 任务列表
     *
     * @return
     */
    @ApiOperation(value = "提交绑卡申请记录")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserBankCardsRecord>> list(@ModelAttribute PageParamRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());
        LambdaQueryWrapper<UserBankCardsRecord> lambdaQueryWrapper = new LambdaQueryWrapper<UserBankCardsRecord>();
        lambdaQueryWrapper.eq(UserBankCardsRecord::getUserId, userService.getUserId());
        lambdaQueryWrapper.orderByDesc(UserBankCardsRecord::getCreateTime);
        return CommonResult.success(CommonPage.restPage(userBankCardsRecordService.list(lambdaQueryWrapper)));
    }

    /**
     * 提交绑卡申请
     */
    @ApiOperation(value = "提交绑卡申请")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public CommonResult<Boolean> submit(@RequestBody @Validated UserBankCardsRecord userCardRecord) {
        UserBankCardsRecord recordServiceBankByCardNo = userBankCardsRecordService.getBankByCardNo(userCardRecord.getCardNo());
        if (recordServiceBankByCardNo != null) {
            if (recordServiceBankByCardNo.getStatus() == CertificateStatus.AUDITING) {
                throw new CrmebException("当前账号正在审核中！");
            }else if (recordServiceBankByCardNo.getStatus() == CertificateStatus.APPROVED){
                throw new CrmebException("该账号已认证过！");
            }
        }
        User user = userService.getInfo();
        userCardRecord.setUserId(user.getUid());
        userCardRecord.setAccount(user.getAccount());
        userCardRecord.setStatus(CertificateStatus.APPROVED);//******** 改直接通过无需审核
        return CommonResult.success(userBankCardsRecordService.save(userCardRecord));
    }

    /**
     * 我的绑卡账户
     */
    @ApiOperation(value = "我绑定的账户")
    @RequestMapping(value = "/getMyBank", method = RequestMethod.GET)
    public CommonResult<List<UserBankCardsRecord>> getMyBank(@RequestParam BanksType cardType) {
        List<UserBankCardsRecord> userBankCardsRecordList = userBankCardsRecordService.fetchUserBankCards(userService.getUserId(), cardType);
        return CommonResult.success(userBankCardsRecordList);
    }

}



