package com.zbkj.front.controller;

import com.zbkj.common.model.InvestItems;
import com.zbkj.common.model.InvestItemsOrder;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.InvestItemsBuyRequest;
import com.zbkj.common.request.InvestItemsResponse;
import com.zbkj.common.request.InvestItemsSearchRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.InvestItemsOrderService;
import com.zbkj.service.service.InvestItemsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  控制器
 */
@Slf4j
@RestController
@Api(tags = "投资项目")
@RequestMapping("api/front/investItems")
public class InvestItemsController {
    @Autowired
    private InvestItemsService investItemsService;

    @Autowired
    private InvestItemsOrderService investItemsOrderService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    public CommonResult<CommonPage<InvestItemsResponse>> list(InvestItemsSearchRequest request){
        CommonPage<InvestItemsResponse> page = CommonPage.restPage(investItemsService.getListResponse(request));
        return CommonResult.success(page);
    }
    @PostMapping("/buy")
    @ApiOperation(value = "下单")
    public CommonResult<Boolean> buy(@RequestBody @Validated InvestItemsBuyRequest request){
        Boolean bought = investItemsOrderService.buy(request);
        
        return CommonResult.success(bought);
    }

    @PostMapping("/getInvestItem/{id}")
    @ApiOperation(value = "获取项目信息")
    public CommonResult<InvestItemsResponse> getInvestItem(@PathVariable Integer id){
        InvestItemsResponse investItemsResponse = investItemsService.getItems(id);
        return CommonResult.success(investItemsResponse);
    }

    @PostMapping("/record")
    @ApiOperation(value = "投资记录")
    public CommonResult<CommonPage<InvestItemsOrder>> record(@RequestBody PageParamRequest request){
        CommonPage<InvestItemsOrder> page = CommonPage.restPage(investItemsOrderService.record(request));
        return CommonResult.success(page);
    }


}
