package com.zbkj.front.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.token.AnchorTokenComponent;
import com.zbkj.common.token.FrontTokenComponent;
import com.zbkj.common.utils.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 移动端管理端 token验证拦截器 使用前注意需要一个@Bean手动注解，否则注入无效
 */
public class FrontTokenInterceptor implements HandlerInterceptor {

    @Autowired
    private FrontTokenComponent frontTokenComponent;

    @Resource
    private AnchorTokenComponent anchorTokenComponent;

    //程序处理之前需要处理的业务
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        response.setCharacterEncoding("UTF-8");
        String requestUri = RequestUtil.getUri(request);
        boolean isFromAnchor = anchorTokenComponent.checkRouterIsFromAnchor(requestUri);
        if (isFromAnchor) {
            String anchorToken = anchorTokenComponent.getToken(request);
            //没有token
            if (StringUtils.isBlank(anchorToken)) {
                boolean isAllowed = anchorTokenComponent.checkRouter(requestUri);
                if (isAllowed) {
                    return true;
                }
                response.getWriter().write(JSONObject.toJSONString(CommonResult.unauthorized()));
                return false;
            }
            Boolean check = anchorTokenComponent.check(anchorToken, request);
            if (!check) {
                response.getWriter().write(JSONObject.toJSONString(CommonResult.unauthorized()));
                return false;
            }
            return true;
        }

        String token = frontTokenComponent.getToken(request);
        if (token == null || token.isEmpty()) {
            //判断路由，部分路由不管用户是否登录都可以访问
            boolean result = frontTokenComponent.checkRouter(requestUri);
            if (result) {
                return true;
            }
            response.getWriter().write(JSONObject.toJSONString(CommonResult.unauthorized()));
            return false;
        }

        Boolean result = frontTokenComponent.check(token, request);
        if (!result) {
            response.getWriter().write(JSONObject.toJSONString(CommonResult.unauthorized()));
            return false;
        }
        return true;
    }

    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }

}
