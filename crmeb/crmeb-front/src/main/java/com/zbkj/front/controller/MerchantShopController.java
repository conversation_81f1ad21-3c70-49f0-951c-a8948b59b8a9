package com.zbkj.front.controller;

import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.MerchantShopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理端登录服务

 */
@Slf4j
@RestController("MerchantShopController")
@RequestMapping("api/front/merchant/shop")
@Api(tags = "商户店铺")
public class MerchantShopController {

    @Autowired
    private MerchantShopService merchantShopService;
    @ApiOperation(value="商铺列表")
    @GetMapping(value = "/list")
    public CommonResult<List<MerchantShop>> list(@RequestParam Integer id) {
        List<MerchantShop> list = merchantShopService.getList(id);
        return CommonResult.success(list);
    }
}
