package com.zbkj.front.controller;

import com.zbkj.common.enums.LiveRecordType;
import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.RoomLikeRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.LivePlayRecordResponse;
import com.zbkj.common.response.LiveRecordResponse;
import com.zbkj.service.service.LiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户登陆 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/live/audience")
@Api(tags = "直播-观众-前台")
public class LiveAudienceController {

    @Resource
    private LiveService liveService;

    @ApiOperation(value = "官方直播间")
    @RequestMapping(value = "/officialRoom", method = RequestMethod.POST)
    public CommonResult<LiveRecord> officialRoom() {
        return CommonResult.success(liveService.officialRoom());
    }

    @ApiOperation(value = "更多直播间列表")
    @RequestMapping(value = "/roomList", method = RequestMethod.POST)
    public CommonResult<CommonPage<LiveRecord>> roomList(PageParamRequest request) {
        CommonPage<LiveRecord> liveRecordResponseCommonPage = CommonPage.restPage(liveService.roomList(request));
        return CommonResult.success(liveRecordResponseCommonPage);
    }

    @ApiOperation(value = "首次加载直播间")
    @RequestMapping(value = "/loadLive", method = RequestMethod.POST)
    public CommonResult<LiveRecord> loadLive() {
        List<LiveRecord> liveRecord = liveService.loadLive(null, 1);
        return CommonResult.success(liveRecord.get(0));
    }

    @ApiOperation(value = "进入直播间")
    @RequestMapping(value = "/enterRoom/{roomId}", method = RequestMethod.POST)
    public CommonResult<LivePlayRecordResponse> enterRoom(@PathVariable Long roomId) {
        return CommonResult.success(liveService.enterRoom(roomId));
    }

    @ApiOperation(value = "离开直播间")
    @RequestMapping(value = "/leaveRoom/{roomId}", method = RequestMethod.POST)
    public CommonResult<Boolean> leaveRoom(@PathVariable Long roomId) {
        liveService.leaveRoom(roomId);
        return CommonResult.success();
    }

    @ApiOperation(value = "用户点赞")
    @RequestMapping(value = "/roomLike", method = RequestMethod.POST)
    public CommonResult<Boolean> roomLike(@RequestBody @Validated RoomLikeRequest request) {
        liveService.roomLike(request);
        return CommonResult.success();
    }

    @ApiOperation(value = "用户退出总直播")
    @RequestMapping(value = "/quit/{roomId}", method = RequestMethod.POST)
    public CommonResult<Boolean> quit(@PathVariable Long roomId) {
        liveService.quit(roomId);
        return CommonResult.success();
    }

}



