package com.zbkj.front.controller;

import cn.hutool.core.util.StrUtil;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.enums.ActivityType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.ActivitySwitch;
import com.zbkj.common.model.SystemGroupDataDZPModel;
import com.zbkj.common.model.SystemGroupDataHBModel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserActivityRecord;
import com.zbkj.common.model.user.UserBill;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserActivityRecordRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.CommonUtil;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.service.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 活动 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/activity")
@Api(tags = "用户 -- 活动")
public class UserActivityRecordController {
    @Autowired
    private UserService userService;
    @Autowired
    private SystemGroupDataService systemGroupDataService;
    @Autowired
    private UserActivityRecordService userActivityRecordService;

    @Autowired
    private UserBillService userBillService;

    @ApiOperation(value = "活动开关状态")
    @RequestMapping(value = "/status", method = RequestMethod.GET)
    public CommonResult<ActivitySwitch> status() {
        return CommonResult.success(userActivityRecordService.activityStatus());
    }

    /**
     * 大转盘列表
     */
    @ApiOperation(value = "大转盘列表")
    @RequestMapping(value = "/dzp/list", method = RequestMethod.GET)
    public CommonResult<List<SystemGroupDataDZPModel>> dzpList() {
        List<SystemGroupDataDZPModel> entityByGid = systemGroupDataService.getEntityByGid(Constants.GROUP_DATA_ID_USER_DZP_CONFIG, SystemGroupDataDZPModel.class);
        return CommonResult.success(entityByGid);
    }

    @ApiOperation(value = "大转盘抽奖剩余次数")
    @RequestMapping(value = "/dzp/drawCount", method = RequestMethod.GET)
    public CommonResult<Integer> drawCount() {
        User user = userService.getInfo();
        return CommonResult.success(user.getDzpEveryDaySignNum() + user.getDzpSignNum());
    }

    @ApiOperation(value = "大转盘抽奖")
    @RequestMapping(value = "/dzp/lottery", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<SystemGroupDataDZPModel> dzpLottery() {
        ActivitySwitch activitySwitch = userActivityRecordService.activityStatus();
        if (!activitySwitch.getDzpSwitch()) {
            throw new CrmebException("当前活动暂未开放，敬请期待");
        }
        Integer userId = userService.getUserId();
        synchronized (userId) {
            User user = userService.getInfo();
            if (user.getDzpSignNum() == 0 && user.getDzpEveryDaySignNum() == 0) {
                throw new CrmebException("可抽奖次数不足！");
            }
            List<SystemGroupDataDZPModel> entityByGid = systemGroupDataService.getEntityByGid(Constants.GROUP_DATA_ID_USER_DZP_CONFIG, SystemGroupDataDZPModel.class);
            SystemGroupDataDZPModel dzpProbabilidad = CommonUtil.getRandomObjectFromList(entityByGid, "dzpProbabilidad", SystemGroupDataDZPModel.class);
            if (dzpProbabilidad == null) {
                throw new CrmebException("抽奖失败，请稍后重试！");
            }
            if (user.getDzpEveryDaySignNum() > 0) {
                user.setDzpEveryDaySignNum(user.getDzpEveryDaySignNum() - 1);
            } else if (user.getDzpSignNum() > 0) {
                user.setDzpSignNum(user.getDzpSignNum() - 1);
            }
            userService.updateById(user);

            UserActivityRecord userActivityRecord = new UserActivityRecord();
            userActivityRecord.setUserId(userId);
            userActivityRecord.setActivityName(dzpProbabilidad.getDzpName());
            userActivityRecord.setActivityId(dzpProbabilidad.getId());
            userActivityRecord.setCreateTime(new Date());
            userActivityRecord.setRemark(dzpProbabilidad.getDzpName());
            userActivityRecord.setType(ActivityType.DZP);
            userActivityRecord.setStatus(false);
            userActivityRecord.setOrgItem(dzpProbabilidad.getId() + "/" + dzpProbabilidad.getDzpName());
            userActivityRecordService.save(userActivityRecord);
            return CommonResult.success(dzpProbabilidad);
        }
    }

    @ApiOperation(value = "活动历史记录")
    @RequestMapping(value = "/history", method = RequestMethod.POST)
    public CommonResult<CommonPage<UserActivityRecord>> dzpHistory(@RequestBody UserActivityRecordRequest request) {
        request.setUserId(userService.getUserId());
        return CommonResult.success(CommonPage.restPage(userActivityRecordService.getList(request)));
    }

    @ApiOperation(value = "红包抽奖")
    @RequestMapping(value = "/hb/lottery", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<BigDecimal> hbLottery() {
        ActivitySwitch activitySwitch = userActivityRecordService.activityStatus();
        if (!activitySwitch.getHbSwitch()) {
            throw new CrmebException("当前活动暂未开放，敬请期待");
        }
        Integer userId = userService.getUserId();
        synchronized (userId) {
            User user = userService.getInfo();
            if (user.getHbSignNum() <= 0) {
                throw new CrmebException("可抽奖次数不足！");
            }
            List<SystemGroupDataHBModel> entityByGid = systemGroupDataService.getEntityByGid(Constants.GROUP_DATA_ID_USER_HB_CONFIG, SystemGroupDataHBModel.class);
            SystemGroupDataHBModel hbProbabilidad = CommonUtil.getRandomObjectFromList(entityByGid, "hbProbabilidad", SystemGroupDataHBModel.class);
            if (hbProbabilidad == null) {
                throw new CrmebException("抽奖失败，请稍后重试！");
            }

            BigDecimal amount = CommonUtil.generateRandomAmount(hbProbabilidad.getHbMinAmount(), hbProbabilidad.getHbMaxAmount());

            user.setNowMoney(user.getNowMoney().add(amount));
            user.setHbSignNum(user.getHbSignNum() - 1);
            userService.updateById(user);

            UserActivityRecord userActivityRecord = new UserActivityRecord();
            userActivityRecord.setUserId(userId);
            userActivityRecord.setActivityName(hbProbabilidad.getHbName());
            userActivityRecord.setActivityId(hbProbabilidad.getId());
            userActivityRecord.setCreateTime(new Date());
            userActivityRecord.setRemark(amount + "元");
            userActivityRecord.setType(ActivityType.RED_RNVELOPE);
            userActivityRecord.setStatus(true);
            userActivityRecord.setOrgItem(hbProbabilidad.getId() + "/" + hbProbabilidad.getHbName());
            userActivityRecord.setGroupId(user.getGroupId());
            userActivityRecord.setAmount(amount);
            userActivityRecordService.save(userActivityRecord);

            // 生成UserBill
            UserBill userBill = new UserBill();
            userBill.setUid(user.getUid());
            userBill.setLinkId("0");
            userBill.setTitle("红包抽奖活动");
            userBill.setCategory(Constants.USER_BILL_CATEGORY_USER_HONGBAO);
            userBill.setNumber(amount);
            userBill.setStatus(1);
            userBill.setCreateTime(DateUtil.nowDateTime());
            userBill.setPm(1);
            userBill.setType(Constants.USER_BILL_TYPE_SYSTEM_ADD);
            userBill.setBalance(user.getNowMoney().add(amount));
            userBill.setMark(StrUtil.format("红包抽奖额度增加了{}元", amount));
            userBillService.save(userBill);


            return CommonResult.success(amount);
        }
    }

    @ApiOperation(value = "合并抽奖剩余次数")
    @RequestMapping(value = "/hb/drawCount", method = RequestMethod.GET)
    public CommonResult<Integer> hbDrawCount() {
        User user = userService.getInfo();
        return CommonResult.success(user.getHbSignNum());
    }

}



