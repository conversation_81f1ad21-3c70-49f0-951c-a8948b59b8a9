package com.zbkj.front.utils;

import com.alibaba.fastjson.JSON;
import com.zbkj.common.model.czIpInfo;
import com.zbkj.common.utils.HttpUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @projectName: mall
 * @package: com.zbkj.front.utils
 * @className: IPData
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/29 11:17
 * @version: 1.0
 */
public class cz88 {

    public static String getRegionOfCZ88(String ip) {
        try {
            if (StringUtils.isEmpty(ip)) {
                return "";
            }
            if (ip.equals("127.0.0.1")) {
                return "内网";
            }
            String host = "https://cz88geoaliyun.cz88.net";
            String path = "/search/ip/geo";
            String method = "POST";
            String appcode = "2a4be86f5fa1410e8e82ccae59cfb1e7";
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", "APPCODE " + appcode);
            headers.put("Content-Type", "application/json; charset=UTF-8");
            Map<String, String> querys = new HashMap<String, String>();
            querys.put("ip", ip);
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, "");
            String string = EntityUtils.toString(response.getEntity());
            czIpInfo czIpInfo = JSON.parseObject(string, czIpInfo.class);
            return czIpInfo.getData().getIana();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        //System.out.println(getRegionOfCZ88("127.0.0.1"));
    }
}
