package com.zbkj.front.aspect;

import com.alibaba.fastjson.JSON;
import com.zbkj.common.enums.DeviceType;
import com.zbkj.common.enums.LoginMethod;
import com.zbkj.common.enums.OperationResult;
import com.zbkj.common.enums.PlatformType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.UserLoginLogs;
import com.zbkj.common.model.VersionInfo;
import com.zbkj.common.model.user.User;
import com.zbkj.common.request.LoginRequest;
import com.zbkj.common.response.LoginResponse;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.service.service.UserLoginLogsService;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @projectName: mall
 * @package: aspect
 * @className: LoginAspect
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/24 18:28
 * @version: 1.0
 */


@Aspect
@Component
public class PhoneLoginAspect {
    @Resource
    private UserLoginLogsService userLoginLogsService;

    @Pointcut("execution(* com.zbkj.service.service.LoginService.phoneLogin(..))")
    public void pointCut(){

    }
    @Around("pointCut()")
    public Object aroundLogin(ProceedingJoinPoint joinPoint) throws Throwable {
        return userLoginLogsService.saveLoginLogs(joinPoint,LoginMethod.SMS);
    }

}
