package com.zbkj.front.config;

import com.zbkj.common.interceptor.SwaggerInterceptor;
import com.zbkj.front.filter.IPFilter;
import com.zbkj.front.filter.ResponseFilter;
import com.zbkj.front.interceptor.FrontTokenInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.MappedInterceptor;

/**
 * token验证拦截器

 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    // 这里使用一个Bean为的是可以在拦截器中自由注入，也可以在拦截器中使用SpringUtil.getBean 获取
    // 但是觉得这样更优雅

    @Bean
    public HandlerInterceptor frontTokenInterceptor() {
        return new FrontTokenInterceptor();
    }

    @Bean
    public ResponseFilter responseFilter() {
        return new ResponseFilter();
    }

    @Bean
    public IPFilter ipFilter() {
        return new IPFilter();
    }

    @Value("${swagger.basic.username}")
    private String username;
    @Value("${swagger.basic.password}")
    private String password;
    @Value("${swagger.basic.check}")
    private Boolean check;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //添加token拦截器
        //addPathPatterns添加需要拦截的命名空间；
        //excludePathPatterns添加排除拦截命名空间

        //前端用户登录token
        registry.addInterceptor(frontTokenInterceptor()).
                addPathPatterns("/api/front/**").
                //直播
                excludePathPatterns("/api/front/live/anchor/login").
                //原来的业务
                excludePathPatterns("/api/front/index").
                excludePathPatterns("/api/front/validate/code/get").
                excludePathPatterns("/api/front/storeLocation").
                excludePathPatterns("/api/front/kefu").
                excludePathPatterns("/api/front/qrcode/**").
                excludePathPatterns("/api/front/login/mobile").
                excludePathPatterns("/api/front/login").
                excludePathPatterns("/api/front/register").
                excludePathPatterns("/api/front/sendCode").
                excludePathPatterns("/api/front/wechat/**").
                excludePathPatterns("/api/front/search/keyword").
                excludePathPatterns("/api/front/share").
                excludePathPatterns("/api/front/article/**").
                excludePathPatterns("/api/front/city/**").
                excludePathPatterns("/api/front/product/hot").
                excludePathPatterns("/api/front/product/good").
                excludePathPatterns("/api/front/products/**").
                excludePathPatterns("/api/front/reply/**").
                excludePathPatterns("/api/front/user/service/**").
                excludePathPatterns("/api/front/logistics").
                excludePathPatterns("/api/front/groom/list/**").
                excludePathPatterns("/api/front/config").
                excludePathPatterns("/api/front/category").
                excludePathPatterns("/api/front/category/top").
                excludePathPatterns("/api/front/seckill/*").
                excludePathPatterns("/api/front/seckill/list/*").
                excludePathPatterns("/api/front/seckill/detail/*").
                excludePathPatterns("/api/front/ios/*").
                excludePathPatterns("/api/front/ios/register/binding/phone").
                excludePathPatterns("/api/front/combination/index").
                excludePathPatterns("/api/front/seckill/index").
                excludePathPatterns("/api/front/bargain/index").
                excludePathPatterns("/api/front/combination/index").
                excludePathPatterns("/api/front/index/product/*").
                excludePathPatterns("/api/front/index/color/config").
                excludePathPatterns("/api/front/image/domain").
                excludePathPatterns("/api/front/domain").
                excludePathPatterns("/api/front/appLaunchInfo").
                excludePathPatterns("/api/front/appUpgrade").
                excludePathPatterns("/api/front/anchorAppInit").
                excludePathPatterns("/api/front/product/leaderboard").
                excludePathPatterns("/api/front/merchant/shop/list").
                excludePathPatterns("/api/front/number/prefixes").
                excludePathPatterns("/api/front/activity/dzp/list").
                excludePathPatterns("/api/front/upload/image").
                excludePathPatterns("/api/front/investItems/list").
                excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**")
        ;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Bean
    public FilterRegistrationBean filterRegister() {
        //注册过滤器
        FilterRegistrationBean registration = new FilterRegistrationBean(responseFilter());
        registration.setOrder(2);
        registration.addUrlPatterns("/*");
        return registration;
    }

    // 注册第二个过滤器
    @Bean
    public FilterRegistrationBean filter2Registration() {
        //注册过滤器
        FilterRegistrationBean registration = new FilterRegistrationBean(ipFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1); // 设置过滤器的顺序
        return registration;
    }

    /* 必须在此处配置拦截器,要不然拦不到swagger的静态资源 */
    @Bean
    @ConditionalOnProperty(name = "swagger.basic.enable", havingValue = "true")
    public MappedInterceptor getMappedInterceptor() {
        return new MappedInterceptor(new String[]{"/doc.html", "/webjars/**"}, new SwaggerInterceptor(username, password, check));
    }
}
