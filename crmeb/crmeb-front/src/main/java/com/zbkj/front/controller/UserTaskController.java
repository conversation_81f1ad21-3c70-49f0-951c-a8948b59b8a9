package com.zbkj.front.controller;

import cn.hutool.core.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.enums.TaskStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.ActivitySwitch;
import com.zbkj.common.model.user.UserCardRecord;
import com.zbkj.common.model.user.UserTask;
import com.zbkj.common.model.user.UserTaskSchedule;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserTaskSubmitRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.DateUtil;
import com.zbkj.service.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * 任务 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/task")
@Api(tags = "用户 -- 任务")
public class UserTaskController {
    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private UserTaskScheduleService userTaskScheduleService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityRecordService userActivityRecordService;

    /**
     * 任务列表
     *
     * @return
     */
    @ApiOperation(value = "任务列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserTask>> list(@ModelAttribute PageParamRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());
        Date time = DateUtil.nowDateTime();
        LambdaQueryWrapper<UserTask> lambdaQueryWrapper = new LambdaQueryWrapper<UserTask>();
        lambdaQueryWrapper.le(UserTask::getStartTime, time);
        lambdaQueryWrapper.ge(UserTask::getEndTime, time);
        lambdaQueryWrapper.eq(UserTask::getStatus, true);
        lambdaQueryWrapper.orderByAsc(UserTask::getSort, UserTask::getId);
        List<UserTask> userTaskList = userTaskService.list(lambdaQueryWrapper);
        return CommonResult.success(CommonPage.restPage(userTaskList));
    }

    @ApiOperation(value = "任务提交记录")
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserTaskSchedule>> history(@RequestParam Integer taskId, @ModelAttribute PageParamRequest request) {
        if (taskId == null) {
            throw new CrmebException("无效的任务ID");
        }
        List<UserTaskSchedule> userTaskSchedule = userTaskScheduleService.getUserTaskScheduleByTaskId(userService.getUserId(), taskId, request);
        return CommonResult.success(CommonPage.restPage(userTaskSchedule));
    }

    @ApiOperation(value = "提交任务")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public CommonResult<Boolean> submit(@RequestBody @Validated UserTaskSubmitRequest request) {
        ActivitySwitch activitySwitch = userActivityRecordService.activityStatus();
        if (!activitySwitch.getTaskSwitch()){
            throw new CrmebException("当前活动暂未开放，敬请期待");
        }
        UserTask userTask = userTaskService.getById(request.getTaskId());
        if (userTask == null) {
            throw new CrmebException("无效的任务ID！");
        }
        Integer userId = userService.getUserId();
        Boolean submitToday = userTaskScheduleService.canSubmitToday(userId, userTask.getId(), userTask.getTaskCount());
        if (!submitToday) {
            throw new CrmebException("今日提交次数已上限！");
        }
        UserTaskSchedule userTaskScheduleByTaskId = new UserTaskSchedule();
        userTaskScheduleByTaskId.setTaskId(request.getTaskId());
        userTaskScheduleByTaskId.setStatus(TaskStatus.PENDING);
        userTaskScheduleByTaskId.setCreateTime(new Date());
        userTaskScheduleByTaskId.setCreateDate(new Date());
        userTaskScheduleByTaskId.setUserId(userId);
        userTaskScheduleByTaskId.setTaskContent(request.getTaskContent());
        userTaskScheduleByTaskId.setTaskRemark(request.getTaskRemark());
        userTaskScheduleByTaskId.setTaskVideo(request.getTaskVideo());
        return CommonResult.success(userTaskScheduleService.save(userTaskScheduleByTaskId));
    }

}



