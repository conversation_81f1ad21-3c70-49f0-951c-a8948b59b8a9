<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crmeb</artifactId>
        <groupId>com.zbkj</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>crmeb-front</artifactId>
    <packaging>jar</packaging>

    <properties>
        <crmeb-service>0.0.1-SNAPSHOT</crmeb-service>
        <mainClass>com.zbkj.front.CrmebFrontApplication</mainClass>
        <debugPort>19083</debugPort>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zbkj</groupId>
            <artifactId>crmeb-service</artifactId>
            <version>${crmeb-service}</version>
        </dependency>

        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>META-INF/**</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xdb</include>
                    <include>**/banner.txt</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>2048m</Xmx>
                <Xms>2048m</Xms>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                    </plugin>
<!--                    <plugin>-->
<!--                        <groupId>org.codehaus.mojo</groupId>-->
<!--                        <artifactId>appassembler-maven-plugin</artifactId>-->
<!--                    </plugin>-->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>2048m</Xmx>
                <Xms>2048m</Xms>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                    </plugin>
<!--                    <plugin>-->
<!--                        <groupId>org.codehaus.mojo</groupId>-->
<!--                        <artifactId>appassembler-maven-plugin</artifactId>-->
<!--                    </plugin>-->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>10240m</Xmx>
                <Xms>10240m</Xms>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                    </plugin>
<!--                    <plugin>-->
<!--                        <groupId>org.codehaus.mojo</groupId>-->
<!--                        <artifactId>appassembler-maven-plugin</artifactId>-->
<!--                    </plugin>-->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>docker-dev</id>
            <properties> <!-- 环境标识，需要与配置文件的名称相对应 -->
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>docker-test</id>
            <properties> <!-- 环境标识，需要与配置文件的名称相对应 -->
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>docker-prod</id>
            <properties> <!-- 环境标识，需要与配置文件的名称相对应 -->
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
