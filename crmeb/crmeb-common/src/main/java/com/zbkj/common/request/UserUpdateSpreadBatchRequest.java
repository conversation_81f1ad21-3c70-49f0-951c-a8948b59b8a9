package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量更新推广人请求对象
 */
@Data
@ApiModel(value = "批量更新推广人请求对象")
public class UserUpdateSpreadBatchRequest {
    @ApiModelProperty(value = "待更新的用户ID列表", required = true)
    @NotEmpty(message = "用户ID列表不能为空")
    private List<Integer> userIds;

    @ApiModelProperty(value = "推广人ID", required = true)
    private Integer spreadUid;
}