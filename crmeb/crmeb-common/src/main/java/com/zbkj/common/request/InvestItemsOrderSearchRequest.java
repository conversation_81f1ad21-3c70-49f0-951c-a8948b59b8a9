package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 投资项目订单表
 */
@Data
@ApiModel(value = "InvestItemsOrderSearchRequest对象", description = "投资项目订单表搜索对象")
public class InvestItemsOrderSearchRequest extends PageParamRequest {

    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用戶分組ID")
    private Integer groupId;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "时间")
    private String dateLimit;

}
