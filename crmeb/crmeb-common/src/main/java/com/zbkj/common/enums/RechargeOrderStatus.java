package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RechargeOrderStatus implements IEnum<Integer> {
    SUBMITTED(0, "提交充值"),
    COMPLETED(1, "已充值到账"),
    PENDING(2, "待审核"),
    FAILED(3, "充值失敗");

    private final int value;
    private final String description;

    @Override
    public Integer getValue() {
        return value;
    }
}