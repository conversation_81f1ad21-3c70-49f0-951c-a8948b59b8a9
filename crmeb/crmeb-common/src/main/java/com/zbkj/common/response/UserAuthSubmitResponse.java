package com.zbkj.common.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zbkj.common.enums.CertificateResponseType;
import com.zbkj.common.enums.PlatformType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserAuthSubmitResponse对象", description="实名认证提交")
public class UserAuthSubmitResponse implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "跳转")
    private String url;

    @ApiModelProperty(value = "certifyId")
    private String certifyId;

    @ApiModelProperty(value = "消息")
    @JsonIgnore
    private String msg;

    @ApiModelProperty(value = "操作类型")
    private CertificateResponseType type;
}
