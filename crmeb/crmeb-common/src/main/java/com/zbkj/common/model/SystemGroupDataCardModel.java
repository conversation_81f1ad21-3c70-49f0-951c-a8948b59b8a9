package com.zbkj.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class SystemGroupDataCardModel implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "卡片名称")
    private String jikaName;

    @ApiModelProperty(value = "卡片标识")
    private String  jikaIdent;

    @ApiModelProperty(value = "卡片图标")
    private String jikaIcon;

    @ApiModelProperty(value = "数量")
    @TableField(exist = false)
    private Integer jikaCount;

    @ApiModelProperty(value = "几率")
    @JsonIgnore
    private Float jikaProbabilidad;

    @ApiModelProperty(value = "是否交易")
    @JsonIgnore
    private String jikaShare;

    @ApiModelProperty(value = "集卡包含")
    @JsonIgnore
    private String jikaInclude;

    @ApiModelProperty(value = "历史充值限制分享金额")
    @TableField(exist = false)
    private BigDecimal jikaAmount;
}
