package com.zbkj.common.response;

import com.zbkj.common.enums.RepaymentMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import software.amazon.ion.Decimal;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 文章响应对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InvestItemsResponse", description="投资项目响应对象")
public class InvestItemsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID（主键）")
    private Integer id;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目描述")
    private String projectDescription;

    @ApiModelProperty(value = "项目封面图片路径")
    private String projectCover;

    @ApiModelProperty(value = "项目金额（万元）")
    private Decimal projectAmount;

    @ApiModelProperty(value = "收益率百分比（%）")
    private Decimal profitRate;

    @ApiModelProperty(value = "百分比（%）")
    private Decimal progress;

    @ApiModelProperty(value = "期限（天）")
    private Integer deadlineDays;

    @ApiModelProperty(value = "还款方式")
    private RepaymentMethod repaymentMethod;

    @ApiModelProperty(value = "起投金额（元）")
    private Decimal minInvestment;

    @ApiModelProperty(value = "最大投资金额（元）")
    private Decimal maxInvestment;

    @ApiModelProperty(value = "最大投资次数")
    private Integer maxInvestmentTimes;

    @ApiModelProperty(value = "已投资总金额")
    private BigDecimal investedAmount;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "截止时间")
    private Date endTime;
}
