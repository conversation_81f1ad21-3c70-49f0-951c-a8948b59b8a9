package com.zbkj.common.request;

import com.zbkj.common.enums.CertificateType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserAuthSubmitResponseRequest", description="实名认证结果提交")
public class UserAuthSubmitResponseRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "响应编码", required = true)
    private int code;

    @ApiModelProperty(value = "处理编码", required = true)
    private String subCode;

    @ApiModelProperty(value = "原因", required = true)
    private String reason;

    @ApiModelProperty(value = "其他信息", required = true)
    private Map<String, String> extInfo;

    @ApiModelProperty(value = "访问ID", required = true)
    private String certifyId;

}
