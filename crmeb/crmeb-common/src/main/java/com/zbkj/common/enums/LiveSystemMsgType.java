package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LiveSystemMsgType implements IEnum<String> {
    ENTER_ROOM("ENTER_ROOM", "用户进入直播间"),
    LEAVE_ROOM("LEAVE_ROOM", "用户离开直播间"),
    LIKE("LIKE", "点赞"),
    ANCHOR_OFFLINE("ANCHOR_OFFLINE", "主播下播"),
    SHOWCASE_SWITCH("SHOWCASE_SWITCH", "橱窗开关");

    private final String type;
    private final String description;

    public String getValue() {
        return type;
    }
}
