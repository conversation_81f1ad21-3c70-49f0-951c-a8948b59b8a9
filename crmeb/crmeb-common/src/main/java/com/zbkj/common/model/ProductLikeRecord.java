package com.zbkj.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@TableName("eb_product_like_record")
@ApiModel(value = "商品点赞记录")
public class ProductLikeRecord implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "商品ID")
    private Integer productId;

    @ApiModelProperty(value = "点赞时间")
    private Date createTime;

    @ApiModelProperty(value = "是否满足奖励条件")
    private Boolean status;

    @ApiModelProperty(value = "奖励百分比")
    private BigDecimal interest;

    @ApiModelProperty(value = "组ID")
    private Integer groupId;

    @ApiModelProperty(value = "账号")
    @TableField(exist = false)
    private String account;

    @ApiModelProperty(value = "金额")
    private BigDecimal money;
}
