package com.zbkj.common.request;

import com.zbkj.common.enums.AccessEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提货点搜索
 */
@Data
@ApiModel(value = "UserAccessListSearchRequest", description = "前台名单配置")
public class UserAccessListSearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "IP或地区")
    private String region;

    @ApiModelProperty(value = "类型")
    private AccessEnum type;

    @ApiModelProperty(value = "状态")
    private Boolean status;


}
