package com.zbkj.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.zbkj.common.response.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

public class IdVerifyUtil {

    /*private static final Logger logger = LoggerFactory.getLogger(IdVerifyUtil.class);

    private static AsyncClient asyncClient;
    private static Client client;

    public static Map<String, String> errorMsgMap = new HashMap<>();

    public static Map<String, String> errorResultMsgMap = new HashMap<>();

    static {
        errorMsgMap.put("algorithmError", "识别服务异常，请稍后重试");
        errorMsgMap.put("AlgorithmTimeout", "算法服务不可用，请尝试内容较少的图像。");
        errorMsgMap.put("convertPDFToImgError", "pdf转图片失败");
        errorMsgMap.put("deprecatedApi", "此API已经废弃，请使用新版本的API");
        errorMsgMap.put("ExceededFaceBackCount", "请提交正确的身份证照片");//图片正反面数量超过了限制
        errorMsgMap.put("exceededImageContent", "请提交正确的身份证照片");//图像内容大小超过 10M
        errorMsgMap.put("exceededImageUrlLength", "请提交正确的身份证照片");//图片 URL 长度超过2048
        errorMsgMap.put("exceededPDFContent", "PDF大小超过10M");
        errorMsgMap.put("illegalApiName", "API名称出错");
        errorMsgMap.put("illegalCutType", "不支持的 cutType 参数");
        errorMsgMap.put("illegalImageContent", "请提交正确的身份证照片");//缺少对应的图像内容，导致切图失败
        errorMsgMap.put("illegalImageSize", "请提交正确的身份证照片");//图像尺寸不合法
        errorMsgMap.put("illegalImageType", "请提交正确的身份证照片");//不支持的 imageType 参数
        errorMsgMap.put("IllegalImageUrl", "系统繁忙，请稍后再试");//图片 URL 资源不可用或超时
        errorMsgMap.put("illegalPDFContent", "PDF内容缺失或不是有效的PDF文件");
        errorMsgMap.put("illegalSignature", "签名错误");
        errorMsgMap.put("illegalSubject", "不支持的 subject（科目） 参数");
        errorMsgMap.put("imageTimeOut", "系统繁忙，请稍后再试");//获取图片超时
        errorMsgMap.put("imageUrlOrBodyEmpty", "请提交正确的身份证照片");//图片URL或BODY为空
        errorMsgMap.put("InvalidCountry", "不支持的国家。");
        errorMsgMap.put("InvalidSignatureVerssion", "签名版本错误");
        errorMsgMap.put("invalidStsToken", "STS token 错误");
        errorMsgMap.put("InvalidVersion", "参数版本错误");
        errorMsgMap.put("MissingAccessKeyId", "缺少AccessKeyId");
        errorMsgMap.put("missingImageName", "请提交正确的身份证照片");//必须输入图片名字
        errorMsgMap.put("MissingImageType", "请提交正确的身份证照片");//缺少图片类型
        errorMsgMap.put("MissingImageUrl", "请提交正确的身份证照片");//图片URL为空
        errorMsgMap.put("MissingLanguages", "缺少 language 参数");
        errorMsgMap.put("MissingSignature", "缺少签名");
        errorMsgMap.put("noPermission", "子账号或角色未授权");
        errorMsgMap.put("notSafeImageUrl", "请提交正确的身份证照片");//图片 URL 不安全
        errorMsgMap.put("OcrServiceExpired", "实名认证异常,请联系客服人员");//OCR服务已经欠费过期
        errorMsgMap.put("ocrServiceNotOpen", "OCR服务未开通");
        errorMsgMap.put("PaperCutEmptyImage", "请提交正确的身份证照片");//在用于切题的图像中没有找到任何试卷，请检查图像。
        errorMsgMap.put("ServiceTimeout", "后端服务超时");
        errorMsgMap.put("ServiceUnavailable", "服务器错误");
        errorMsgMap.put("SignatureDoesNotMatch", "签名错误");
        errorMsgMap.put("SignatureNonceUsed", "Signature nonce已经被使用");
        errorMsgMap.put("systemError", "服务器异常，请稍后重试");
        errorMsgMap.put("Throttling.User", "系统繁忙，请稍后再试");//超过限流阈值
        errorMsgMap.put("unmatchedImageType", "请提交正确的身份证照片");//图像类型与API接口不匹配
        errorMsgMap.put("unsupportedEncodeImageUrl", "请提交正确的身份证照片");//图片 URL 编码无法解析
        errorMsgMap.put("unsupportedImageFormat", "请提交正确的身份证照片");//图像内容错误或格式不支持
        errorMsgMap.put("unsupportedLanguage", "不支持的 language 参数");

        errorMsgMap.put("Z5120", "刷脸成功，认证通过");
        errorMsgMap.put("Z1000", "其他异常");
        errorMsgMap.put("Z1001", "人脸识别算法初始化失败");
        errorMsgMap.put("Z1003", "不支持的CPU架构");
        errorMsgMap.put("Z1004", "Android系统版本过低");
        errorMsgMap.put("Z1005", "刷脸超时(单次)");
        errorMsgMap.put("Z1006", "多次刷脸超时");
        errorMsgMap.put("Z1018", "无前置摄像头");
        errorMsgMap.put("Z1019", "摄像头权限未赋予");
        errorMsgMap.put("Z1020", "打开摄像头失败");
        errorMsgMap.put("Z1024", "SDK认证流程正在进行中，请等待本地认证流程完成后再发起新调用");
        errorMsgMap.put("Z1035", "Context为空");
        errorMsgMap.put("Z1036", "认证之前没有调用 install() 接口完成初始化");
        errorMsgMap.put("Z1037", "CertifyId为null或长度为0");
        errorMsgMap.put("Z1038", "混淆配置错误");
        errorMsgMap.put("Z5112", "上传炫彩Meta信息失败");
        errorMsgMap.put("Z5113", "上传炫彩视频失败");
        errorMsgMap.put("Z5114", "认证视频上传失败");
        errorMsgMap.put("Z6001", "OCR识别次数超限");
        errorMsgMap.put("Z6002", "OCR图片上传网络超时");
        errorMsgMap.put("Z6003", "OSS Token过期");
        errorMsgMap.put("Z6004", "人脸照片处理失败");
        errorMsgMap.put("Z7001", "SDK初始化或者使用过程中数据异常");
        errorMsgMap.put("Z1008", "用户在认证过程中点击X退出");
        errorMsgMap.put("Z1009", "用户在授权页面点击“暂不授权”退出");
        errorMsgMap.put("Z1011", "客户端初始化网络错误");
        errorMsgMap.put("Z1012", "客户端网络访问异常");
        errorMsgMap.put("Z1025", "客户端初始化接口返回网络错误");
        errorMsgMap.put("Z1026", "信息上传网络错误");
        errorMsgMap.put("Z1027", "服务端认证接口网络错误");
        errorMsgMap.put("Z1028", "服务端接口并发请求超出限制");
        errorMsgMap.put("2003", "客户端设备时间错误");
        errorMsgMap.put("Z5128", "刷脸失败，认证未通过。");
    }

    public static void main(String[] args) throws Exception {
        String metaInfo = "{\"bioMetaInfo\":\"4.1.0:2916352,0\",\"deviceType\":\"web\",\"ua\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36\",\"apdidToken\":\"\"}";
        String accessKeyId = "LTAI5tGk7V5F2DS4d7kXC2AD";
        String accessKeySecret = "******************************";
        CommonResult<String> verifyToken = getVerifyToken(24, "王福梅", "510727198807260845", metaInfo, accessKeyId, accessKeySecret);
        //System.out.println(verifyToken.getMessage());
    }


    public static CommonResult<String> getVerifyToken(Integer userId, String name, String certNo, String metaInfo, String accessKeyId, String accessKeySecret){
        try {
            if (null == client) {
                client = new Client(new Config().setAccessKeyId(accessKeyId)
                        .setAccessKeySecret(accessKeySecret).setEndpoint("facebody.cn-shanghai.aliyuncs.com"));
            }
            GenRealPersonVerificationTokenRequest genRealPersonVerificationTokenRequest = new GenRealPersonVerificationTokenRequest();
            genRealPersonVerificationTokenRequest.setCertificateName(name);
            genRealPersonVerificationTokenRequest.setCertificateNumber(certNo);
            genRealPersonVerificationTokenRequest.setMetaInfo(metaInfo);
            RuntimeOptions runtime = new RuntimeOptions();

            GenRealPersonVerificationTokenResponse genRealPersonVerificationTokenResponse = client.genRealPersonVerificationTokenWithOptions(genRealPersonVerificationTokenRequest, runtime);
            GenRealPersonVerificationTokenResponseBody.GenRealPersonVerificationTokenResponseBodyData data = genRealPersonVerificationTokenResponse.getBody().getData();
            //logger.info("会员{},姓名{},身份证号{},阿里云获取TOKEN:{}", userId, name, certNo, JSONObject.toJSON(data));
            return CommonResult.success(data.getVerificationToken());
        } catch (Exception e) {
            String msg = e.getMessage();
            String request = msg.substring(msg.indexOf(",") + 1, msg.indexOf("request")).replaceAll(" ","");
            return CommonResult.failed(request);
        }
    }

    public static boolean getVerifyResult(Integer userId, String verificationToken, String accessKeyId, String accessKeySecret) throws Exception {
        if (null == client) {
            client = new Client(new Config().setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret));
        }
        GetRealPersonVerificationResultRequest getRealPersonVerificationResultRequest = new GetRealPersonVerificationResultRequest();
        getRealPersonVerificationResultRequest.setVerificationToken(verificationToken);
        RuntimeOptions runtime = new RuntimeOptions();
        GetRealPersonVerificationResultResponse genRealPersonVerificationTokenRequest = client.getRealPersonVerificationResultWithOptions(getRealPersonVerificationResultRequest, runtime);
        GetRealPersonVerificationResultResponseBody.GetRealPersonVerificationResultResponseBodyData data = genRealPersonVerificationTokenRequest.getBody().getData();
        logger.info("会员{},token-{},阿里云校验结果:{}", userId, verificationToken, JSONObject.toJSON(data));
        return data.getPassed();
    }*/
}
