package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityType implements IEnum<Integer> {
    DZP("大转盘", 1),
    RED_RNVELOPE("红包", 2),
    ;

    private final String type;

    @EnumValue
    private final Integer value;
}
