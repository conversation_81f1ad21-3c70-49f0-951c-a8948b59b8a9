package com.zbkj.common.utils;

import com.zbkj.common.constants.Constants;

/**
 * @projectName: mall
 * @package: com.zbkj.common.utils
 * @className: Recharge
 * @author: Gavin
 * @description: TODO
 * @date: 2024/1/5 16:43
 * @version: 1.0
 */
public class Recharge {
    /**
     * 获取支付文字
     * @param payType String 支付方式
     */
    public static String getPayType(String payType) {
        switch (payType) {
            case Constants.PAY_TYPE_WE_CHAT:
                return Constants.PAY_TYPE_STR_WE_CHAT;
            case Constants.PAY_TYPE_YUE:
                return Constants.PAY_TYPE_STR_YUE;
            case Constants.PAY_TYPE_ALI_PAY:
                return Constants.PAY_TYPE_STR_ALI_PAY;
            case Constants.PAY_TYPE_AI_PAY:
                return Constants.PAY_TYPE_STR_AI_PAY;
            case Constants.PAY_TYPE_M_PAY:
                return Constants.PAY_TYPE_STR_M_PAY;
            case Constants.PAY_TYPE_F_PAY:
                return Constants.PAY_TYPE_STR_F_PAY;
            case Constants.PAY_TYPE_WANB_PAY:
                return Constants.PAY_TYPE_STR_WANB_PAY;
            case Constants.PAY_TYPE_SCAN_QR:
                return Constants.PAY_TYPE_STR_SCAN_PAY;
            case Constants.PAY_TYPE_BANK_PAY:
                return Constants.PAY_TYPE_STR_BANK_PAY;
            case Constants.PAY_TYPE_USDT_PAY:
                return Constants.PAY_TYPE_STR_USDT_PAY;
            case Constants.PAY_TYPE_CB_PAY:
                return "CBPAY钱包";
            case Constants.PAY_TYPE_KD_PAY:
                return "K豆钱包";
            case Constants.PAY_TYPE_TO_PAY:
                return "TOPAY钱包";
            case Constants.PAY_TYPE_OK_PAY:
                return "OKPAY钱包";
            case Constants.PAY_TYPE_AA_PAY:
                return "AA钱包";
            default:
                return Constants.PAY_TYPE_STR_OTHER;
        }
    }
}
