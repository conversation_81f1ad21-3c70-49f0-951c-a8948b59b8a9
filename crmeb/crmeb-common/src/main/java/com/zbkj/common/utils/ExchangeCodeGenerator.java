package com.zbkj.common.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;

/**
 * @projectName: mall
 * @package: com.zbkj.common.utils
 * @className: ExchangeCodeGenerator
 * @author: Gavin
 * @description: TODO
 * @date: 2023/12/6 14:48
 * @version: 1.0
 */
public class ExchangeCodeGenerator {
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final Random RANDOM = new SecureRandom();

    public static String generateExchangeCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int randomIndex = RANDOM.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(randomIndex));
        }
        String modifiedCode = System.currentTimeMillis() + code.toString();
        String modifiedExchangeCode = generateModifiedExchangeCode(modifiedCode);
        return modifiedExchangeCode.toUpperCase();
    }

    public static String generateExchangeCode(int min,int max) {
        int length = min + RANDOM.nextInt(max - min + 1); // 随机生成长度在 [min, max] 范围内的邀请码
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int randomIndex = RANDOM.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(randomIndex));
        }
        return code.toString();
    }

    public static String generateInvitationCode() {
        StringBuilder code = new StringBuilder();

        // 生成两个大写字母
        for (int i = 0; i < 2; i++) {
            char uppercaseLetter = (char) ('A' + RANDOM.nextInt(26)); // ASCII码 A-Z: 65-90
            code.append(uppercaseLetter);
        }

        // 生成五个数字
        for (int i = 0; i < 5; i++) {
            int digit = RANDOM.nextInt(10); // 0-9
            code.append(digit);
        }

        return code.toString();
    }

    public static String generateModifiedExchangeCode(String originalCode) {
        try {
            // 使用 SHA-256 哈希算法对原始兑换码进行处理
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(originalCode.getBytes());

            // 将哈希结果转换为十六进制字符串
            StringBuilder modifiedCode = new StringBuilder();
            for (byte b : hashBytes) {
                modifiedCode.append(String.format("%02x", b));
            }

            // 截取结果，确保长度在20-25位之间
            return modifiedCode.substring(0, Math.min(modifiedCode.length(), 25));

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
}
