package com.zbkj.common.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zbkj.common.enums.LocationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 线下门店和公司地址
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_store_location")
@ApiModel(value = "线下门店和公司地址")
public class StoreLocation implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "类型")
    @NotNull(message = "类型不能为空")
    private LocationType type;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "地址")
    @NotBlank(message = "地址不能为空")
    private String address;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "经度")
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @NotBlank(message = "纬度不能为空")
    private String latitude;

    @ApiModelProperty(value = "排序")
    private Integer ranking;

}
