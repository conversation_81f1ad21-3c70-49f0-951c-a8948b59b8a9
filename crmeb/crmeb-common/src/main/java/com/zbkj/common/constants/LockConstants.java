package com.zbkj.common.constants;

/**
 * 分布式锁常量类
 * 统一管理项目中所有的锁标识
 * 
 * <AUTHOR>
 */
public class LockConstants {

    /**
     * 用户相关锁
     */
    public static class User {
        
        /**
         * 用户数据更新锁标识
         * 用于保护用户基础数据（余额、积分、佣金等）的并发更新
         * 使用场景：
         * - 用户余额变动
         * - 用户积分变动  
         * - 用户佣金变动
         * - 用户认证状态更新
         * - 充值操作
         * - 提现操作
         * - 支付操作
         * - 投资操作
         * - 点赞奖励等
         */
        public static final String USER_DATA_UPDATE = "USER_DATA_UPDATE";
        
        /**
         * 用户认证锁标识
         * 用于保护用户实名认证相关操作
         */
        public static final String USER_AUTH = "USER_AUTH";
        
        /**
         * 用户提现锁标识
         * 用于保护用户提现相关操作
         */
        public static final String USER_WITHDRAW = "USER_WITHDRAW";
        
        /**
         * 用户充值锁标识
         * 用于保护用户充值相关操作
         */
        public static final String USER_RECHARGE = "USER_RECHARGE";
        
        /**
         * 用户支付锁标识
         * 用于保护用户支付相关操作
         */
        public static final String USER_PAYMENT = "USER_PAYMENT";
        
        /**
         * 用户投资锁标识
         * 用于保护用户投资相关操作
         */
        public static final String USER_INVESTMENT = "USER_INVESTMENT";
    }
    
    /**
     * 订单相关锁
     */
    public static class Order {
        
        /**
         * 订单处理锁标识
         */
        public static final String ORDER_PROCESS = "ORDER_PROCESS";
    }
    
    /**
     * 商品相关锁
     */
    public static class Product {
        
        /**
         * 商品库存锁标识
         */
        public static final String PRODUCT_STOCK = "PRODUCT_STOCK";
    }
}
