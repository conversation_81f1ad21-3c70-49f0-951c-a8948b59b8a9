package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CertificateStatus implements IEnum<Integer> {
    PENDING("未认证", 0),

    AUDITING("认证中", 1),

    APPROVED("认证成功", 2),

    REJECTED("认证失败", 3);

    private final String type;

    @EnumValue
    private final Integer value;
}
