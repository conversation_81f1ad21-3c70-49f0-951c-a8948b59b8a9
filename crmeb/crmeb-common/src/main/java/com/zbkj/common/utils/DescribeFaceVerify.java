package com.zbkj.common.utils;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyRequest;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.zbkj.common.model.AliyunFaceVeryifyConfig;

import java.util.Arrays;
import java.util.List;

/**
 * @projectName: mall
 * @package: com.zbkj.common.utils
 * @className: DescribeFaceVerify
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/16 16:32
 * @version: 1.0
 */
public class DescribeFaceVerify {
    public static DescribeFaceVerifyResponse describeFaceVerifyResult(AliyunFaceVeryifyConfig aliyunFaceVeryifyConfig,String certifyId){
        // 通过以下代码创建API请求并设置参数。
        DescribeFaceVerifyRequest request = new DescribeFaceVerifyRequest();
        // 请输入场景ID+L。
        request.setSceneId(aliyunFaceVeryifyConfig.getAliyunScneId());
        request.setCertifyId(certifyId);
        // 推荐，支持服务路由。
        DescribeFaceVerifyResponse response = describeFaceVerifyAutoRoute(request,aliyunFaceVeryifyConfig);
        // 不支持服务自动路由。
        //DescribeFaceVerifyResponse response = describeFaceVerify("cloudauth.cn-shanghai.aliyuncs.com", request);
        return response;
    }

    private static DescribeFaceVerifyResponse describeFaceVerifyAutoRoute(DescribeFaceVerifyRequest request,AliyunFaceVeryifyConfig aliyunFaceVeryifyConfig) {
        // 第一个为主区域Endpoint，第二个为备区域Endpoint。
        List<String> endpoints = Arrays.asList("cloudauth.cn-shanghai.aliyuncs.com", "cloudauth.cn-beijing.aliyuncs.com");
        DescribeFaceVerifyResponse lastResponse = null;
        for (int i = 0; i < endpoints.size(); i++) {
            try {
                DescribeFaceVerifyResponse response = describeFaceVerify(endpoints.get(i), request,aliyunFaceVeryifyConfig);
                lastResponse = response;

                // 服务端错误，切换到下个区域调用。
                if (response != null) {
                    if (500 == response.getStatusCode()) {
                        continue;
                    }
                    if (response.getBody() != null) {
                        if ("500".equals(response.getBody().getCode())) {
                            continue;
                        }
                    }
                }

                return lastResponse;
            } catch (Exception e) {
                if (i == endpoints.size() - 1) {
                    throw new RuntimeException(e);
                }
            }
        }

        return lastResponse;
    }

    private static DescribeFaceVerifyResponse describeFaceVerify(String endpoint, DescribeFaceVerifyRequest request, AliyunFaceVeryifyConfig aliyunFaceVeryifyConfig)
            throws Exception {
        // 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
        // 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
        // 本示例通过阿里云Credentials工具从环境变量中读取AccessKey，来实现API访问的身份验证。如何配置环境变量，请参见https://help.aliyun.com/document_detail/378657.html。

        com.aliyun.credentials.models.Config c = new com.aliyun.credentials.models.Config();
        c.setType(aliyunFaceVeryifyConfig.getAliyunType());
        c.setAccessKeyId(aliyunFaceVeryifyConfig.getAliyunAccessKey());
        c.setAccessKeySecret(aliyunFaceVeryifyConfig.getAliyunAccessKeySecret());
        com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client(c);

        Config config = new Config();
        config.setEndpoint(endpoint);
        config.setCredential(credentialClient);
        Client client = new Client(config);

        // 创建RuntimeObject实例并设置运行参数。
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;

        return client.describeFaceVerifyWithOptions(request, runtime);
    }
}
