package com.zbkj.common.enums;


import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LocationType implements IEnum<Integer> {
    COMPANY("公司", 1),
    STORE("门店", 0);

    private final String type;

    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }
}
