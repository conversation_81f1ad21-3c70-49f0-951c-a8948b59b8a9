package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OperationResult implements IEnum<Integer> {
    SUCCESS("成功", 1),
    FAIL("失败", 0);

    private final String type;

    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }
}
