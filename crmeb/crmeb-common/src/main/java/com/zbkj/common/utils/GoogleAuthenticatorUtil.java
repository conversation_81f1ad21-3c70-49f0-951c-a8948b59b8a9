package com.zbkj.common.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import org.apache.commons.codec.binary.Base32;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.UndeclaredThrowableException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.SecureRandom;

public class GoogleAuthenticatorUtil {

    /**
     * 生成随机的密钥
     *
     * @return
     */
    public static String getSecretKey() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[20];
        random.nextBytes(bytes);
        Base32 base32 = new Base32();
        return base32.encodeToString(bytes).toUpperCase();
    }

    /**
     * 根据密钥，计算出当前时间的动态口令 （30s会变化一次）
     *
     * @param secretKey
     * @return
     */
    public static String getCode(String secretKey) {
        Base32 base32 = new Base32();
        byte[] bytes = base32.decode(secretKey);
        String hexKey = Hex.encodeHexString(bytes);
        long time = (System.currentTimeMillis() / 1000) / 30;
        String hexTime = Long.toHexString(time);
        return TOTP.generateTOTP(hexKey, hexTime, "6");
    }

    /**
     * Return a URL that generates and displays a QR barcode. The user scans this bar code with the
     * Google Authenticator application on their smartphone to register the auth code. They can also
     * manually enter the
     * secret if desired
     *
     * @param user   user id (e.g. fflinstone)
     * @param secret the secret that was previously generated for this user
     * @return the URL for the QR code to scan
     */
    public static String getQRBarcodeURL(String user, String secret) {
        String format = "otpauth://totp/%s?secret=%s";
        return String.format(format, "Mall-" + user, secret);
    }

    /**
     * 根据 TOPT 密钥的 URI 字符串 生成二维码
     *
     * @param barCode
     * @param outputStream
     * @param height
     * @param width
     * @throws WriterException
     * @throws IOException
     */

    public static void createQRCode(String barCode, OutputStream outputStream, int height, int width)
            throws WriterException, IOException {
        BitMatrix matrix = new MultiFormatWriter().encode(barCode, BarcodeFormat.QR_CODE, width, height);
        MatrixToImageWriter.writeToStream(matrix, "png", outputStream);
    }

    /**
     * This is an example implementation of the OATH
     * TOTP algorithm.
     * Visit www.openauthentication.org for more information.
     *
     * <AUTHOR> Rydell, PortWise, Inc.
     */
    public static class TOTP {

        private static final int[] DIGITS_POWER
                // 0 1  2   3    4     5      6       7        8
                = {1, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000};

        private TOTP() {
        }

        /**
         * This method uses the JCE to provide the crypto algorithm.
         * HMAC computes a Hashed Message Authentication Code with the
         * crypto hash algorithm as a parameter.
         *
         * @param crypto:   the crypto algorithm (HmacSHA1, HmacSHA256,
         *                  HmacSHA512)
         * @param keyBytes: the bytes to use for the HMAC key
         * @param text:     the message or text to be authenticated
         */

        private static byte[] hmac_sha(String crypto, byte[] keyBytes, byte[] text) {
            try {
                Mac hmac;
                hmac = Mac.getInstance(crypto);
                SecretKeySpec macKey = new SecretKeySpec(keyBytes, "RAW");
                hmac.init(macKey);
                return hmac.doFinal(text);
            } catch (GeneralSecurityException gse) {
                throw new UndeclaredThrowableException(gse);
            }
        }

        /**
         * This method converts a HEX string to Byte[]
         *
         * @param hex: the HEX string
         * @return: a byte array
         */

        private static byte[] hexStr2Bytes(String hex) {
            // Adding one byte to get the right conversion
            // Values starting with "0" can be converted
            byte[] bArray = new BigInteger("10" + hex, 16).toByteArray();

            // Copy all the REAL bytes, not the "first"
            byte[] ret = new byte[bArray.length - 1];
            System.arraycopy(bArray, 1, ret, 0, ret.length);
            return ret;
        }

        /**
         * This method generates a TOTP value for the given
         * set of parameters.
         *
         * @param key:          the shared secret, HEX encoded
         * @param time:         a value that reflects a time
         * @param returnDigits: number of digits to return
         * @return: a numeric String in base 10 that includes digits
         */

        public static String generateTOTP(String key, String time, String returnDigits) {
            return generateTOTP(key, time, returnDigits, "HmacSHA1");
        }

        /**
         * This method generates a TOTP value for the given
         * set of parameters.
         *
         * @param key:          the shared secret, HEX encoded
         * @param time:         a value that reflects a time
         * @param returnDigits: number of digits to return
         * @return: a numeric String in base 10 that includes digits
         */

        public static String generateTOTP256(String key, String time, String returnDigits) {
            return generateTOTP(key, time, returnDigits, "HmacSHA256");
        }

        /**
         * This method generates a TOTP value for the given
         * set of parameters.
         *
         * @param key:          the shared secret, HEX encoded
         * @param time:         a value that reflects a time
         * @param returnDigits: number of digits to return
         * @return: a numeric String in base 10 that includes digits
         */

        public static String generateTOTP512(String key, String time, String returnDigits) {
            return generateTOTP(key, time, returnDigits, "HmacSHA512");
        }

        /**
         * This method generates a TOTP value for the given
         * set of parameters.
         *
         * @param key:          the shared secret, HEX encoded
         * @param time:         a value that reflects a time
         * @param returnDigits: number of digits to return
         * @param crypto:       the crypto function to use
         * @return: a numeric String in base 10 that includes digits
         */

        public static String generateTOTP(String key, String time, String returnDigits, String crypto) {
            int codeDigits = Integer.decode(returnDigits);
            StringBuilder result;

            // Using the counter
            // First 8 bytes are for the movingFactor
            // Compliant with base RFC 4226 (HOTP)
            StringBuilder timeBuilder = new StringBuilder(time);
            while (timeBuilder.length() < 16) {
                timeBuilder.insert(0, "0");
            }
            time = timeBuilder.toString();

            // Get the HEX in a Byte[]
            byte[] msg = hexStr2Bytes(time);
            byte[] k = hexStr2Bytes(key);
            byte[] hash = hmac_sha(crypto, k, msg);

            // put selected bytes into result int
            int offset = hash[hash.length - 1] & 0xf;

            int binary =
                    ((hash[offset] & 0x7f) << 24) | ((hash[offset + 1] & 0xff) << 16) | ((hash[offset + 2] & 0xff) << 8) | (
                            hash[offset + 3] & 0xff);

            int otp = binary % DIGITS_POWER[codeDigits];

            result = new StringBuilder(Integer.toString(otp));
            while (result.length() < codeDigits) {
                result.insert(0, "0");
            }
            return result.toString();
        }
    }
}
