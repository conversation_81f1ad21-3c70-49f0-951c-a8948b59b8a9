package com.zbkj.common.response;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * f pay参数响应结果
 */
@Data
public class WanbPayCreatePaymentResponse {
    private String data;
    private Integer code;
    private String msg;
    private Data dataJSON;

    public static class Data {
        private String amount;
        private String createtime;
        private String currency;
        private String examount;
        private String exrate;
        private String id;
        private String navigateurl;
        private int notifycount;
        private String orderid;
        private int state;
        private String userid;

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getCreatetime() {
            return createtime;
        }

        public void setCreatetime(String createtime) {
            this.createtime = createtime;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getExamount() {
            return examount;
        }

        public void setExamount(String examount) {
            this.examount = examount;
        }

        public String getExrate() {
            return exrate;
        }

        public void setExrate(String exrate) {
            this.exrate = exrate;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNavigateurl() {
            return navigateurl;
        }

        public void setNavigateurl(String navigateurl) {
            this.navigateurl = navigateurl;
        }

        public int getNotifycount() {
            return notifycount;
        }

        public void setNotifycount(int notifycount) {
            this.notifycount = notifycount;
        }

        public String getOrderid() {
            return orderid;
        }

        public void setOrderid(String orderid) {
            this.orderid = orderid;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }

        public String getUserid() {
            return userid;
        }

        public void setUserid(String userid) {
            this.userid = userid;
        }
    }

    public Data getDataObject() {
        return JSON.parseObject(data, Data.class);
    }
}
