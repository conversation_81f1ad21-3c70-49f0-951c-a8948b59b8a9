package com.zbkj.common.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.enums.CertificateType;
import com.zbkj.common.model.SystemGroupDataCardModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户响应体

 */
@Data
public class UserAuthResponse {
    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "类型")
    private CertificateType type;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "证件号码")
    private String cerNo;

    @ApiModelProperty(value = "证件图片")
    private String cerPic;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "审核人")
    private String reviewer;

    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "状态")
    private CertificateStatus status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "唯一标识")
    private String certifyId;

    @ApiModelProperty(value = "处理结果")
    private String result;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "手动处理")
    private Boolean manually;

}
