package com.zbkj.common.request;

import com.zbkj.common.constants.RegularConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 移动端手机密码登录请求对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LoginRequest对象", description="移动端手机密码登录请求对象")
public class LoginRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "账号", required = true)
    @NotBlank(message = "账号不能为空")
    @JsonProperty(value = "account")
    @Length(max = 32,message = "错误的账号格式，长度在6~18之间")
    private String account;

    @ApiModelProperty(value = "密码", required = true, example = "1~[6,18]")
//    @Pattern(regexp = RegularConstants.PASSWORD, message = "密码格式错误，密码必须以字母开头，长度在6~18之间，只能包含字符、数字和下划线")
    private String password;

    @ApiModelProperty(value = "推广人id")
    @JsonProperty(value = "spread_spid")
    private Integer spreadPid = 0;

    @ApiModelProperty(value = "冠号")
    private String numberCode = "+86";

    @ApiModelProperty(value = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;

    @ApiModelProperty(value = "验证码秘钥")
    @NotBlank(message = "验证码秘钥不能为空")
    private String codeKey;


}
