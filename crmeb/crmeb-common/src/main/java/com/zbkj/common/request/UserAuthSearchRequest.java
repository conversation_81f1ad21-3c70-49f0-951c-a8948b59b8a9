package com.zbkj.common.request;

import com.zbkj.common.enums.CertificateStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提货点搜索
 */
@Data
@ApiModel(value = "UserAuthSearchRequest对象", description = "实名认证条件搜索")
public class UserAuthSearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "状态")
    private CertificateStatus status;

    @ApiModelProperty(value = "审核者")
    private String reviewer;

    @ApiModelProperty(value = "提交时间区间")
    private String dateLimit;

    @ApiModelProperty(value = "证件号码")
    private String cerNo;

    @ApiModelProperty(value = "证件号码")
    private String password;
}
