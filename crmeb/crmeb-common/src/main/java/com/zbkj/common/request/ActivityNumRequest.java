package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**

 */
@Data
@ApiModel(value="ActivityNumRequest对象", description="集卡/转盘/红包/修改数量")
public class ActivityNumRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户ID")
    private Integer id;

    @ApiModelProperty(value = "类型1增加 2减少")
    @NotNull(message = "请提供类型参数")
    private Integer type;

    @ApiModelProperty(value = "数量")
    @NotNull(message = "数量不能为空")
    @Min(value = 1)
    private Integer count;

    @ApiModelProperty(value = "账号")
    private String account;

}
