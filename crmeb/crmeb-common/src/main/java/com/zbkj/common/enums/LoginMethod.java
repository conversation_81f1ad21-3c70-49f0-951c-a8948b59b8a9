package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: mall
 * @package: com.zbkj.common.enums
 * @className: LoginMethod
 * @author: <PERSON>
 * @description: TODO
 * @date: 2024/2/24 18:09
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum LoginMethod implements IEnum<Integer> {
    PASSWORD("密码登录", 1),
    SMS("短信验证码登录", 0);

    private final String type;

    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }
}
