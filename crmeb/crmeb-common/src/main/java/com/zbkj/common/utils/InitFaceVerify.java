package com.zbkj.common.utils;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.InitFaceVerifyRequest;
import com.aliyun.cloudauth20190307.models.InitFaceVerifyResponse;
import com.aliyun.cloudauth20190307.models.InitFaceVerifyResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.zbkj.common.model.AliyunFaceVerificationResponse;
import com.zbkj.common.model.AliyunFaceVeryifyConfig;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @projectName: mall
 * @package: com.zbkj.common.utils
 * @className: InitFaceVerify
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/16 11:06
 * @version: 1.0
 */
public class InitFaceVerify {
    /**
     * web端人脸验证
     * @param orderNo 请求标识
     * @param certName 姓名
     * @param CertNo 证件号码
     * @param metaInfo
     * @param config 配置
     * @return
     */
    public static AliyunFaceVerificationResponse webVery(String orderNo, String certName, String CertNo, String metaInfo, AliyunFaceVeryifyConfig config){
        InitFaceVerifyRequest request = new InitFaceVerifyRequest();
        // 请输入场景ID+L。
        request.setSceneId(config.getAliyunScneId());
        // 设置商户请求的唯一标识。
        request.setOuterOrderNo(orderNo);
        // 认证方案。
        request.setProductCode(config.getAliyunProductCode());
        // 模式。
        request.setModel(config.getAliyunModel());
        request.setCertType(config.getAliyunCertType());
        request.setCertName(certName);
        request.setCertNo(CertNo);
        // MetaInfo环境参数。
        request.setMetaInfo(metaInfo);
        //业务页面回跳的目标地址。
        request.setReturnUrl(config.getAliyunReturnUrl());
        request.setCertifyUrlStyle("L");
        // 推荐，支持服务路由。
        InitFaceVerifyResponse response = initFaceVerifyAutoRoute(request,config);
        // 不支持服务自动路由。
        //InitFaceVerifyResponse response = initFaceVerify("cloudauth.cn-shanghai.aliyuncs.com", request);
        /*response.getBody().getRequestId();
        response.getBody().getResultObject().getCertifyId();
        System.out.println(response.getBody().getRequestId());
        System.out.println(response.getBody().getCode());
        System.out.println(response.getBody().getMessage());
        System.out.println(response.getBody().getResultObject() == null ? null
                : response.getBody().getResultObject().getCertifyId());*/

        AliyunFaceVerificationResponse verificationResponse = new AliyunFaceVerificationResponse();
        BeanUtils.copyProperties(response,verificationResponse);
        InitFaceVerifyResponseBody responseBody = response.getBody();
        String backMessage = responseBody.getMessage();
        String message = responseBody.getMessage();
        switch (responseBody.getCode()) {
            case "200":
                backMessage = "身份信息校验成功，等待人脸识别..";
                break;
            case "400":
                backMessage = "提交身份证和号码为空";
                message = "身份证信息不能为空";
                break;
            case "401":
                if (backMessage.contains("certNo")) {
                    backMessage = "身份证号码不符合国家标准";
                    message = backMessage;
                } else if (backMessage.contains("certName")) {
                    backMessage = "姓名不符合国家标准";
                    message = backMessage;
                }
                break;
            case "402":
                backMessage = "应用配置不存在";
                message = "暂时无法使用刷脸服务，请联系客服！";
                break;
            case "404":
                backMessage = "认证场景配置不存在，请先在控制台上创建认证场景";
                message = "暂时无法使用刷脸服务，请联系客服！";
                break;
            case "410":
                backMessage = "未开通服务，未开通OSS产品或未完成OSS读写授权，请登录控制台完成授权";
                message = "暂时无法使用刷脸服务，请联系客服！";
                break;
            case "411":
                backMessage = "RAM无权限，需要给RAM用户授予AliyunAntCloudAuthFullAccess的操作权限";
                message = "暂时无法使用刷脸服务，请联系客服！";
                break;
            case "412":
                backMessage = "欠费中，金融级实人认证或OSS存在欠费，请充值后操作";
                message = "暂时无法使用刷脸服务，请联系客服！";
                break;
            case "414":
                backMessage = "设备类型不支持，当前移动设备不支持刷脸认证，请更换设备后操作";
                break;
            case "415":
                backMessage = "SDK版本不支持，当前认证SDK版本不支持刷脸认证，请升级SDK后操作";
                break;
            case "416":
                backMessage = "系统版本不支持，当前操作系统版本不支持刷脸认证，请升级系统或更换设备操作";
                break;
            case "417":
                backMessage = "无法使用刷脸服务，当前身份信息比对源不可用。若信息正确，建议人工审核";
                break;
            case "418":
                backMessage = "刷脸失败次数过多，当天刷脸认证次数过多，请明天再试";
                break;
            case "500":
                backMessage = "系统内部错误，请反馈阿里云工程师排查";
                break;
            default:
                break;
        }
        verificationResponse.setBackMessage(backMessage);
        verificationResponse.getBody().setMessage(message);

        return verificationResponse;
    }


    private static InitFaceVerifyResponse initFaceVerifyAutoRoute(InitFaceVerifyRequest request,AliyunFaceVeryifyConfig config) {
        // 第一个为主区域Endpoint，第二个为备区域Endpoint。
        List<String> endpoints = Arrays.asList("cloudauth.cn-shanghai.aliyuncs.com", "cloudauth.cn-beijing.aliyuncs.com");
        InitFaceVerifyResponse lastResponse = null;
        for (int i=0; i<endpoints.size(); i++) {
            try {
                InitFaceVerifyResponse response = initFaceVerify(endpoints.get(i), request,config);
                lastResponse = response;

                // 服务端错误，切换到下个区域调用。
                if(response != null){
                    if(500 == response.getStatusCode()){
                        continue;
                    }
                    if(response.getBody() != null){
                        if("500".equals(response.getBody().getCode())){
                            continue;
                        }
                    }
                }

                // 正常返回
                return lastResponse;
            }catch (Exception e) {
                e.printStackTrace();
                if(i == endpoints.size()-1){
                    throw new RuntimeException(e);
                }
            }
        }

        return lastResponse;
    }

    public static InitFaceVerifyResponse initFaceVerify(String endpoint, InitFaceVerifyRequest request,AliyunFaceVeryifyConfig aliyunFaceVeryifyConfig)
            throws Exception {
        // 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
        // 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
        // 本示例通过阿里云Credentials工具从环境变量中读取AccessKey，来实现API访问的身份验证。如何配置环境变量，请参见https://help.aliyun.com/document_detail/378657.html。

        com.aliyun.credentials.models.Config c = new com.aliyun.credentials.models.Config();
        c.setType(aliyunFaceVeryifyConfig.getAliyunType());
        c.setAccessKeyId(aliyunFaceVeryifyConfig.getAliyunAccessKey());
        c.setAccessKeySecret(aliyunFaceVeryifyConfig.getAliyunAccessKeySecret());
        com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client(c);


        Config config = new Config();
        config.setEndpoint(endpoint);
        config.setCredential(credentialClient);


        Client client = new Client(config);

        // 创建RuntimeObject实例并设置运行参数。
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;

        return client.initFaceVerifyWithOptions(request, runtime);
    }
}
