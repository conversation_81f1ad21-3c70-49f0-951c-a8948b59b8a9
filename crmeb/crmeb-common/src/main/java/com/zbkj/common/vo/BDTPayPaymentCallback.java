package com.zbkj.common.vo;

import lombok.Data;

/**
 * cbpay 回调实体
 */
@Data
public class BDTPayPaymentCallback {
    // 商户号
    private String merchantId;

    // 商户订单号
    private String merchantUniqueOrderId;

    // 结束时间
    private String finishTime;

    // 代付状态, 100 为成功, 90 失败
    private String status;

    // 金额
    private String amount;

    // 随机数
    private String solt;

    // 签名 (根据签名生成方式)
    private String sign;
}
