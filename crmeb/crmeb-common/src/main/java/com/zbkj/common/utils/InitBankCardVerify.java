package com.zbkj.common.utils;

import com.alibaba.fastjson.JSON;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.AliyunBankCardVerificationResponse;
import com.zbkj.common.request.UserAuthSubmitRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class InitBankCardVerify {

//    public static AliyunBankCardVerificationResponse verifyBankCard(UserAuthSubmitRequest request) {
//        String host = "https://ckid.market.alicloudapi.com";
//        String path = "/lundear/verifyBank";
//        String method = "GET";
//        String appcode = "712b81f6bdfb4ed0b5efbe5789eb6804";
//        Map<String, String> headers = new HashMap<>();
//        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
//        headers.put("Authorization", "APPCODE " + appcode);
//
//        // 構建請求參數
//        Map<String, String> body = new HashMap<>();
//        body.put("cardno", request.getCardno());
//        body.put("name", request.getName());
//        body.put("idcard", request.getCerNo());
//        body.put("mobile", request.getMobile());
//
//        String jsonResponse = null;
//        HttpResponse response = null;
//        try {
//            response = HttpUtils.doGet(host, path, method, headers, body);
//            jsonResponse = EntityUtils.toString(response.getEntity(), "UTF-8");
//        } catch (Exception e) {
//            log.error("实名认证，请求失败，网关={}，response={}，jsonResponse={}", host + path, JSON.toJSONString(response), jsonResponse, e);
//            throw new CrmebException("实名认证，请求失败，请稍后再试!");
//        }
//        return JSON.parseObject(jsonResponse, AliyunBankCardVerificationResponse.class);
//    }
}