package com.zbkj.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@Slf4j
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisCache {
    @Autowired
    public RedisTemplate redisTemplate;

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 根据前缀删除 Redis 中的全部键
     *
     * @param prefix
     */
    public void deleteKeysWithPrefix(String prefix) {
        String result = prefix.replaceAll("[:_]%[dsf]", "");
        ScanOptions options = ScanOptions.scanOptions().match(result + "*").build();
        try (Cursor<byte[]> cursor = redisTemplate.getConnectionFactory().getConnection().scan(options)) {
            while (cursor.hasNext()) {
                byte[] keyBytes = cursor.next();
                String key = new String(keyBytes);
                redisTemplate.delete(key);
            }
        } catch (Exception e) {
            // 处理异常
            e.printStackTrace();
        }
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    public <T> long setCacheList(final String key, final List<T> dataList, final long timeout, final TimeUnit unit) {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        expire(key, timeout, unit);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 获得缓存的对象模糊匹配
     * @param key
     * @return
     * @param <T>
     */
    public <T> List<T> getCacheListWithPrefix(final String key) {
        List<T> tArrayList = new ArrayList<>();
        ScanOptions options = ScanOptions.scanOptions().match(key + "*").build();
        try (Cursor<byte[]> cursor = redisTemplate.getConnectionFactory().getConnection().scan(options)) {
            while (cursor.hasNext()) {
                byte[] keyBytes = cursor.next();
                String resultKey = new String(keyBytes);
                T t = this.getCacheObject(resultKey);
                if (t != null) {
                    tArrayList.add(t);
                }
            }
        } catch (Exception e) {
            // 处理异常
            e.printStackTrace();
        }
        return tArrayList;
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) throws IOException {
        Set<String> keys = new HashSet<>();
        // 使用 ScanOptions 构建扫描规则
        ScanOptions options = ScanOptions.scanOptions()
                .match(pattern)  // 匹配模式
                .count(1000000)     // 每次扫描的数量，可以根据需要调整
                .build();

        // 执行 SCAN 命令
        Cursor<byte[]> cursor = redisTemplate.getConnectionFactory().getConnection().scan(options);
        // 遍历 cursor 来获取所有匹配的 keys
        while (cursor.hasNext()) {
            keys.add(new String(cursor.next()));  // 将 byte[] 转换为 String
        }
        // 关闭游标以释放资源
        cursor.close();
        return keys;  // 返回 Set<String> 类型的键集合
    }

    /**
     * 尝试获取分布式锁。
     *
     * @param lockKey   锁的键
     * @param value     锁的唯一值（如UUID）
     * @param timeout   最大等待时间（毫秒）
     * @param leaseTime 锁过期时间（由timeUnit决定）
     * @param timeUnit  leaseTime的时间单位
     * @return true表示获取锁成功，false表示失败
     */
    public boolean tryLock(String lockKey, String value, long timeout, long leaseTime, TimeUnit timeUnit) {
        // 使用 SETNX 来设置锁，并设置过期时间
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, value, leaseTime, timeUnit);
        if (Boolean.TRUE.equals(success)) {
            return true;
        }

        // 如果获取锁失败，开始等待
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeout) {
            success = redisTemplate.opsForValue().setIfAbsent(lockKey, value, leaseTime, timeUnit);
            if (Boolean.TRUE.equals(success)) {
                return true;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return false;
    }


    /**
     * 安全释放锁
     *
     * @param lockKey 锁的键
     * @param value 锁的值（通常是当前线程的唯一标识）
     * @return 是否成功释放锁
     */
    public boolean unlock(String lockKey, String value) {
        try {
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
            Long result = (Long) redisTemplate.execute(redisScript, Collections.singletonList(lockKey), value);
            if (result == null || result == 0) {
                log.warn("锁释放失败，key: {}, value: {}, 可能已过期或被其他线程释放", lockKey, value);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("释放锁异常，key: {}, value: {}, error: {}", lockKey, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 原子加
     * @param key
     * @return
     */
    public Long incrementCounter(String key) {
        return redisTemplate.opsForValue().increment(key, 1);
    }

    /**
     * 原子减
     * @param key
     * @return
     */
    public Long decrementCounter(String key) {
        return redisTemplate.opsForValue().decrement(key, 1);
    }
}
