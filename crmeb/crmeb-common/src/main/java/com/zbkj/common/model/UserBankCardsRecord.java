package com.zbkj.common.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.enums.CertificateStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;



/**
 * 用户绑卡审核记录
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_user_bank_cards_record")
@ApiModel(value = "用户绑卡审核记录")
public class UserBankCardsRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "持卡人")
    @NotBlank(message = "持卡人不能为空")
    private String name;

    @ApiModelProperty(value = "所属类型")
    private BanksType cardType;

    @ApiModelProperty(value = "卡号")
    @NotBlank(message = "账号不能为空")
    private String cardNo;

    @ApiModelProperty(value = "二维码")
    private String qrCode;
    @ApiModelProperty(value = "状态")
    private CertificateStatus status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "审核人")
    private String reviewer;

    @ApiModelProperty(value = "审核时间")
    private Date reviewerTime;

    @ApiModelProperty(value = "银行名称")
    private String bankName;
}
