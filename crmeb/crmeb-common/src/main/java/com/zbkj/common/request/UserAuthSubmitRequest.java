package com.zbkj.common.request;

import com.zbkj.common.enums.CertificateType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value="UserAuthSubmitRequest对象", description="实名认证提交")
public class UserAuthSubmitRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "类型")
    @NotNull(message = "证件类型不能为空")
    private CertificateType type;

    @ApiModelProperty(value = "姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "证件号码")
    @NotBlank(message = "证件号码不能为空")
    private String cerNo;

    @ApiModelProperty(value = "证件照片")
    @NotBlank(message = "证件照片不可为空")
    private String cerPic;

//    @ApiModelProperty(value = "银行卡号")
//    @NotBlank(message = "银行卡号不能为空")
//    private String cardno;
//
//    @ApiModelProperty(value = "手机号")
//    @NotBlank(message = "手机号不能为空")
//    private String mobile;

    @ApiModelProperty(value = "metaInfo")
    @NotBlank(message = "metaInfo不能为空")
    private String metaInfo;

}