package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户充值记录查询对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserRechargReviewRequest对象", description="审核查询对象")
public class UserRechargReviewRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "订单号")
    @NotNull(message = "订单号不能为空")
    private String orderId;

    @ApiModelProperty(value = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status; //0提交充值 1已充值到账 2 待审核 3充值失败
}
