package com.zbkj.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;

import java.io.File;
import java.nio.file.Paths;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 导出工具类
 */
@Slf4j
public class ExportUtil {

    /**
     * 导出Excel文件
     *
     * @param fileName 文件名
     * @param title    文件标题
     * @param voList   数据列表
     * @param aliasMap 别名Map（别名需要与数据列表的数据对应）
     * @return 返回给前端的文件名（路径+文件名）
     */
    public static String exportExcel(String fileName, String title, List<?> voList, LinkedHashMap<String, String> aliasMap) {
        if (StrUtil.isBlank(fileName)) {
            throw new CrmebException("文件名不能为空");
        }
        if (StrUtil.isBlank(title)) {
            throw new CrmebException("标题不能为空");
        }
        if (CollUtil.isEmpty(voList)) {
            throw new CrmebException("数据列表不能为空");
        }
        if (CollUtil.isEmpty(aliasMap)) {
            throw new CrmebException("别名map不能为空");
        }

        // 文件名部分
        String newFileName = UploadUtil.getWebPath() + fileName;
        String filePath = UploadUtil.getServerPath();
        if (StrUtil.isBlank(filePath)) {
            log.error("UploadUtil.getServerPath 返回空路径");
            throw new CrmebException("文件路径不能为空");
        }

        // 判断是否存在当前目录，不存在则创建
        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        // 构建完整路径
        String fullPath = Paths.get(filePath, fileName).toString();
        File excelFile = new File(fullPath);

        // 生成 Excel
        ExcelWriter writer = null;
        try {
            writer = ExcelUtil.getWriter(fullPath);
            CellStyle headCellStyle = writer.getHeadCellStyle();
            Font font = writer.createFont();
            font.setBold(true);
            headCellStyle.setFont(font);

            // 自定义标题别名
            aliasMap.forEach(writer::addHeaderAlias);
            // 合并单元格后的标题行，使用默认标题样式
            writer.merge(aliasMap.size() - 1, title);
            writer.merge(aliasMap.size() - 1, StrUtil.format("生成时间: {}", DateUtil.nowDateTimeStr()));
            // 设置宽度自适应
            writer.setColumnWidth(-1, 22);
            // 写入数据
            writer.write(voList, true);
            // 确保数据写入磁盘
            writer.flush();

            // 验证文件
            if (!excelFile.exists() || excelFile.length() == 0) {
                log.error("文件生成失败或为空: {}", fullPath);
                throw new CrmebException("文件生成失败或为空: " + fullPath);
            }
        } catch (Exception e) {
            log.error("生成 Excel 失败: {}", fullPath, e);
            throw new CrmebException("生成 Excel 失败: " + e.getMessage());
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (Exception e) {
                    log.error("关闭 ExcelWriter 失败: {}", e.getMessage());
                }
            }
        }

        log.info("生成 Excel 成功: {}", fullPath);
        return newFileName;
    }

    /**
     * 上传部分设置
     */
    public static void setUpload(String rootPath, String modelPath, String type) {
        rootPath = "exportFile";
        if (StrUtil.isBlank(rootPath) || StrUtil.isBlank(modelPath) || StrUtil.isBlank(type)) {
            throw new CrmebException("请检查上传参数，上传参数不能为空");
        }
        UploadUtil.setRootPath(rootPath);
        UploadUtil.setModelPath(Constants.UPLOAD_TYPE_IMAGE + "/" + Constants.UPLOAD_TYPE_FILE + "/" + modelPath);
//        UploadUtil.setType(type);
    }


}
