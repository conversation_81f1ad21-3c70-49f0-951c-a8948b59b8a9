package com.zbkj.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * PC登录请求对象

 */
@Data
public class SystemUserGoogleAuthRequest {

    @ApiModelProperty(value = "管理员ID",required = true)
    @NotEmpty(message = "管理员ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "状态", required = true)
    private Boolean status = false;
}
