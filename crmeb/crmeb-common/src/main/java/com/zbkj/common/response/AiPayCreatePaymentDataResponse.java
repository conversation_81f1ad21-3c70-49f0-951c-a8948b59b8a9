package com.zbkj.common.response;

import lombok.Data;

/**
 * ai pay参数 data结果集
 */
@Data
public class AiPayCreatePaymentDataResponse {
    /**
     * 回调接口地址
     */
    private String callbackUrl;
    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 订单手续费
     */
    private double handlingFee;

    /**
     * 订单手续费比例
     */
    private String handlingFeeRatio;

    /**
     * 订单 ID
     */
    private String id;

    /**
     * 商户号
     */
    private int merchantId;

    /**
     * 商户账号
     */
    private String merchantUsername;

    /**
     * 是否为优惠订单
     */
    private boolean offer;

    /**
     * 订单金额
     */
    private double orderAmount;

    /**
     * 平台订单号
     */
    private String orderNo;

    /**
     * 商户订单号
     */
    private String outOrderNo;

    /**
     * 扫码充值页面地址
     */
    private String rechargePage;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 回调状态
     */
    private String callbackStatus;

    /**
     * 交易完成时间
     */
    private String tradeTime;
}
