package com.zbkj.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AnchorStartBroadcastRequest {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty("封面")
    @NotEmpty(message = "封面不能为空")
    private String roomCover;

    @ApiModelProperty("是否开启商品橱窗，true是 ，false 否")
    private Boolean storeWindow;

    public Boolean getStoreWindow() {
        return storeWindow == null ? Boolean.FALSE : storeWindow;
    }
}
