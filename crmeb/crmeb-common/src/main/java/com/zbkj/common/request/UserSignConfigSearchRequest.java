package com.zbkj.common.request;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

	import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 用户签到配置
 */
@Data
@ApiModel(value="UserSignConfigSearchRequest对象", description="用户签到配置搜索对象")
public class UserSignConfigSearchRequest extends PageParamRequest{
	
}
