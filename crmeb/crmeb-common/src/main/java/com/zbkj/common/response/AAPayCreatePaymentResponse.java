package com.zbkj.common.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * cbpay参数响应结果
 */
@Data
public class AAPayCreatePaymentResponse {
    /**
     * 状态码，200成功，其他失败
     */
    private Integer code;
    private Data data;
    /**
     * 错误信息描述
     */
    private String message;
    /**
     * 数据响应校验，校验的数据是data内的json数据
     */
    private String sign;

    @Getter
    @Setter
    public static class Data {
        /**
         * 支付币种地址，支付币种地址
         */
        private String coinAddress;
        /**
         * 支付币种，支付币种
         */
        private String coinCode;
        /**
         * 支付币种金额，支付币种金额
         */
        private String coinMoney;
        /**
         * 货币代码，货币代码
         */
        private String currencyCode;
        /**
         * 货币金额，货币金额
         */
        private String currencyMoney;
        /**
         * 订单过期时间，过期时间
         */
        private long orderExpireTime;
        /**
         * 系统订单id，系统订单id
         */
        private long orderid;
        /**
         * 支付页面地址，支付地址
         */
        private String payAddressurl;
    }
}
