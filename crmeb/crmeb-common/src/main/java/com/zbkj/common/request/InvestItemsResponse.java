package com.zbkj.common.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zbkj.common.enums.RepaymentMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import software.amazon.ion.Decimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;


/**
 *
 */
@Data
@ApiModel(value = "InvestItemsResponse", description = "前端项目对象")
public class InvestItemsResponse extends PageParamRequest {

    @ApiModelProperty(value = "项目ID（主键）")
    private Integer id;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目描述")
    private String projectDescription;

    @ApiModelProperty(value = "项目封面图片路径")
    private String projectCover;

    @ApiModelProperty(value = "项目金额（万元）")
    private BigDecimal projectAmount;

    @ApiModelProperty(value = "进度百分比（%）")
    private BigDecimal progress;

    @ApiModelProperty(value = "期限（天）")
    private Integer deadlineDays;

    @ApiModelProperty(value = "还款方式")
    private RepaymentMethod repaymentMethod;

    @ApiModelProperty(value = "起投金额（元）")
    private BigDecimal minInvestment;

    @ApiModelProperty(value = "最大投资金额（元）")
    private BigDecimal maxInvestment;

    @ApiModelProperty(value = "最大投资次数")
    private Integer maxInvestmentTimes;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "截止时间")
    private Date endTime;

    @ApiModelProperty(value = "收益率百分比（%）")
    private BigDecimal profitRate;
}
