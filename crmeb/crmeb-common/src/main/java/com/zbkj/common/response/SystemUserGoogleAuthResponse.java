package com.zbkj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 主页统计数据对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SystemUserGoogleAuthResponse对象", description = "谷歌验证数据对象")
public class SystemUserGoogleAuthResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "管理员ID")
    private Integer userId;

    @ApiModelProperty(value = "是否更新或者创建")
    private Boolean isUpdate;

    @ApiModelProperty(value = "base64二维码链接")
    private String base64Url;


}
