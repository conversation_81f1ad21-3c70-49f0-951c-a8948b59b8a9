package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "提现订单批量锁定请求对象")
public class UserExtractBatchLockRequest {
    
    @NotEmpty(message = "订单ID列表不能为空")
    @ApiModelProperty(value = "订单ID列表")
    private List<Integer> ids;
    
    @NotNull(message = "锁定状态不能为空")
    @ApiModelProperty(value = "锁定状态：1-锁定，2-解锁")
    private Integer lock;
}