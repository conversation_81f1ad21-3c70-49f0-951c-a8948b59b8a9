package com.zbkj.common.vo;

import com.zbkj.common.enums.RepaymentMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 投资项目订单表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "投资项目订单表")
public class InvestItemsOrderVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID（主键）")
    private Integer id;

    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "期限天数")
    private Integer deadlineDays;

    @ApiModelProperty(value = "还款方式")
    private String repaymentMethod;

    @ApiModelProperty(value = "投资金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "预计收益")
    private BigDecimal expectedEarnings;

    @ApiModelProperty(value = "收益率百分比（%）")
    private BigDecimal profitRate;

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "用户分组id")
    private Integer groupId;

}
