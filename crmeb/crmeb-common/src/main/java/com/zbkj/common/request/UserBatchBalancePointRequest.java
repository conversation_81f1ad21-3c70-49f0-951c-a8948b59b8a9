package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Range;
import java.io.Serializable;

/**
 * 批量处理余额/积分请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="批量处理余额/积分", description="批量处理余额/积分")
public class UserBatchBalancePointRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "操作类型：1=余额，2=积分", required = true)
    @NotNull(message = "操作类型不能为空")
    @Range(min = 1, max = 2, message = "操作类型只能是1或2")
    private Integer operationType;

    @ApiModelProperty(value = "批量数据，每行格式：会员账号±金额=备注 或 会员账号±金额", required = true)
    @NotBlank(message = "批量数据不能为空")
    private String batchData;
}
