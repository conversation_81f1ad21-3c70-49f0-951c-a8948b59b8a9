package com.zbkj.common.enums;


import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RepaymentMethod implements IEnum<Integer> {
    PRINCIPAL_AND_INTEREST_AT_MATURITY(1, "到期还本还息");

    private Integer value;
    private String description;
    @Override
    public Integer getValue() {
        return value;
    }
}
