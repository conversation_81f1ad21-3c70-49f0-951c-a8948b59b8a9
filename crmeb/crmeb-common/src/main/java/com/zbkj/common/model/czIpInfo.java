package com.zbkj.common.model;

import lombok.Data;

/**
 * @projectName: mall
 * @package: com.zbkj.common.model
 * @className: czIpInfo
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/29 21:43
 * @version: 1.0
 */
@Data
public class czIpInfo {
    private int code;
    private Data data;
    private String message;
    private boolean success;
    private String time;

   @lombok.Data
    public static class Data {
        private String continent;
        private String countryCode;
        private String iana;
        private String ianaEn;
        private String ip;
        private String country;
        private String province;
        private String city;
        private String districts;
        private String isp;
        private String geocode;
        private String provinceCode;
        private String cityCode;
        private String districtCode;
    }
}
