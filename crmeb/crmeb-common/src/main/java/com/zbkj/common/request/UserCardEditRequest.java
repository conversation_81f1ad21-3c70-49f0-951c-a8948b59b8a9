package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserCardEditRequest对象", description="卡片编辑")
public class UserCardEditRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不为空")
    private Integer uid;
    @ApiModelProperty(value = "卡片ID")
    @NotNull(message = "卡片ID不为空")
    private Integer cardId;
    @ApiModelProperty(value = "修改数量")
    @NotNull(message = "修改数量不能为空")
    private Integer count;
}
