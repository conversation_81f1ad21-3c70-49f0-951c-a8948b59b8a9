package com.zbkj.common.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * cbpay参数响应结果
 */
@Data
public class AAPayWithdrawalPaymentResponse {
    /**
     * 状态码，200成功，其他失败
     */
    private Integer code;
    private Data data;
    /**
     * 错误信息描述
     */
    private String message;
    /**
     * 数据响应校验，校验的数据是data内的json数据
     */
    private String sign;
    private Integer timestamp;

    @Getter
    @Setter
    public static class Data {
        /**
         * 代付id
         */
        private String withdrawal_id;
        /**
         * 自定义订单号
         */
        private String user_withdrawal_id;
        /**
         * 提现地址
         */
        private String withdrawal_address;
        /**
         * 币种金额
         */
        private String coin_money;
        /**
         * 手续费
         */
        private String commission;
        /**
         * 币种代码
         */
        private String coin_code;
        /**
         * 货币代码
         */
        private String currency_code;
    }
}
