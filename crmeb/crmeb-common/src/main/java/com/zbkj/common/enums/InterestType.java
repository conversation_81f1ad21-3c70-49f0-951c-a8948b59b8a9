package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InterestType implements IEnum<Integer> {
    INTEREST("利息", 1),
    CLOSE("关闭", 2);

    private final String type;

    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }
}
