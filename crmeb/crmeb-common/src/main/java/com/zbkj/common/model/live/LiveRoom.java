package com.zbkj.common.model.live;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.enums.BroBroadcastingStatus;
import com.zbkj.common.enums.RoomStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@TableName("eb_live_room")
@ApiModel(value="LiveRoom", description="直播间")
public class LiveRoom implements Serializable {

    @ApiModelProperty(value = "直播间ID")
    @TableId
    private Long roomId;

    @ApiModelProperty(value = "直播间名称")
    private String roomName;

    @ApiModelProperty(value = "商户")
    private Integer merchant;

    @ApiModelProperty(value = "直播间头像")
    private String roomAvatar;

    @ApiModelProperty(value = "商品橱窗")
    private Boolean storeWindow = Boolean.FALSE;

    @ApiModelProperty(value = "开播状态")
    private BroBroadcastingStatus broadcastingStatus;

    @ApiModelProperty(value = "直播间状态")
    private RoomStatus roomStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
