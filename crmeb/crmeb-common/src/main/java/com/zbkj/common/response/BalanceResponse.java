package com.zbkj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户地址表

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BalanceResponse对象", description="提现金额")
public class BalanceResponse implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "已提现")
    private BigDecimal withdrawn;

    @ApiModelProperty(value = "未提现")
    private BigDecimal unDrawn;

    @ApiModelProperty(value = "佣金总金额")
    private BigDecimal commissionTotal;

    @ApiModelProperty(value = "待提现")
    private BigDecimal ToBeWithdrawn;

    @ApiModelProperty(value = "提现手续费")
    private BigDecimal withdrawalFee;

    @ApiModelProperty(value = "新增用户")
    private Integer newUserNum;

    @ApiModelProperty(value = "余额利息")
    private BigDecimal balanceInterest;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal rechargerAmount;

    @ApiModelProperty(value = "冻结金额")
    private BigDecimal frozenBalance;

    @ApiModelProperty(value = "红包统计")
    private BigDecimal hongBaoTotal;

    @ApiModelProperty(value = "点赞金额统计")
    private BigDecimal totalLikeBalance;
    
    @ApiModelProperty(value = "现金盈余")
    private BigDecimal cashBalance;

    @ApiModelProperty(value = "首充金额")
    private BigDecimal firstTotalAmount;

    @ApiModelProperty(value = "首充人数数量")
    private Integer firstTotalUserNum;

    @ApiModelProperty(value = "充值人数")
    private Integer totalRechargeUserCount;

    @ApiModelProperty(value = "提现人数")
    private Integer totalWithdrawUserCount;

    @ApiModelProperty(value = "投资人数")
    private Integer investorCount;

}
