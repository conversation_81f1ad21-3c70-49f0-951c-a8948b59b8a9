package com.zbkj.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户响应体

 */
@Data
public class UserRechargeExcelVo {

    @ApiModelProperty(value = "用户id")
    private Integer uid;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal price;

    @ApiModelProperty(value = "赠送金额")
    private BigDecimal givePrice;

    @ApiModelProperty(value = "充值类型")
    private String rechargeType;

    @ApiModelProperty(value = "充值状态")
    private String rechargeState;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    @ApiModelProperty(value = "提交图片")
    private String submitPic;

    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "审核人")
    private String reviewBy;

    @ApiModelProperty(value = "备注")
    private String remark;

}
