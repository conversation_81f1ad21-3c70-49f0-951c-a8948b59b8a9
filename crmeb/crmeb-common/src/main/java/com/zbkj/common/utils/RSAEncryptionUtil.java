package com.zbkj.common.utils;

import com.zbkj.common.utils.mpay.RSAUtils;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSAEncryptionUtil {
    public static String encryptAndBase64Encode(String plaintext, String publicKeyBase64) {
        try {
            Base64.Decoder base64 = Base64.getDecoder();
            String string = new String(base64.decode(publicKeyBase64), StandardCharsets.UTF_8);
            PublicKey key = RSAUtils.getPublicKey(string);
            byte[] encrypted = RSAUtils.encrypt(plaintext.getBytes(StandardCharsets.UTF_8), key);
            return  Base64.getUrlEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
