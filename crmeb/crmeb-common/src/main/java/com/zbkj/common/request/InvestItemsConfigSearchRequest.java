package com.zbkj.common.request;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;


/**
 * 投资项目等级收益配置
 */
@Data
@ApiModel(value = "InvestItemsConfigSearchRequest对象", description = "投资项目等级收益配置搜索对象")
public class InvestItemsConfigSearchRequest extends PageParamRequest {

    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "项目ID不能为空")
    private Integer itemId;

}
