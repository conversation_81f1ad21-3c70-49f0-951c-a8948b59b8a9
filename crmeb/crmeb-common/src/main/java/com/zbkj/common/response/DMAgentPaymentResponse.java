package com.zbkj.common.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * cbpay参数响应结果
 */
@Data
public class DMAgentPaymentResponse {
    private String code;
    private String msg;
    private OrderData data;

    @Getter
    @Setter
    public static class OrderData {
        private String orderNo;
        private String appOrderNo;
        private String sign;
        private BigDecimal orderFee;
        private BigDecimal orderAmt;
    }

}
