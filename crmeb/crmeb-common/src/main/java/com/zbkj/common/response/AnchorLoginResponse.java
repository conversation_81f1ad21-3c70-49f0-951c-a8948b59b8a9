package com.zbkj.common.response;

import com.zbkj.common.model.system.MerchantShop;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AnchorLoginResponse {

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "直播间ID")
    private Long roomId;

    @ApiModelProperty(value = "直播间名称")
    private String roomName;

    @ApiModelProperty(value = "商户")
    private Integer merchant;

    @ApiModelProperty(value = "直播间头像")
    private String roomAvatar;

    @ApiModelProperty(value = "商铺信息")
    private MerchantShop merchantShop;

    @ApiModelProperty(value = "IM userSig")
    private String imUsersig;
}
