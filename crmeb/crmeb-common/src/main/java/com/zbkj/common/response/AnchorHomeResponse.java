package com.zbkj.common.response;

import com.zbkj.common.enums.BroBroadcastingStatus;
import com.zbkj.common.enums.RoomStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AnchorHomeResponse {


    @ApiModelProperty(value = "直播间ID")
    private Long roomId;

    @ApiModelProperty(value = "直播间名称")
    private String roomName;

    @ApiModelProperty(value = "商户")
    private Integer merchant;

    @ApiModelProperty(value = "直播间头像")
    private String roomAvatar;

    @ApiModelProperty(value = "开播状态")
    private BroBroadcastingStatus broadcastingStatus;

    @ApiModelProperty(value = "直播间状态")
    private RoomStatus roomStatus;

    @ApiModelProperty(value = "推流地址")
    private String streamUrl;

    @ApiModelProperty(value = "直播场次数")
    private Integer liveTimes = 0;

    @ApiModelProperty(value = "总销售额")
    private BigDecimal saleAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "总销售商品数")
    private Integer saleProduct = 0;

}
