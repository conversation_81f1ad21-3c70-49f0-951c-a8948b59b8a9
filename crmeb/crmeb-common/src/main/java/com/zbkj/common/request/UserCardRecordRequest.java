package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserCardRecordRequest对象", description="卡片历史搜索")
public class UserCardRecordRequest extends PageParamRequest implements Serializable  {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "用户ID")
    private Integer userId;
    @ApiModelProperty(value = "用户账号")
    private String account;
    @ApiModelProperty(value = "卡片ID")
    private Integer cardId;
    @ApiModelProperty(value = "时间区间")
    private String dateLimit;

}
