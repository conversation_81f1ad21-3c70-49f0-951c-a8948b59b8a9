package com.zbkj.common.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户响应体

 */
@Data
public class UserCardShareResponse {
    @ApiModelProperty(value = "ID")
    private Integer id;

    @ApiModelProperty(value = "发送用户ID")
    private Integer sendUserId;

    @ApiModelProperty(value = "发送用户账号")
    private String sendAccount;

    @ApiModelProperty(value = "用户卡片ID")
    private Integer cardId;

    @ApiModelProperty(value = "领取用户ID")
    private Integer receiveUserId;

    @ApiModelProperty(value = "领取用户账号")
    private String receiveAccount;

    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "领取时间")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "兑换码")
    private String code;
}
