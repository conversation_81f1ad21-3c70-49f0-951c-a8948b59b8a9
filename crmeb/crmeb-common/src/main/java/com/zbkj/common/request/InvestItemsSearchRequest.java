package com.zbkj.common.request;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 *
 */
@Data
@ApiModel(value = "InvestItemsSearchRequest对象", description = "搜索对象")
public class InvestItemsSearchRequest extends PageParamRequest {

    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
