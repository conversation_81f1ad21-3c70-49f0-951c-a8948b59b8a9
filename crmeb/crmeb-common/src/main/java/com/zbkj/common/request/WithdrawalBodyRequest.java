package com.zbkj.common.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.enums.BanksType;
import com.zbkj.common.enums.HierarchyLevel;
import com.zbkj.common.enums.UserField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户提现表

 */
@Data
@ApiModel(value="WithdrawalBodyRequest", description="代付出款请求参数")
public class WithdrawalBodyRequest {

    @ApiModelProperty(value = "ID")
    @NotNull(message = "出款ID不能为空")
    private Integer id;

    @ApiModelProperty(value = "代付类型")
    private BanksType type;

}
