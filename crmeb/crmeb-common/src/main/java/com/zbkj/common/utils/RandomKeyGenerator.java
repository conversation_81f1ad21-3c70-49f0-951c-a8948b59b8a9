package com.zbkj.common.utils;

import java.security.SecureRandom;
import java.util.Base64;

public class RandomKeyGenerator {
    public static String generateRandomKey(int keyLength) {
        byte[] randomBytes = new byte[keyLength];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(randomBytes);
        return Base64.getEncoder().encodeToString(randomBytes);
    }

    public static String generateRandomKey() {
        return generateRandomKey(32);
    }

}
