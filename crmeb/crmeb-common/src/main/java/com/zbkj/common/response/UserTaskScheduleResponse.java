package com.zbkj.common.response;

import com.zbkj.common.enums.TaskStatus;
import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户响应体

 */
@Data
public class UserTaskScheduleResponse {
    @ApiModelProperty(value = "ID")
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用户名")
    private String account;

    @ApiModelProperty(value = "任务ID")
    private Integer taskId;

    @ApiModelProperty(value = "提交内容")
    private String taskContent;

    @ApiModelProperty(value = "提交视频")
    private String taskVideo;

    @ApiModelProperty(value = "备注")
    private String taskRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "任务状态")
    private TaskStatus status;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "审核原因")
    private String reason;

    @ApiModelProperty(value = "提交次数")
    private Integer count;
}
