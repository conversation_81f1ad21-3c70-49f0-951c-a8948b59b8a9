package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserInterestConfigRequest对象", description="利息配置搜索")
public class UserInterestConfigRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "用户ID")
    private Integer uid;
    @ApiModelProperty(value = "用户账号")
    private String account;
    @ApiModelProperty(value = "时间区间")
    private String dateLimit;
    @ApiModelProperty(value = "是否启用")
    private Boolean status;

}
