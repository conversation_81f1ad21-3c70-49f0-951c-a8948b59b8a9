package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import software.amazon.ion.Decimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 *
 */
@Data
@ApiModel(value = "InvestItemsBuyRequest", description = "购买对象")
public class InvestItemsBuyRequest {

    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "投资项目ID不能为空")
    private Integer projectId;

    @ApiModelProperty(value = "投资金额")
    @NotNull(message = "投资金额不能为空")
    @Min(value = 1,message = "投资金额不可低于1元")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付密码")
    @NotBlank(message = "请提供有效的支付密码")
    private String paymentPwd;


}
