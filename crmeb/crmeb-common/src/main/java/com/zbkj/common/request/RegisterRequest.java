package com.zbkj.common.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zbkj.common.constants.RegularConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.security.Timestamp;

/**
 * 移动端手机密码登录请求对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RegisterRequest对象", description="注册请求对象")
public class RegisterRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户账号", required = true)
    @NotBlank(message = "账号不能为空")
    @Length(min = 6,max = 32,message = "账号长度为6-32位")
    private String account;

    @ApiModelProperty(value = "用户密码")
    @NotBlank(message = "密码不能为空")
    @Length(min = 6,max = 32,message = "密码长度为6-32位")
    private String password;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "手机号码", required = true, example = "***********")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "性别 (0未知, 1男, 2女, 3保密)")
    private Integer sex;

    @ApiModelProperty(value = "推广人id")
    private Integer spreadPid = 0;

    @ApiModelProperty(value = "冠号")
    private String numberCode = "+86";

    @ApiModelProperty(value = "邀请码")
    @NotBlank(message = "邀请码不能为空")
    private String invitationCode;

    @ApiModelProperty(value = "短信验证码")
    private String validateCode;

    @ApiModelProperty(value = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;

    @ApiModelProperty(value = "验证码秘钥")
    @NotBlank(message = "验证码秘钥不能为空")
    private String codeKey;
}
