package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserCardAddRequest对象", description="卡片新增")
public class UserCardAddRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户账号")
    @NotNull(message = "用户账号不为空")
    private String account;
    @ApiModelProperty(value = "卡片ID")
    @NotNull(message = "卡片ID不为空")
    private Integer cardId;
    @ApiModelProperty(value = "卡片数量")
    @NotNull(message = "数量不能为空")
    private Integer count;
}
