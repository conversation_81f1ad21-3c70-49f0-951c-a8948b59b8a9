package com.zbkj.common.request;

import com.zbkj.common.annotation.StringContains;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 资金监控

 */
@Data
@ApiModel(value="FundsMonitorEditRequest对象", description="资金监控修改对象")
public class FundsMonitorEditRequest implements Serializable {

    @ApiModelProperty(value = "id")
    @NotNull(message = "请提供有效的ID")
    private Integer id;

    @ApiModelProperty(value = "备注")
    @NotNull(message = "账单标题")
    private String title;
}
