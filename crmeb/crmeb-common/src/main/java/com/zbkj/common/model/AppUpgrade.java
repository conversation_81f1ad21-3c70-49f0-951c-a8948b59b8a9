package com.zbkj.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(value = "AppUpgrade", description = "APP更新对象")
public class AppUpgrade implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Android当前版本")
    private String appAndroidCurrentVersion;

    @ApiModelProperty(value = "Android最新版本")
    private String appAndroidLatestVersion;

    @ApiModelProperty(value = "Android强制更新")
    private Boolean appAndroidForceUpdate;

    @ApiModelProperty(value = "Android更新地址")
    private String appAndroidUrl;

    @ApiModelProperty(value = "IOS当前版本")
    private String appIOSCurrentVersion;

    @ApiModelProperty(value = "IOS最新版本")
    private String appIOSLatestVersion;

    @ApiModelProperty(value = "IOS强制更新")
    private Boolean appIOSForceUpdate;

    @ApiModelProperty(value = "IOS更新地址")
    private String appIOSUrl;

    @ApiModelProperty(value = "IOS注销账户")
    private Boolean appIOSAccountLogout;

}
