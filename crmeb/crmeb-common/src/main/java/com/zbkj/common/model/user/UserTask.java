package com.zbkj.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
@TableName("eb_user_task")
@ApiModel(value = "用户任务管理")
public class UserTask implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "标题")
    @NotNull(message = "标题不能为空")
    private String taskTitle;

    @ApiModelProperty(value = "任务内容")
    @NotNull(message = "内容不能为空")
    private String taskContent;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "任务开关")
    private Boolean status;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "新增人")
    private String createBy;

    @ApiModelProperty(value = "任务开始时间")
    @NotNull(message = "任务开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "任务结束时间")
    @NotNull(message = "任结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "任务次数")
    @NotBlank(message = "任务次数不能为空")
    private Integer taskCount;
}
