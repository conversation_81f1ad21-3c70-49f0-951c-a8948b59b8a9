package com.zbkj.common.utils;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PhoneNumberUtils {
    private static final Pattern CHN_MOBILE_PATTERN = Pattern.compile("^1[2-9]\\d{9}$");
    private static final Pattern HK_MOBILE_PATTERN = Pattern.compile("^[5689]\\d{7}$");
    private static final Pattern TW_MOBILE_PATTERN = Pattern.compile("^9\\d{8}$");
    private static final Pattern MACAO_MOBILE_PATTERN = Pattern.compile("^6\\d{7}$");

    public static PhoneNumberUtil phoneNumberUtil;
    static {
        //初始化谷歌验证库
        phoneNumberUtil= PhoneNumberUtil.getInstance();
    }

    /**
     * 验证全球号码格式是否正确
     * @param callingCode 冠号 例如+86
     * @param phoneNumber 电话号码 例如 13235001234
     * @return
     */
    public static boolean isPhoneNumberValid(String callingCode,String phoneNumber){
        switch (callingCode) {
            case "+86"://大陆陆手机号码验证
                return isValidChinaPhoneNumber(phoneNumber);
            case "+852"://香港港手机号码验证
                return isValidHkPhoneNumber(phoneNumber);
            case "+853"://澳门港手机号码验证
                return isValidMacaoPhoneNumber(phoneNumber);
            case "+886"://台湾湾手机号码验证
                return isValidTwPhoneNumber(phoneNumber);
            default:
                //未主动配置的冠号谷歌验证库兜底
                return validPhoneNumberByGoogle(callingCode, phoneNumber);
        }
    }

    /**
     * 谷歌号码验证库
     * @param callingCode
     * @param phoneNumber
     * @return
     */
    public static boolean validPhoneNumberByGoogle(String callingCode, String phoneNumber){
        try {
            // 在解析电话号码时，将国家区号和手机号码拼接为完整的电话号码
            String completePhoneNumber = callingCode + phoneNumber;
            Phonenumber.PhoneNumber number = phoneNumberUtil.parse(completePhoneNumber, null);
            // 验证电话号码是否有效
            return phoneNumberUtil.isValidNumber(number);
        } catch (NumberParseException e) {

        }
        return false;
    }
    /**+
     * 判断是否是大陆手机号
     * 1开头3-9为第二位，后面9位数
     * @param phoneNumber
     * @return
     */
    public static boolean isValidChinaPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)){
            return false;
        }
        return CHN_MOBILE_PATTERN.matcher(phoneNumber).matches();
    }

    /**
     * 是否是香港手机号
     * 8位数字组成，以5、6、8或9开头
     * @param phoneNumber
     * @return
     */
    public static boolean isValidHkPhoneNumber(String phoneNumber) {
        Matcher matcher = HK_MOBILE_PATTERN.matcher(phoneNumber);
        return matcher.matches();
    }
    /**
     * 是否是台湾手机号
     * 9开头，后跟8位数字。
     * @param phoneNumber
     * @return
     */
    public static boolean isValidTwPhoneNumber(String phoneNumber) {
        Matcher matcher = TW_MOBILE_PATTERN.matcher(phoneNumber);
        return matcher.matches();
    }

    /**
     * 是否是澳门手机号
     * 6开头，后跟7位数字。
     * @param phoneNumber
     * @return
     */
    public static boolean isValidMacaoPhoneNumber(String phoneNumber) {
        Matcher matcher = MACAO_MOBILE_PATTERN.matcher(phoneNumber);
        return matcher.matches();
    }

}
