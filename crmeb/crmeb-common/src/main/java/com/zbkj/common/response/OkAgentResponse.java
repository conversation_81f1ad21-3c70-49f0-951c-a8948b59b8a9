package com.zbkj.common.response;

import lombok.Data;

@Data
public class OkAgentResponse {
    private Integer code;
    private String data;  // 原始的 data 字符串
    private DataClass dataClass;  // 解析后的 data 对象
    private String msg;

    @Data
    public static class DataClass {
        /** 编号 */
        private String id;
        /** 商户订单号 */
        private String orderid;
        /** 订单类型 */
        private String ordertype;
        /** 订单状态 */
        private String state;
        /** 出币方编号 */
        private String sendid;
        /** 收币方编号 */
        private String recvid;
        /** 币数量 */
        private String amount;
        /** 出币手续费 */
        private String sendcharge;
        /** 收币手续费 */
        private String recvcharge;
        /** 创建日期 */
        private String createtime;
        /** 转币日期 */
        private String transtime;
        /** 说明 */
        private String note;
        /** 系统备注 */
        private String remark;
        /** 签名 */
        private String sign;
    }
}


