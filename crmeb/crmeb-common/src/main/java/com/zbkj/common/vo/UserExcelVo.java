package com.zbkj.common.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.model.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品ExcelVo对象类

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserExcelVo对象", description = "用户信息导出")
public class UserExcelVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户id")
    private Integer uid;

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "分组名称")
    private String groupName;


    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "最后一次登录ip")
    private String lastIp;

    @ApiModelProperty(value = "用户余额")
    private BigDecimal nowMoney;

    @ApiModelProperty(value = "佣金金额")
    private BigDecimal brokeragePrice;

    @ApiModelProperty(value = "用户剩余积分")
    private Integer integral;

    @ApiModelProperty(value = "用户剩余经验")
    private Integer experience;

    @ApiModelProperty(value = "连续签到天数")
    private Integer signNum;

    @ApiModelProperty(value = "账号状态")
    private Boolean status;

    @ApiModelProperty(value = "等级")
    private Integer level;

    @ApiModelProperty(value = "用户购买次数")
    private Integer payCount;

    @ApiModelProperty(value = "详细地址")
    private String addres;

    @ApiModelProperty(value = "注册时间")
    private Date createTime;

    @ApiModelProperty(value = "最后一次登录时间")
    private Date lastLoginTime;

    @ApiModelProperty(value = "充值总金额")
    private BigDecimal rechargeAmount;

    @ApiModelProperty(value = "提现总金额")
    private BigDecimal withdrawAmount;

    @ApiModelProperty(value = "冠号")
    private String numberCode;

    @ApiModelProperty(value = "冻结余额")
    private BigDecimal frozenBalance;

    @ApiModelProperty(value = "余额利息%")
    private BigDecimal balanceInterest;

    @ApiModelProperty(value = "提现手续费%")
    private BigDecimal withdrawFee;

    @ApiModelProperty(value = "认证状态")
    private String authenticationStatus;

    @ApiModelProperty(value = "邀请码")
    private String invitationCode;
}
