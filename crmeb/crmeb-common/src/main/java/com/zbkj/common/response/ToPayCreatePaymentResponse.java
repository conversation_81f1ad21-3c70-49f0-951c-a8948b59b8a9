package com.zbkj.common.response;

import lombok.Data;

@Data
public class ToPayCreatePaymentResponse {
    private Integer code;
    private String data;  // 原始的 data 字符串
    private DataClass dataClass;  // 解析后的 data 对象
    private String msg;

    @Data
    public static class DataClass {
        private String amount;
        private String createtime;
        private String id;
        private String note;
        private String notifyurl;
        private String orderid;
        private String ordertype;
        private String qrcode;
        private String navurl;
        private String qrurl;
        private String recvcharge;
        private String recvid;
        private String remark;
        private String returnurl;
        private String sendcharge;
        private String sendid;
        private String sign;
        private String state;
        private String transtime;
    }
}


