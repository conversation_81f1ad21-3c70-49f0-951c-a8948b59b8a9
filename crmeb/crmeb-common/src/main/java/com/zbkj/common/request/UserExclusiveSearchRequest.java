package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 移动端手机密码登录请求对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserExclusiveSearchRequest", description="专属用户搜索")
public class UserExclusiveSearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "名称", required = false)
    private String name;
}
