package com.zbkj.common.request;

import com.zbkj.common.enums.HierarchyLevel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户充值记录查询对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserRechargeSearchRequest对象", description="用户充值记录查询对象")
public class UserRechargeSearchRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    @ApiModelProperty(value = "today,yesterday,lately7,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/")
    private String dateLimit;

    @ApiModelProperty(value = "用户uid")
    private Integer uid;

    @ApiModelProperty(value = "层级用户ID")
    private Integer levelUid;

    @ApiModelProperty(value = "层级")
    private HierarchyLevel levelType;

    @ApiModelProperty(value = "充值状态")
    private Integer rechargeStatus;

    @ApiModelProperty(value = "充值方式")
    private String rechargeMethod;

    @ApiModelProperty(value = "充值最小金额")
    private BigDecimal amountMin;

    @ApiModelProperty(value = "充值金额最大值")
    private BigDecimal amountMax;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;

    @ApiModelProperty(value = "充值次数")
    private Integer rechargeCount;

}
