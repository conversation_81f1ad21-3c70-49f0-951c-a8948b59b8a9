package com.zbkj.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "AliyunBankCardVerificationResponse", description = "阿里云银行卡校验响应")
public class AliyunBankCardVerificationResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "响应状态码，0表示成功")
    private Integer code;

    @ApiModelProperty(value = "响应描述")
    private String desc;

    @ApiModelProperty(value = "银行卡详细信息")
    private BankCardData data;

    /**
     * 嵌套类，用于银行卡详细信息
     */
    @Data
    @ApiModel(value = "BankCardData", description = "银行卡详细信息")
    public static class BankCardData implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "银行编码")
        private String bankId;

        @ApiModelProperty(value = "银行名称")
        private String bankName;

        @ApiModelProperty(value = "银行英文缩写")
        private String abbr;

        @ApiModelProperty(value = "卡名称")
        private String cardName;

        @ApiModelProperty(value = "卡类型")
        private String cardType;

        @ApiModelProperty(value = "卡BIN")
        private String cardBin;

        @ApiModelProperty(value = "卡BIN长度")
        private Integer binLen;

        @ApiModelProperty(value = "卡所在地区")
        private String area;

        @ApiModelProperty(value = "银行电话")
        private String bankPhone;

        @ApiModelProperty(value = "银行网址")
        private String bankUrl;

        @ApiModelProperty(value = "银行Logo的URL")
        private String bankLogo;
    }
}