package com.zbkj.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.zbkj.common.enums.ActivityType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
@TableName("eb_user_activity_record")
@ApiModel(value = "用户活动记录表")
public class UserActivityRecord implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "奖品ID")
    @NotNull(message = "请选择有效的奖品")
    private Integer activityId;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "抽奖时间")
    @NotNull(message = "抽奖时间不能为空")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "类型")
    @NotNull(message = "活动类型不能为空")
    private ActivityType type;

    @ApiModelProperty(value = "处理状态")
    private Boolean status;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "原始中奖记录")
    private String orgItem;

    @ApiModelProperty(value = "用户账号")
    @NotBlank(message = "请提供有效的用户账号")
    @TableField(exist = false)
    private String account;

    @ApiModelProperty(value = "奖品名称")
    private String activityName;

    @ApiModelProperty(value = "组ID")
    private Integer groupId;

    @ApiModelProperty(value = "实际金额")
    private BigDecimal amount;
}
