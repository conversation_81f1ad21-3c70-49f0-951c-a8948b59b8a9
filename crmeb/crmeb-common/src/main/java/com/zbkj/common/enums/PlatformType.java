package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlatformType implements IEnum<Integer> {
    H5("H5", 1),
    PC("PC", 2),
    IOS("iOS", 3),
    ANDROID("Android", 4),
    MACOS("Mac OS", 6),
    OTHER("其他", 5);

    private final String type;

    @EnumValue
    private final Integer value;
}
