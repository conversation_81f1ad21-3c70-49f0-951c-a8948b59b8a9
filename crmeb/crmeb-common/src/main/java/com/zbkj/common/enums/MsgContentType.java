package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MsgContentType implements IEnum<Integer> {
    TEXT("文本消息", 0),
    EMOJI("表情消息", 1),
    IMAGE("图片消息", 2),
    AUDIO("音频消息", 3);

    private final String type;

    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }
}
