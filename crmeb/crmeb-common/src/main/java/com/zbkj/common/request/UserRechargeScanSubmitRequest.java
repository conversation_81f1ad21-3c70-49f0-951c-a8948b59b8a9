package com.zbkj.common.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充值

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserRechargeScanSubmitRequest对象", description="充值提交")
public class UserRechargeScanSubmitRequest implements Serializable {

    private static final long serialVersionUID=1L;


    @ApiModelProperty(value = "订单号")
    @NotNull(message = "订单号不能为空")
    private String orderId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "提交的图片")
    private String submitPic;
}
