package com.zbkj.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.zbkj.common.enums.LikeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
@TableName("eb_product_like_config")
@ApiModel(value = "商品点赞配置")
public class ProductLikeConfig implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "商品ID")
    @NotBlank(message = "商品不能为空")
    private String productId;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "请输入有效的时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "请输入有效的时间")
    private Date endTime;

    @ApiModelProperty(value = "奖励要求次数")
    @NotNull(message = "奖励要求次数不能为空")
    private Integer awardCount;

    @ApiModelProperty(value = "每次点赞间隔时间(分) 0无限制")
    private Integer likeInterval;

    @ApiModelProperty(value = "奖励百分比")
    @NotNull(message = "奖励百分比不能为空")
    private BigDecimal interest;

    @ApiModelProperty(value = "限制奖励数量 0无限制")
    private Integer limitCount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "类型")
    @NotNull(message = "类型不能为空")
    private LikeType type;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;
}
