package com.zbkj.common.vo;

import lombok.Data;

/**
 * okpay 回调实体
 */
@Data
public class OkPayPaymentCallback {
    /**
     * 编号
     */
    private String id;

    /**
     * 商户订单号
     */
    private String orderid;

    /**
     * 订单类型
     */
    private String ordertype;

    /**
     * 订单状态
     */
    private String state;

    /**
     * 出币方
     */
    private String sendid;

    /**
     * 收币方
     */
    private String recvid;

    /**
     * 币数量
     */
    private String amount;

    /**
     * 出币手续费
     */
    private String sendcharge;

    /**
     * 收币手续费
     */
    private String recvcharge;

    /**
     * 创建日期
     */
    private String createtime;

    /**
     * 转币日期
     */
    private String transtime;

    /**
     * 二维码地址
     */
    private String qrurl;

    /**
     * 回调地址
     */
    private String notifyurl;

    /**
     * 二维码内容
     */
    private String qrcode;

    /**
     * 前台跳转地址
     */
    private String returnurl;

    /**
     * 说明
     */
    private String note;

    /**
     * 系统备注
     */
    private String remark;

    /**
     * 签名
     */
    private String sign;

    /**
     * 回调签名（创建返回时为空，服务器回调时有值）
     */
    private String retsign;

    /**
     * H5 支付页面
     */
    private String navurl;
}
