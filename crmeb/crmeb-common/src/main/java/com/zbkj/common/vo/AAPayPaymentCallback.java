package com.zbkj.common.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * aapay 回调实体
 */
@Data
public class AAPayPaymentCallback {
    private Long code;
    private String message;
    private String sign;
    private Long timestamp;
    private Data data;

    @Getter
    @Setter
    public class Data {
        @JSONField(ordinal = 1)
        private Long order_id;//订单ID
        @JSONField(ordinal = 2)
        private Integer order_status;//订单状态:1.支付中,2.支付成功3.支付失败4.支付超时
        @JSONField(ordinal = 3)
        private String currency_code;//支付订单发起货币代码
        @JSONField(ordinal = 4)
        private String coin_code; //支付订单发起支付币种
        @JSONField(ordinal = 5)
        private String coin_address;//支付订单支付币种地址
        @JSONField(ordinal = 6)
        private String user_order_id;//自定义订单id
        @JSONField(ordinal = 7)
        private String coin_receipt_money;//地址实际收到币种金额（币种取这个）
        @JSONField(ordinal = 8)
        private String currency_receipt_money; //转换实际收到货币金额（法币取这个）

    }

}
