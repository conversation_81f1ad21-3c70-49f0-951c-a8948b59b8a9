package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CardType implements IEnum<Integer> {
    SIGN_IN("签到", 1),
    RECEIVE("兑换", 2),
    GIVE_AWAY("赠送", 3),
    DEDUCT("扣减", 4)
    ;

    private final String type;

    @EnumValue
    private final Integer value;
}
