package com.zbkj.common.request;

import com.zbkj.common.enums.LocationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;


/**
 * 线下门店和公司地址
 */
@Data
@ApiModel(value = "StoreLocationSearchRequest对象", description = "线下门店和公司地址搜索对象")
public class StoreLocationSearchRequest extends PageParamRequest {

    @ApiModelProperty(value = "类型")
    private LocationType type;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "状态")
    private Boolean status;

}
