package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserField implements IEnum<Integer> {
    REGISTER_TIME_ASC("注册时间-正序", 1),
    REGISTER_TIME_DESC("注册时间-倒序", 2),
    BALANCE_ASC("余额-正序", 3),
    BALANCE_DESC("余额-倒序", 4),
    TOTAL_RECHARGE_ASC("充值总金额-正序", 5),
    TOTAL_RECHARGE_DESC("充值总金额-倒序", 6),
    TOTAL_WITHDRAWAL_ASC("提现总金额-正序", 7),
    TOTAL_WITHDRAWAL_DESC("提现总金额-倒序", 8),
    POINTS_ASC("积分-正序", 9),
    POINTS_DESC("积分-倒序", 10);
    private final String type;
    private final Integer value;
    @Override
    public Integer getValue() {
        return value;
    }
}
