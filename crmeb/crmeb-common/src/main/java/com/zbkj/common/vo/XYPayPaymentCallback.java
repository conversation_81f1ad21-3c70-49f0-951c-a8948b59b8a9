package com.zbkj.common.vo;

import lombok.Data;

/**
 * cbpay 回调实体
 */
@Data
public class XYPayPaymentCallback {
    // 商户编号，3-5位的纯数字
    private String mchid;

    // 商户订单号
    private String out_trade_no;

    // 订单金额
    private String amount;

    // 处理的时间
    private String successdate;

    // 交易状态码（1：成功 2：待审核 3：处理中 4：驳回 5：未知 6：交易不存在 7：冲正）
    private int code;

    // 状态信息（待处理,待审核,处理中,驳回等）
    private String msg;

    // 订单备注/驳回理由
    private String remark;

    // 签名
    private String sign;
}
