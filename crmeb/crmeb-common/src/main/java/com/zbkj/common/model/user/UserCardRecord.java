package com.zbkj.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.enums.CardType;
import com.zbkj.common.model.SystemGroupDataCardModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;


@Data
@TableName("eb_user_card_record")
@Accessors(chain = true)
@ApiModel(value="UserCardRecord对象", description="用户卡片记录表")
public class UserCardRecord implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用户卡片ID")
    private Integer cardId;

    @ApiModelProperty(value = "类型")
    private CardType type;

    @ApiModelProperty(value = "创建时间")

    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建日期")
    private LocalDate createDate;

    @TableField(exist = false)
    private SystemGroupDataCardModel systemGroupDataCardModel;

    @ApiModelProperty(value = "数量")
    private Integer count;

}
