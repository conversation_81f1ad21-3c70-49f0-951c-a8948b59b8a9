package com.zbkj.common.model.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_merchant_shop")
@ApiModel(value="MerchantShop对象", description="商户店铺表")
public class MerchantShop implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "店铺名称")
    @NotNull(message = "店铺名称不能为空")
    private String shopName;

    @ApiModelProperty(value = "店铺LOGO")
    @NotNull(message = "店铺LOGO不能为空")
    private String shopLogo;

    @ApiModelProperty(value = "店铺状态")
    private Boolean shopStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "商户id")
    @NotNull(message = "商户ID不能为空")
    private Integer merchant;

    @ApiModelProperty(value = "店铺额度")
    private BigDecimal balance;

    @ApiModelProperty(value = "分类ID")
    @NotNull(message = "分类不能为空")
    private Integer categoryId;

    @ApiModelProperty(value = "限制金额")
    private BigDecimal limitAmount;

}
