package com.zbkj.common.model.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;


@Data
@TableName("eb_system_user_google_auth")
@ApiModel(value="SystemUser谷歌验证码对象", description="后台谷歌验证表")
public class SystemUserGoogleAuth {

    @Id
    @TableId(value = "user_id", type = IdType.NONE)
    @ApiModelProperty(value = "账号ID")
    private int userId;

    @ApiModelProperty(value = "谷歌验证码的密钥")
    private String secretKey;

    @ApiModelProperty(value = "是否启用谷歌验证码")
    private boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updateBy;
}
