package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserCardShareRequest对象", description="卡片分享搜索")
public class UserCardShareRequest extends PageParamRequest implements Serializable  {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "分享用户ID")
    private Integer sendUserId;
    @ApiModelProperty(value = "领取用户ID")
    private Integer receiveUserId;
    @ApiModelProperty(value = "分享用户名")
    private String sendAccount;
    @ApiModelProperty(value = "领取用户名")
    private String receiveAccount;
    @ApiModelProperty(value = "分享时间区间")
    private String sendDateLimit;
    @ApiModelProperty(value = "领取时间区间")
    private String receiveDateLimit;
    @ApiModelProperty(value = "兑换码")
    private String code;
    @ApiModelProperty(value = "卡片ID")
    private Integer cardId;

}
