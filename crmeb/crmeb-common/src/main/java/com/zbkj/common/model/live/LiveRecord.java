package com.zbkj.common.model.live;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.enums.BroBroadcastingStatus;
import com.zbkj.common.model.system.MerchantShop;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@TableName("eb_live_record")
@ApiModel(value="LiveRecord", description="直播记录")
public class LiveRecord implements Serializable {

    @ApiModelProperty(value = "记录ID")
    private Long id;

    @ApiModelProperty(value = "直播间ID")
    private Long roomId;

    @ApiModelProperty(value = "商户")
    private Integer merchant;

    @ApiModelProperty(value = "直播间名称")
    private String roomName;

    @ApiModelProperty(value = "直播间标题")
    private String title;

    @ApiModelProperty(value = "直播间封面")
    private String roomCover;

    @ApiModelProperty(value = "开播状态")
    private BroBroadcastingStatus broadcastingStatus;

    @ApiModelProperty(value = "直播开始时间")
    private Date startTime;

    @ApiModelProperty(value = "直播结束时间")
    private Date endTime;

    @ApiModelProperty(value = "本场销售额")
    private BigDecimal saleAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "本场销售商品数")
    private Integer saleProduct = 0;

    @ApiModelProperty(value = "商店Logo")
    @TableField(exist = false)
    private String shopLogo;

    @ApiModelProperty(value = "橱窗开关")
    @TableField(exist = false)
    private Boolean storeWindow;

    @ApiModelProperty(value = "拉流地址")
    @TableField(exist = false)
    private String pullStreamUrl;

    @ApiModelProperty(value = "m3u8拉流地址(H5使用)")
    @TableField(exist = false)
    private String m3u8StreamUrl;

    @ApiModelProperty(value = "点赞数")
    @TableField(exist = false)
    private Long likeCount;


}
