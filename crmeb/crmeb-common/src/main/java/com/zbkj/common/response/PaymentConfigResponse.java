package com.zbkj.common.response;

import com.zbkj.common.enums.PaymentType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页经营数据响应对象

 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PaymentConfigResponse对象", description="支付通道")
public class PaymentConfigResponse implements Serializable {

    private static final long serialVersionUID = -1486435421582495511L;

    private String name;
    private String icon;
    private String value;
    private String title;
    private PaymentType type;//支持类型 1订单支付  2充值支付 0都支持

    /*public PaymentConfigResponse(String name,String icon,String value,String title,PaymentType type){
        this.name = name;
        this.icon = icon;
        this.value = value;
        this.title = title;
        this.type = type;
    }*/
}
