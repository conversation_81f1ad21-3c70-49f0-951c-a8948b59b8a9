package com.zbkj.common.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * aapay 回调实体
 */
@Data
public class AAAgentPayPaymentCallback {
    private Integer code;           // 状态码
    private String message;     // 消息
    private String sign;        // 签名
    private Long timestamp;     // 时间戳
    private Data data;

    @Getter
    @Setter
    public class Data {
        @JSONField(ordinal = 1)
        private Long withdrawal_id;             // 代付ID
        @JSONField(ordinal = 2)
        private Long user_id;                   // 用户ID
        @JSONField(ordinal = 3)
        private String user_withdrawal_id;       // 自定义订单号
        @JSONField(ordinal = 4)
        private String withdrawal_address;      // 提现地址
        @JSONField(ordinal = 5)
        private BigDecimal coin_money;          // 币种金额(USDT)
        @JSONField(ordinal = 6)
        private BigDecimal commission;         // 手续费
        @JSONField(ordinal = 7)
        private String coin_code;               // 币种代码
        @JSONField(ordinal = 8)
        private String tx_id;                   // 交易哈希
        @JSONField(ordinal = 9)
        private BigDecimal currency_amount;     // 货币金额
        @JSONField(ordinal = 10)
        private Integer withdrawal_status;      // 提现状态

    }

}
