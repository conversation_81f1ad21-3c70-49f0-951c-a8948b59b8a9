package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CertificateType implements IEnum<Integer> {
    MAINLAND_IDENTITY_CARD("大陆身份证", 1),

    TAIWAN_IDENTITY_CARD_OR_PASSPORT("台湾居住证", 2),

    HONGKONG_IDENTITY_CARD_OR_PASSPORT("香港居住证", 3),

    MACAO_IDENTITY_CARD_OR_PASSPORT("澳门居住证", 4),

    OTHER("其他国家/地区", 5);

    private final String type;

    @EnumValue
    private final Integer value;
}
