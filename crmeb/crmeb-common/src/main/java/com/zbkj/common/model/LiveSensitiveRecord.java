package com.zbkj.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 直播间敏感词命中记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Getter
@Setter
@TableName("eb_live_sensitive_record")
public class LiveSensitiveRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 敏感词ID
     */
    private Long parentId;

    /**
     * 创建时间
     */
    private Date createTime;
}
