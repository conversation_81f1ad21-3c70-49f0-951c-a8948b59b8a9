package com.zbkj.common.request;

import com.zbkj.common.enums.LoginMethod;
import com.zbkj.common.enums.OperationResult;
import com.zbkj.common.enums.PaymentType;
import com.zbkj.common.enums.PlatformType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 提货点搜索

 */
@Data
@ApiModel(value="UserLoginLogsSearchRequest对象", description="用户登录日志搜索")
public class UserLoginLogsSearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "登录IP")
    private String LoginIp;

    @ApiModelProperty(value = "登录状态")
    private OperationResult loginStatus;

    @ApiModelProperty(value = "登录平台")
    private PlatformType loginPlatform;

    @ApiModelProperty(value = "登录方式")
    private LoginMethod loginMethod;

    @ApiModelProperty(value = "登录时间区间")
    private String dateLimit;
}
