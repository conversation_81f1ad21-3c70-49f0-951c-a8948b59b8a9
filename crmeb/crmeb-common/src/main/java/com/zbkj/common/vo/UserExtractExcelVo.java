package com.zbkj.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户响应体

 */
@Data
public class UserExtractExcelVo {

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "提现金额")
    private BigDecimal extractPrice;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(value = "提现方式")
    private String extractType;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "收款账号")
    private String bankCode;

    @ApiModelProperty(value = "收款码")
    private String qrcodeUrl;

    @ApiModelProperty(value = "审核时间")
    private Date updateTime;

    @ApiModelProperty(value = "审核状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "处理结果")
    private String failMsg;

}
