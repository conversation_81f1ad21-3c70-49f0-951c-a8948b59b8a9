package com.zbkj.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户分组表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RoomLikeRequest", description = "房间点赞")
public class RoomLikeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间ID")
    @NotNull(message = "房间ID不能为空")
    private Long roomId;

    @ApiModelProperty(value = "连续点赞次数")
    @NotNull(message = "点赞次数不能为空")
    @Min(value = 1, message = "点赞次数不可低于1次")
    private Integer count;

}
