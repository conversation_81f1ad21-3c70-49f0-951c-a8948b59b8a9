package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BroBroadcastingStatus implements IEnum<Integer> {
    STARTED("已开始", 1),
    ENDED("已结束", 2);

    private final String type;

    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }
}
