package com.zbkj.common.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 通用工具类

 */
public class CommonUtil {

    /**
     * 随机生成密码
     *
     * @param phone 手机号
     * @return 密码
     * 使用des方式加密
     */
    public static String createPwd(String phone) {
        String password = "Abc" + CrmebUtil.randomCount(10000, 99999);
        return CrmebUtil.encryptPassword(password, phone);
    }

    /**
     * 随机生成用户昵称
     *
     * @param phone 手机号
     * @return 昵称
     */
    public static String createNickName(String phone) {
        return DigestUtils.md5Hex(phone + DateUtil.getNowTime()).
                subSequence(0, 12).
                toString();
    }

    public static boolean isNumeric(String str) {
        // 使用正则表达式判断是否全是数字
        // \\d 表示数字，+ 表示一个或多个
        return Pattern.matches("\\d+", str);
    }

    /**
     * 检测是否包含字符
     * @param username
     * @return
     */
    public static boolean isUsernameValid(String username) {
        // 正则表达式，要求用户名包含至少一个字母
        String regex = ".*[a-zA-Z]+.*";
        // 编译正则表达式
        return Pattern.matches(regex,username);
    }

    /**
     * 排序
     * @param data
     * @return
     */
    public static String generateSignature(Map<String, Object> data) {
        // TreeMap 用于对 Map 中的键进行排序
        TreeMap<String, Object> sortedData = new TreeMap<>(data);
        // 拼接字符串
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedData.entrySet()) {
            String key = entry.getKey();
            String value = StringUtils.isEmpty(entry.getValue()) ? "" : String.valueOf(entry.getValue());
            signStr.append(key).append("=").append(value).append("&");
        }
        // 去掉最后一个&字符
        signStr.deleteCharAt(signStr.length() - 1);
        return signStr.toString();
    }

    /**
     * 生成签名字符串
     * 该方法将过滤掉 `paraMap` 中值为 `null` 或空字符串的条目，并将剩余的条目拼接成 `key=value` 的格式，最后用 `&` 分隔。
     *
     * @param paraMap 待签名的参数 Map，包含 key-value 对
     * @return 拼接后的签名字符串，格式为 "key1=value1&key2=value2&..."
     */
    public static String getSignStr(Map<String, Object> paraMap) {
        return paraMap.entrySet().stream()  // 将 Map 转换为流，处理每个条目
                // 过滤掉值为 null 的条目
                .filter(entry -> Objects.nonNull(entry.getValue()))
                // 过滤掉值为空字符串的条目
                .filter(entry -> !entry.getValue().toString().isEmpty())
                // 将每个条目的 key 和 value 拼接成 "key=value" 的格式
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                // 使用 Collectors.joining 拼接所有 "key=value" 字符串，使用 "&" 作为分隔符
                .collect(Collectors.joining("&"));
    }

    public static HashMap<String, Object> getRandomOfList(List<HashMap<String, Object>> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        double totalProbability = 0.0;
        // 计算总几率
        for (HashMap<String, Object> map : list) {
            Object object = map.get("jikaProbabilidad");
            if (object != null && !StringUtils.isEmpty(object.toString())) {
                double parsed = Double.parseDouble(object.toString());
                if (parsed <= 0){
                    continue;
                }
                totalProbability += parsed;
            }
        }
        // 生成一个随机数，用于确定抽中的几率区间
        Random random = new Random();
        double randomValue = random.nextDouble() * totalProbability;

        double currentProbability = 0.0;

        // 遍历列表，确定随机数所在的几率区间
        for (HashMap<String, Object> map : list) {
            Object object = map.get("jikaProbabilidad");
            if (object != null && !StringUtils.isEmpty(object.toString())) {
                double probability = Double.parseDouble(object.toString());
                if (probability <= 0 ){
                    continue;
                }
                currentProbability += probability;

                if (randomValue <= currentProbability) {
                    return map; // 返回抽中的对象
                }
            }
        }

        return null; // 如果所有几率之和为0，或者随机数不在任何几率区间内，返回null
    }

    /**
     *
     * @param list 列表
     * @param probabilidad 几率字段名
     * @param clazz 实体对象
     * @return
     * @param <T>
     */
    public static  <T> T getRandomObjectFromList(List<T> list, String probabilidad, Class<T> clazz) {
        if (list == null || list.isEmpty()) {
            return null;
        }

        double totalProbability = 0.0;

        // 计算总几率
        for (T item : list) {
            try {
                Method method = clazz.getMethod("get" + StringUtils.capitalize(probabilidad));
                Object object = method.invoke(item);
                if (object != null && !StringUtils.isEmpty(object.toString())) {
                    double parsed = Double.parseDouble(object.toString());
                    if (parsed <= 0){
                        continue;
                    }
                    totalProbability += parsed;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 生成一个随机数，用于确定抽中的几率区间
        Random random = new Random();
        double randomValue = random.nextDouble() * totalProbability;
        double currentProbability = 0.0;

        // 遍历列表，确定随机数所在的几率区间
        for (T item : list) {
            try {
                Method method = clazz.getMethod("get" + StringUtils.capitalize(probabilidad));
                Object object = method.invoke(item);
                if (object != null && !StringUtils.isEmpty(object.toString())) {
                    double probability = Double.parseDouble(object.toString());
                    if (probability <= 0){
                        continue;
                    }
                    currentProbability += probability;

                    if (randomValue <= currentProbability) {
                        return item; // 返回抽中的对象
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return null; // 如果所有几率之和为0，或者随机数不在任何几率区间内，返回null
    }


    // 生成随机金额的方法
    public static BigDecimal generateRandomAmount(BigDecimal minAmount, BigDecimal maxAmount) {
        // 创建一个Random对象
        Random random = new Random();
        // 生成随机BigDecimal类型的金额
        BigDecimal range = maxAmount.subtract(minAmount);
        BigDecimal scaled = range.multiply(new BigDecimal(random.nextDouble()));
        BigDecimal randomAmount = scaled.add(minAmount);
        // 使用setScale方法设置小数精度
        randomAmount = randomAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        return randomAmount;
    }

    public static String md5(String text,Integer num){
        for (int i = 0; i < num; i++) {
            text =  DigestUtils.md5Hex(text);
        }
        return text;
    }

}
