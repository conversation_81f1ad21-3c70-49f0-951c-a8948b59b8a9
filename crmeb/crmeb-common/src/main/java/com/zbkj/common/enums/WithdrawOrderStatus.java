package com.zbkj.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WithdrawOrderStatus implements IEnum<Integer> {
    PENDING(0, "审核中(待审核)"),
    WITHDRAWN(1, "已提现(手动审核通过)"),
    REJECTED(-1, "已拒绝(手动审核未通过)"),
    PAYING(2, "代付中"),
    SUCCESS(3, "代付成功(回调通过)"),
    FAILED(4, "代付失败");

    private final int value;
    private final String description;

    @Override
    public Integer getValue() {
        return value;
    }
}