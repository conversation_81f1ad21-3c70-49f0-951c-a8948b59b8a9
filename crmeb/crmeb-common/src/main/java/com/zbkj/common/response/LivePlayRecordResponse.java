package com.zbkj.common.response;

import com.zbkj.common.model.live.LiveRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LivePlayRecordResponse {

    @ApiModelProperty(value = "当前直播间roomId")
    private Long roomId;

    @ApiModelProperty(value = "上一场直播记录")
    private LiveRecord previous;

    @ApiModelProperty(value = "下一场直播记录")
    private LiveRecord next;

}
