package com.zbkj.common.request;

import com.zbkj.common.enums.CertificateStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 用户绑卡审核记录
 */
@Data
@ApiModel(value = "UserBankCardsRecordReviewRequest", description = "用户绑卡审核记录审核对象")
public class UserBankCardsRecordReviewRequest extends PageParamRequest {


    @ApiModelProperty(value = "id")
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value = "状态")
    @NotNull(message = "状态不能为空")
    private CertificateStatus status;

    @ApiModelProperty(value = "备注")
    private String remark;

}
