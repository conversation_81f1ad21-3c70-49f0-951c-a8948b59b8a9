# 新增OkPay支付表单配置、配置分类、系统设置。
INSERT INTO eb_system_form_temp (name, info, content)
VALUES ('OkPay', 'OkPay',
        '{"formRef":"elForm","formModel":"formData","size":"medium","labelPosition":"right","labelWidth":120,"formRules":"rules","gutter":15,"disabled":false,"span":24,"formBtns":true,"fields":[{"__config__":{"label":"商户号","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":101,"renderKey":1715835658426},"__slot__":{"prepend":"","append":""},"placeholder":"请输入商户号","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okMerchantId"},{"__config__":{"label":"回调地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":104,"renderKey":1715835767922},"__slot__":{"prepend":"","append":""},"placeholder":"请输入回调地址","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okCallBackUrl"},{"__config__":{"label":"秘钥","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":105,"renderKey":1715835796363},"__slot__":{"prepend":"","append":""},"placeholder":"请输入秘钥","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okKey"},{"__config__":{"label":"网关地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":106,"renderKey":1715835816779},"__slot__":{"prepend":"","append":""},"placeholder":"请输入网关地址","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okUrl"},{"__config__":{"label":"图标","tag":"self-upload","tagIcon":"selfUpload","layout":"colFormItem","defaultValue":null,"showLabel":true,"labelWidth":null,"required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","span":24,"showTip":false,"buttonText":"","regList":[],"changeTag":true,"document":"https://element.eleme.cn/#/zh-CN/component/upload","formId":108,"renderKey":1715835845186},"__slot__":{"list-type":true},"disabled":true,"accept":"image","name":"file","multiple":false,"__vModel__":"okLogo"},{"__config__":{"label":"状态","labelWidth":null,"showLabel":true,"tag":"el-radio-group","tagIcon":"radio","changeTag":true,"layout":"colFormItem","span":24,"optionType":"default","regList":[],"required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","border":false,"document":"https://element.eleme.cn/#/zh-CN/component/radio","formId":109,"renderKey":1715835857498,"defaultValue":0},"__slot__":{"options":[{"label":"开启","value":1},{"label":"关闭","value":0}]},"style":{},"size":"medium","disabled":false,"__vModel__":"okStatus"}]}');

-- 获取刚插入记录的自增主键ID
SET @last_id = LAST_INSERT_ID();
INSERT INTO eb_category (pid, path, name, type, url, extra, status, sort,
                         merchant, points_deducted)
VALUES (103, '/0/103/', 'OkPay', 6, 'OkPay', @last_id, 1, 1, 0, FALSE);

INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okStatus', 'okStatus', @last_id, '0', 0);
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okLogo', 'okLogo', @last_id,
        'https://d25pa5dldl22p8.cloudfront.net/9d5f9393-a0b5-4c77-999a-e3ed3930652d.jpg', 0);
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okUrl', 'okUrl', @last_id, 'https://shcshf02.ok365sh2.com', 0);
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okKey', 'okKey', @last_id, 'ac7975e4efdb4422bcf92fa1e0e2ab9b', 0);
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okMerchantId', 'okMerchantId', @last_id, '05e67850-35bb-4863-97b8-37da7f406d78', 0);
# 正式环境回调地址
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okCallBackUrl', 'okCallBackUrl', @last_id,
        'https://adm.xbladm.com/api/admin/payment/callback/okPay', 0);


# SELECT *
# FROM eb_system_form_temp
# WHERE name LIKE '%okpay%';
#
# SELECT *
# FROM eb_category
# WHERE name LIKE '%okPay%';
#
# SELECT *
# FROM eb_system_config
# WHERE name IN ('okStatus', 'okLogo', 'okUrl', 'okKey', 'okMerchantId', 'okCallBackUrl')