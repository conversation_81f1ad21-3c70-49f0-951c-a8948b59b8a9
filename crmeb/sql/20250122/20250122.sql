# to代付
UPDATE eb_system_form_temp
SET content ='{"formRef":"elForm","formModel":"formData","size":"medium","labelPosition":"right","labelWidth":120,"formRules":"rules","gutter":15,"disabled":false,"span":24,"formBtns":true,"fields":[{"__config__":{"label":"商户号","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":101,"renderKey":1715835658426},"__slot__":{"prepend":"","append":""},"placeholder":"请输入商户号","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"toMerchantId"},{"__config__":{"label":"回调地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":104,"renderKey":1715835767922},"__slot__":{"prepend":"","append":""},"placeholder":"请输入回调地址","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"toCallBackUrl"},{"__config__":{"label":"代付回调地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"tips":false,"formId":102,"renderKey":1735299643065},"__slot__":{"prepend":"","append":""},"__vModel__":"toAgentCallBackUrl","placeholder":"请输入代付回调地址","style":{"width":"100%"},"clearable":true,"prefix-icon":"el-icon-mobile","suffix-icon":"","maxlength":"","show-word-limit":false,"readonly":false,"disabled":false},{"__config__":{"label":"秘钥","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":105,"renderKey":1715835796363},"__slot__":{"prepend":"","append":""},"placeholder":"请输入秘钥","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"toKey"},{"__config__":{"label":"网关地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":106,"renderKey":1715835816779},"__slot__":{"prepend":"","append":""},"placeholder":"请输入网关地址","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"toUrl"},{"__config__":{"label":"图标","tag":"self-upload","tagIcon":"selfUpload","layout":"colFormItem","defaultValue":null,"showLabel":true,"labelWidth":null,"required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","span":24,"showTip":false,"buttonText":"","regList":[],"changeTag":true,"document":"https://element.eleme.cn/#/zh-CN/component/upload","formId":108,"renderKey":1715835845186},"__slot__":{"list-type":true},"disabled":true,"accept":"image","name":"file","multiple":false,"__vModel__":"toLogo"},{"__config__":{"label":"状态","labelWidth":null,"showLabel":true,"tag":"el-radio-group","tagIcon":"radio","changeTag":true,"layout":"colFormItem","span":24,"optionType":"default","regList":[],"required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","border":false,"document":"https://element.eleme.cn/#/zh-CN/component/radio","formId":109,"renderKey":1715835857498,"defaultValue":0},"__slot__":{"options":[{"label":"开启","value":1},{"label":"关闭","value":0}]},"style":{},"size":"medium","disabled":false,"__vModel__":"toStatus"}]}'
WHERE name = 'topay';

# 取得to代付的form_id
SET @to_form_id = (SELECT form_id
                FROM eb_system_config
                WHERE name = 'toUrl');

# 鑫百利正式环境回调地址
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('toAgentCallBackUrl', 'toAgentCallBackUrl', @to_form_id,
        'https://adm.xbladm.com/api/admin/payment/callback/toAgentPay', 0);


# ========================================================================================================================
# ok代付
UPDATE eb_system_form_temp
SET content ='{"formRef":"elForm","formModel":"formData","size":"medium","labelPosition":"right","labelWidth":120,"formRules":"rules","gutter":15,"disabled":false,"span":24,"formBtns":true,"fields":[{"__config__":{"label":"商户号","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":101,"renderKey":1715835658426},"__slot__":{"prepend":"","append":""},"placeholder":"请输入商户号","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okMerchantId"},{"__config__":{"label":"回调地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":104,"renderKey":1715835767922},"__slot__":{"prepend":"","append":""},"placeholder":"请输入回调地址","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okCallBackUrl"},{"__config__":{"label":"代付回调地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"tips":false,"formId":102,"renderKey":1735299643065},"__slot__":{"prepend":"","append":""},"__vModel__":"okAgentCallBackUrl","placeholder":"请输入代付回调地址","style":{"width":"100%"},"clearable":true,"prefix-icon":"el-icon-mobile","suffix-icon":"","maxlength":"","show-word-limit":false,"readonly":false,"disabled":false},{"__config__":{"label":"秘钥","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":105,"renderKey":1715835796363},"__slot__":{"prepend":"","append":""},"placeholder":"请输入秘钥","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okKey"},{"__config__":{"label":"网关地址","labelWidth":null,"showLabel":true,"changeTag":true,"tag":"el-input","tagIcon":"input","required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","layout":"colFormItem","span":24,"document":"https://element.eleme.cn/#/zh-CN/component/input","regList":[],"formId":106,"renderKey":1715835816779},"__slot__":{"prepend":"","append":""},"placeholder":"请输入网关地址","style":{"width":"95%"},"clearable":true,"prefix-icon":"","suffix-icon":"","maxlength":null,"show-word-limit":false,"readonly":false,"disabled":false,"__vModel__":"okUrl"},{"__config__":{"label":"图标","tag":"self-upload","tagIcon":"selfUpload","layout":"colFormItem","defaultValue":null,"showLabel":true,"labelWidth":null,"required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","span":24,"showTip":false,"buttonText":"","regList":[],"changeTag":true,"document":"https://element.eleme.cn/#/zh-CN/component/upload","formId":108,"renderKey":1715835845186},"__slot__":{"list-type":true},"disabled":true,"accept":"image","name":"file","multiple":false,"__vModel__":"okLogo"},{"__config__":{"label":"状态","labelWidth":null,"showLabel":true,"tag":"el-radio-group","tagIcon":"radio","changeTag":true,"layout":"colFormItem","span":24,"optionType":"default","regList":[],"required":true,"tips":false,"tipsDesc":"","tipsIsLink":false,"tipsLink":"","border":false,"document":"https://element.eleme.cn/#/zh-CN/component/radio","formId":109,"renderKey":1715835857498,"defaultValue":0},"__slot__":{"options":[{"label":"开启","value":1},{"label":"关闭","value":0}]},"style":{},"size":"medium","disabled":false,"__vModel__":"okStatus"}]}'
WHERE name = 'okpay';


# 取得ok代付的form_id
SET @ok_form_id = (SELECT form_id
                FROM eb_system_config
                WHERE name = 'okUrl');

# 鑫百利正式环境回调地址
INSERT INTO eb_system_config (name, title, form_id, value, status)
VALUES ('okAgentCallBackUrl', 'okAgentCallBackUrl', @ok_form_id,
        'https://adm.xbladm.com/api/admin/payment/callback/okAgentPay', 0);