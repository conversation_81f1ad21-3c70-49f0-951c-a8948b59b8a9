# 剩余需要修改的文件

## 需要修改的调用方式

将所有的：
```java
userLockService.executeWithUserLock(userId, () -> {
```

改为：
```java
userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + userId, () -> {
```

## 剩余文件清单：

1. **OrderPayServiceImpl.java**
   - `userLockService.executeWithUserLock(storeOrder.getUid(), () -> {`
   - 改为：`userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + storeOrder.getUid(), () -> {`

2. **UserExtractServiceImpl.java** (4处)
   - `userLockService.executeWithUserLock(userExtract.getUid(), () -> {`
   - `userLockService.executeWithUserLock(user.getUid(), () -> {`
   - 改为：`userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + userExtract.getUid(), () -> {`
   - 改为：`userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + user.getUid(), () -> {`

3. **InvestItemsOrderServiceImpl.java**
   - `userLockService.executeWithUserLock(userId, () -> {`
   - 改为：`userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + userId, () -> {`

4. **UserAuthServiceImpl.java**
   - `userLockService.executeWithUserLock(user.getUid(), () -> {`
   - 改为：`userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + user.getUid(), () -> {`

## 优势总结

修改后的优势：
1. **更通用**：`executeWithLock` 可以用于任何类型的锁
2. **更明确**：锁键完全由调用方控制，一目了然
3. **更灵活**：可以轻松扩展到其他业务锁
4. **更优雅**：统一的锁服务接口

## 示例对比

### 修改前：
```java
return userLockService.executeWithUserLock(userAuth.getUserId(), () -> {
    // 业务逻辑
    return result;
});
```

### 修改后：
```java
return userLockService.executeWithLock(Constants.USER_LOCK_PREFIX + userAuth.getUserId(), () -> {
    // 业务逻辑  
    return result;
});
```

这样一眼就能看出使用的是 `user_lock_{userId}` 格式的锁！
