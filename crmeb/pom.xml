<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>crmeb-common</module>
        <module>crmeb-service</module>
        <module>crmeb-admin</module>
        <module>crmeb-front</module>
    </modules>
    <groupId>com.zbkj</groupId>
    <artifactId>crmeb</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>crmeb</name>
    <description>Crmeb project for Spring Boot</description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <springfox.version>2.9.2</springfox.version>
        <spring-boot.version>2.3.12.RELEASE</spring-boot.version>
        <swagger-models.version>1.5.22</swagger-models.version>
        <swagger-bootstrap-ui.version>1.9.3</swagger-bootstrap-ui.version>
        <!--        兼容个别 mav 环境 GBK问题-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-assembly-plugin.version>3.6.0</maven-assembly-plugin.version>
        <appassembler-maven-plugin.version>2.1.0</appassembler-maven-plugin.version>
        <mainClass><!--子模块填写--></mainClass>
        <debugPort><!--子模块填写--></debugPort>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>

            <!--添加 Alibaba 数据源-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.22</version>
            </dependency>

            <!--访问mysql-->
            <!--JDBC-->
            <!-- MySql 5.5 Connector -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.0.33</version>
            </dependency>

            <!--代码自动生成工具-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.4.3</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>5.2.5.RELEASE</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>3.4.3</version>
            </dependency>

            <!--generator-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.4.1</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.21</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.21</version>
            </dependency>

            <!-- doc -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger-models.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger-bootstrap-ui.version}</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>5.3</version>
            </dependency>

            <!-- Spring Boot Redis 依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.1.0</version>
            </dependency>

            <!-- 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.5</version>
                <!--            mybatis plus 和 pagehelper 冲突-->
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>4.5.7</version>
            </dependency>

            <!--httpclient-->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.6</version>
            </dependency>

            <!--字符串操作-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.5</version>
            </dependency>

            <!--导出excel-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>

            <!-- Apache Commons FileUpload -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.3.3</version>
            </dependency>
            <!-- Apache Commons IO  -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.4</version>
            </dependency>
            <!-- thumbnailator 图片压缩工具 -->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>0.4.8</version>
            </dependency>

            <!--阿里云oss上传-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.5.0</version>
            </dependency>

            <!--腾讯云COS-->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>5.6.22</version>
            </dependency>

            <!-- 七牛云 -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>7.2.28</version>
            </dependency>

            <!--xml-->
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>1.6.1</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>


            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-core</artifactId>
                <version>3.8.2</version>
            </dependency>
            <dependency>
                <groupId>com.vaadin.external.google</groupId>
                <artifactId>android-json</artifactId>
                <version>0.0.20131108.vaadin1</version>
                <scope>compile</scope>
            </dependency>
            <!--图片上传-->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.5.2</version>
            </dependency>

            <!--谷歌二维码-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.3.3</version>
            </dependency>

            <!--中文转拼音-->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.0</version>
            </dependency>

            <!--解析JWT-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.9.1</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>jwks-rsa</artifactId>
                <version>0.9.0</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.xdb</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>META-INF/**</include>
                    <include>templates/**</include>
                    <include>ipData/**</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                    <include>**/*.db</include>
                    <include>**/*.xdb</include>
                    <include>**/*.tpl</include>
                    <include>**/*.ftl</include>
                    <include>**/banner.txt</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>2048m</Xmx>
                <Xms>2048m</Xms>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
<!--                        <plugin>-->
<!--                            <groupId>org.codehaus.mojo</groupId>-->
<!--                            <artifactId>appassembler-maven-plugin</artifactId>-->
<!--                            <version>${appassembler-maven-plugin.version}</version>-->
<!--                            <executions>-->
<!--                                <execution>-->
<!--                                    <phase>package</phase>-->
<!--                                    <goals>-->
<!--                                        <goal>generate-daemons</goal>-->
<!--                                    </goals>-->
<!--                                </execution>-->
<!--                            </executions>-->
<!--                            <configuration>-->
<!--                                <configurationDirectory>conf</configurationDirectory>-->
<!--                                <includeConfigurationDirectoryInClasspath>true-->
<!--                                </includeConfigurationDirectoryInClasspath>-->
<!--                                <repositoryLayout>flat</repositoryLayout>-->
<!--                                <useWildcardClassPath>true</useWildcardClassPath>-->
<!--                                <daemons>-->
<!--                                    <daemon>-->
<!--                                        <id>${project.artifactId}</id>-->
<!--                                        <mainClass>${mainClass}</mainClass>-->
<!--                                        <platforms>-->
<!--                                            <platform>jsw</platform>-->
<!--                                        </platforms>-->
<!--                                        <generatorConfigurations>-->
<!--                                            <generatorConfiguration>-->
<!--                                                <generator>jsw</generator>-->
<!--                                                <includes>-->
<!--                                                    <include>linux-x86-32</include>-->
<!--                                                    <include>linux-x86-64</include>-->
<!--                                                    <include>linux-ppc-64</include>-->
<!--                                                </includes>-->
<!--                                                <configuration>-->
<!--                                                    <property>-->
<!--                                                        <name>configuration.directory.in.classpath.first</name>-->
<!--                                                        <value>conf</value>-->
<!--                                                    </property>-->
<!--                                                    <property>-->
<!--                                                        <name>set.default.REPO_DIR</name>-->
<!--                                                        <value>lib</value>-->
<!--                                                    </property>-->
<!--                                                </configuration>-->
<!--                                            </generatorConfiguration>-->
<!--                                        </generatorConfigurations>-->
<!--                                        <jvmSettings>-->
<!--                                            <extraArguments>-->
<!--                                                <extraArgument>-Xmx${Xmx}</extraArgument>&lt;!&ndash;最大堆内存&ndash;&gt;-->
<!--                                                <extraArgument>-Xms${Xms}</extraArgument>&lt;!&ndash;初始堆内存&ndash;&gt;-->
<!--                                                &lt;!&ndash;<extraArgument>-XX:+UseG1GC</extraArgument>&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+UseG1GC</extraArgument>-->
<!--                                                <extraArgument>-XX:SoftRefLRUPolicyMSPerMB=50</extraArgument>-->
<!--                                                <extraArgument>-XX:+UseCompressedOops</extraArgument>&lt;!&ndash;普通对象指针压缩&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+UseCompressedClassPointers-->
<!--                                                </extraArgument>&lt;!&ndash;类指针压缩&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+SegmentedCodeCache</extraArgument>-->
<!--                                                <extraArgument>-XX:ReservedCodeCacheSize=256m</extraArgument>-->
<!--                                                <extraArgument>-XX:InitialCodeCacheSize=256m</extraArgument>-->
<!--                                                <extraArgument>-XX:+PrintCommandLineFlags-->
<!--                                                </extraArgument>&lt;!&ndash;打印那些被新值覆盖的项&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+ExitOnOutOfMemoryError</extraArgument>&lt;!&ndash;内存溢出时退出&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+ExplicitGCInvokesConcurrent</extraArgument>-->
<!--                                                <extraArgument>-XX:MetaspaceSize=${MetaspaceSize}-->
<!--                                                </extraArgument>&lt;!&ndash;初始元空间大小&ndash;&gt;-->
<!--                                                <extraArgument>-XX:MaxMetaspaceSize=${MaxMetaspaceSize}-->
<!--                                                </extraArgument>&lt;!&ndash;最大元空间大小&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+HeapDumpOnOutOfMemoryError-->
<!--                                                </extraArgument> &lt;!&ndash;通过将堆的内容转储到 java_pid.hprof 文件来帮助诊断内存泄漏 &ndash;&gt;-->
<!--                                                <extraArgument>-XX:HeapDumpPath=logs/oom_dump.log</extraArgument>-->
<!--                                                <extraArgument>-XX:ErrorFile=logs/java_error.log</extraArgument>-->
<!--                                                <extraArgument>-Dsun.io.useCanonPrefixCache=false</extraArgument>-->
<!--                                                <extraArgument>-Dfile.encoding=UTF-8</extraArgument>-->
<!--                                                <extraArgument>-Djava.security.egd=file:/dev/./urandom</extraArgument>-->
<!--                                                &lt;!&ndash;<extraArgument>-->
<!--                                                    -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=logs/gc-%t.log:time,tid,tags:filecount=5,filesize=50m  &lt;!&ndash;GC日志输出配置 &ndash;&gt;-->
<!--                                                </extraArgument>&ndash;&gt;-->
<!--                                                <extraArgument>-->
<!--                                                    -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:${debugPort}-->
<!--                                                </extraArgument>-->
<!--                                            </extraArguments>-->
<!--                                        </jvmSettings>-->
<!--                                    </daemon>-->
<!--                                </daemons>-->
<!--                            </configuration>-->
<!--                        </plugin>-->
<!--                        <plugin>-->
<!--                            <groupId>org.apache.maven.plugins</groupId>-->
<!--                            <artifactId>maven-assembly-plugin</artifactId>-->
<!--                            <version>${maven-assembly-plugin.version}</version>-->
<!--                            <configuration>-->
<!--                                <attach>false</attach>-->
<!--                                <appendAssemblyId>false</appendAssemblyId>-->
<!--                                <finalName>${project.artifactId}</finalName>-->
<!--                                <descriptors>-->
<!--                                    <descriptor>${project.basedir}/src/main/assembly/assembly-linux.xml</descriptor>-->
<!--                                </descriptors>-->
<!--                                &lt;!&ndash; 解决maven打包文件名超过100字符的问题 longer than 100 characters &ndash;&gt;-->
<!--                                <tarLongFileMode>gnu</tarLongFileMode>-->
<!--                            </configuration>-->
<!--                            <executions>-->
<!--                                <execution>-->
<!--                                    <id>make-assembly</id>-->
<!--                                    <phase>package</phase>-->
<!--                                    <goals>-->
<!--                                        <goal>single</goal>-->
<!--                                    </goals>-->
<!--                                </execution>-->
<!--                            </executions>-->
<!--                        </plugin>-->
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>2048m</Xmx>
                <Xms>2048m</Xms>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
<!--                        <plugin>-->
<!--                            <groupId>org.codehaus.mojo</groupId>-->
<!--                            <artifactId>appassembler-maven-plugin</artifactId>-->
<!--                            <version>${appassembler-maven-plugin.version}</version>-->
<!--                            <executions>-->
<!--                                <execution>-->
<!--                                    <phase>package</phase>-->
<!--                                    <goals>-->
<!--                                        <goal>generate-daemons</goal>-->
<!--                                    </goals>-->
<!--                                </execution>-->
<!--                            </executions>-->
<!--                            <configuration>-->
<!--                                <configurationDirectory>conf</configurationDirectory>-->
<!--                                <includeConfigurationDirectoryInClasspath>true-->
<!--                                </includeConfigurationDirectoryInClasspath>-->
<!--                                <repositoryLayout>flat</repositoryLayout>-->
<!--                                <useWildcardClassPath>true</useWildcardClassPath>-->
<!--                                <daemons>-->
<!--                                    <daemon>-->
<!--                                        <id>${project.artifactId}</id>-->
<!--                                        <mainClass>${mainClass}</mainClass>-->
<!--                                        <platforms>-->
<!--                                            <platform>jsw</platform>-->
<!--                                        </platforms>-->
<!--                                        <generatorConfigurations>-->
<!--                                            <generatorConfiguration>-->
<!--                                                <generator>jsw</generator>-->
<!--                                                <includes>-->
<!--                                                    <include>linux-x86-32</include>-->
<!--                                                    <include>linux-x86-64</include>-->
<!--                                                    <include>linux-ppc-64</include>-->
<!--                                                </includes>-->
<!--                                                <configuration>-->
<!--                                                    <property>-->
<!--                                                        <name>configuration.directory.in.classpath.first</name>-->
<!--                                                        <value>conf</value>-->
<!--                                                    </property>-->
<!--                                                    <property>-->
<!--                                                        <name>set.default.REPO_DIR</name>-->
<!--                                                        <value>lib</value>-->
<!--                                                    </property>-->
<!--                                                </configuration>-->
<!--                                            </generatorConfiguration>-->
<!--                                        </generatorConfigurations>-->
<!--                                        <jvmSettings>-->
<!--                                            <extraArguments>-->
<!--                                                <extraArgument>-Xmx${Xmx}</extraArgument>&lt;!&ndash;最大堆内存&ndash;&gt;-->
<!--                                                <extraArgument>-Xms${Xms}</extraArgument>&lt;!&ndash;初始堆内存&ndash;&gt;-->
<!--                                                &lt;!&ndash;<extraArgument>-XX:+UseG1GC</extraArgument>&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+UseG1GC</extraArgument>-->
<!--                                                <extraArgument>-XX:SoftRefLRUPolicyMSPerMB=50</extraArgument>-->
<!--                                                <extraArgument>-XX:+UseCompressedOops</extraArgument>&lt;!&ndash;普通对象指针压缩&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+UseCompressedClassPointers-->
<!--                                                </extraArgument>&lt;!&ndash;类指针压缩&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+SegmentedCodeCache</extraArgument>-->
<!--                                                <extraArgument>-XX:ReservedCodeCacheSize=256m</extraArgument>-->
<!--                                                <extraArgument>-XX:InitialCodeCacheSize=256m</extraArgument>-->
<!--                                                <extraArgument>-XX:+PrintCommandLineFlags-->
<!--                                                </extraArgument>&lt;!&ndash;打印那些被新值覆盖的项&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+ExitOnOutOfMemoryError</extraArgument>&lt;!&ndash;内存溢出时退出&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+ExplicitGCInvokesConcurrent</extraArgument>-->
<!--                                                <extraArgument>-XX:MetaspaceSize=${MetaspaceSize}-->
<!--                                                </extraArgument>&lt;!&ndash;初始元空间大小&ndash;&gt;-->
<!--                                                <extraArgument>-XX:MaxMetaspaceSize=${MaxMetaspaceSize}-->
<!--                                                </extraArgument>&lt;!&ndash;最大元空间大小&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+HeapDumpOnOutOfMemoryError-->
<!--                                                </extraArgument> &lt;!&ndash;通过将堆的内容转储到 java_pid.hprof 文件来帮助诊断内存泄漏 &ndash;&gt;-->
<!--                                                <extraArgument>-XX:HeapDumpPath=logs/oom_dump.log</extraArgument>-->
<!--                                                <extraArgument>-XX:ErrorFile=logs/java_error.log</extraArgument>-->
<!--                                                <extraArgument>-Dsun.io.useCanonPrefixCache=false</extraArgument>-->
<!--                                                <extraArgument>-Dfile.encoding=UTF-8</extraArgument>-->
<!--                                                <extraArgument>-Djava.security.egd=file:/dev/./urandom</extraArgument>-->
<!--                                                &lt;!&ndash;<extraArgument>-->
<!--                                                    -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=logs/gc-%t.log:time,tid,tags:filecount=5,filesize=50m  &lt;!&ndash;GC日志输出配置 &ndash;&gt;-->
<!--                                                </extraArgument>&ndash;&gt;-->
<!--                                                <extraArgument>-->
<!--                                                    -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:${debugPort}-->
<!--                                                </extraArgument>-->
<!--                                            </extraArguments>-->
<!--                                        </jvmSettings>-->
<!--                                    </daemon>-->
<!--                                </daemons>-->
<!--                            </configuration>-->
<!--                        </plugin>-->
<!--                        <plugin>-->
<!--                            <groupId>org.apache.maven.plugins</groupId>-->
<!--                            <artifactId>maven-assembly-plugin</artifactId>-->
<!--                            <version>${maven-assembly-plugin.version}</version>-->
<!--                            <configuration>-->
<!--                                <attach>false</attach>-->
<!--                                <appendAssemblyId>false</appendAssemblyId>-->
<!--                                <finalName>${project.artifactId}</finalName>-->
<!--                                <descriptors>-->
<!--                                    <descriptor>${project.basedir}/src/main/assembly/assembly-linux.xml</descriptor>-->
<!--                                </descriptors>-->
<!--                                &lt;!&ndash; 解决maven打包文件名超过100字符的问题 longer than 100 characters &ndash;&gt;-->
<!--                                <tarLongFileMode>gnu</tarLongFileMode>-->
<!--                            </configuration>-->
<!--                            <executions>-->
<!--                                <execution>-->
<!--                                    <id>make-assembly</id>-->
<!--                                    <phase>package</phase>-->
<!--                                    <goals>-->
<!--                                        <goal>single</goal>-->
<!--                                    </goals>-->
<!--                                </execution>-->
<!--                            </executions>-->
<!--                        </plugin>-->
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <properties> <!-- 环境标识，需要与配置文件的名称相对应 -->
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>4096m</Xmx>
                <Xms>4096m</Xms>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
<!--                        <plugin>-->
<!--                            <groupId>org.codehaus.mojo</groupId>-->
<!--                            <artifactId>appassembler-maven-plugin</artifactId>-->
<!--                            <version>${appassembler-maven-plugin.version}</version>-->
<!--                            <executions>-->
<!--                                <execution>-->
<!--                                    <phase>package</phase>-->
<!--                                    <goals>-->
<!--                                        <goal>generate-daemons</goal>-->
<!--                                    </goals>-->
<!--                                </execution>-->
<!--                            </executions>-->
<!--                            <configuration>-->
<!--                                <configurationDirectory>conf</configurationDirectory>-->
<!--                                <includeConfigurationDirectoryInClasspath>true-->
<!--                                </includeConfigurationDirectoryInClasspath>-->
<!--                                <repositoryLayout>flat</repositoryLayout>-->
<!--                                <useWildcardClassPath>true</useWildcardClassPath>-->
<!--                                <daemons>-->
<!--                                    <daemon>-->
<!--                                        <id>${project.artifactId}</id>-->
<!--                                        <mainClass>${mainClass}</mainClass>-->
<!--                                        <platforms>-->
<!--                                            <platform>jsw</platform>-->
<!--                                        </platforms>-->
<!--                                        <generatorConfigurations>-->
<!--                                            <generatorConfiguration>-->
<!--                                                <generator>jsw</generator>-->
<!--                                                <includes>-->
<!--                                                    <include>linux-x86-32</include>-->
<!--                                                    <include>linux-x86-64</include>-->
<!--                                                    <include>linux-ppc-64</include>-->
<!--                                                </includes>-->
<!--                                                <configuration>-->
<!--                                                    <property>-->
<!--                                                        <name>configuration.directory.in.classpath.first</name>-->
<!--                                                        <value>conf</value>-->
<!--                                                    </property>-->
<!--                                                    <property>-->
<!--                                                        <name>set.default.REPO_DIR</name>-->
<!--                                                        <value>lib</value>-->
<!--                                                    </property>-->
<!--                                                </configuration>-->
<!--                                            </generatorConfiguration>-->
<!--                                        </generatorConfigurations>-->
<!--                                        <jvmSettings>-->
<!--                                            <extraArguments>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.base/java.lang=ALL-UNNAMED-->
<!--                                                </extraArgument>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.base/java.math=ALL-UNNAMED-->
<!--                                                </extraArgument>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.base/java.util=ALL-UNNAMED-->
<!--                                                </extraArgument>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.base/java.util.concurrent=ALL-UNNAMED-->
<!--                                                </extraArgument>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.base/java.net=ALL-UNNAMED-->
<!--                                                </extraArgument>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.base/java.text=ALL-UNNAMED-->
<!--                                                </extraArgument>-->
<!--                                                <extraArgument>&#45;&#45;add-opens=java.sql/java.sql=ALL-UNNAMED</extraArgument>-->
<!--                                                <extraArgument>-Xmx${Xmx}</extraArgument>&lt;!&ndash;最大堆内存&ndash;&gt;-->
<!--                                                <extraArgument>-Xms${Xms}</extraArgument>&lt;!&ndash;初始堆内存&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+UseG1GC</extraArgument>-->
<!--                                                <extraArgument>-XX:SoftRefLRUPolicyMSPerMB=50</extraArgument>-->
<!--                                                <extraArgument>-XX:+UseCompressedOops</extraArgument>&lt;!&ndash;普通对象指针压缩&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+UseCompressedClassPointers-->
<!--                                                </extraArgument>&lt;!&ndash;类指针压缩&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+SegmentedCodeCache</extraArgument>-->
<!--                                                <extraArgument>-XX:ReservedCodeCacheSize=256m</extraArgument>-->
<!--                                                <extraArgument>-XX:InitialCodeCacheSize=256m</extraArgument>-->
<!--                                                <extraArgument>-XX:+PrintCommandLineFlags-->
<!--                                                </extraArgument>&lt;!&ndash;打印那些被新值覆盖的项&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+ExitOnOutOfMemoryError</extraArgument>&lt;!&ndash;内存溢出时退出&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+ExplicitGCInvokesConcurrent</extraArgument>-->
<!--                                                <extraArgument>-XX:MetaspaceSize=${MetaspaceSize}-->
<!--                                                </extraArgument>&lt;!&ndash;初始元空间大小&ndash;&gt;-->
<!--                                                <extraArgument>-XX:MaxMetaspaceSize=${MaxMetaspaceSize}-->
<!--                                                </extraArgument>&lt;!&ndash;最大元空间大小&ndash;&gt;-->
<!--                                                <extraArgument>-XX:+HeapDumpOnOutOfMemoryError-->
<!--                                                </extraArgument> &lt;!&ndash;通过将堆的内容转储到 java_pid.hprof 文件来帮助诊断内存泄漏 &ndash;&gt;-->
<!--                                                <extraArgument>-XX:HeapDumpPath=logs/oom_dump.log</extraArgument>-->
<!--                                                <extraArgument>-XX:ErrorFile=logs/java_error.log</extraArgument>-->
<!--                                                <extraArgument>-Dsun.io.useCanonPrefixCache=false</extraArgument>-->
<!--                                                <extraArgument>-Dfile.encoding=UTF-8</extraArgument>-->
<!--                                                <extraArgument>-Djava.security.egd=file:/dev/./urandom</extraArgument>-->
<!--                                                &lt;!&ndash;<extraArgument>-->
<!--                                                    -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=logs/gc-%t.log:time,tid,tags:filecount=5,filesize=50m  &lt;!&ndash;GC日志输出配置 &ndash;&gt;-->
<!--                                                </extraArgument>&ndash;&gt;-->
<!--                                            </extraArguments>-->
<!--                                        </jvmSettings>-->
<!--                                    </daemon>-->
<!--                                </daemons>-->
<!--                            </configuration>-->
<!--                        </plugin>-->
<!--                        <plugin>-->
<!--                            <groupId>org.apache.maven.plugins</groupId>-->
<!--                            <artifactId>maven-assembly-plugin</artifactId>-->
<!--                            <version>${maven-assembly-plugin.version}</version>-->
<!--                            <configuration>-->
<!--                                <attach>false</attach>-->
<!--                                <appendAssemblyId>false</appendAssemblyId>-->
<!--                                <finalName>${project.artifactId}</finalName>-->
<!--                                <descriptors>-->
<!--                                    <descriptor>${project.basedir}/src/main/assembly/assembly-linux.xml</descriptor>-->
<!--                                </descriptors>-->
<!--                                &lt;!&ndash; 解决maven打包文件名超过100字符的问题 longer than 100 characters &ndash;&gt;-->
<!--                                <tarLongFileMode>gnu</tarLongFileMode>-->
<!--                            </configuration>-->
<!--                            <executions>-->
<!--                                <execution>-->
<!--                                    <id>make-assembly</id>-->
<!--                                    <phase>package</phase>-->
<!--                                    <goals>-->
<!--                                        <goal>single</goal>-->
<!--                                    </goals>-->
<!--                                </execution>-->
<!--                            </executions>-->
<!--                        </plugin>-->
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>pre</id>
            <properties> <!-- 环境标识，需要与配置文件的名称相对应 -->
                <MetaspaceSize>256m</MetaspaceSize>
                <MaxMetaspaceSize>256m</MaxMetaspaceSize>
                <Xmx>4096m</Xmx>
                <Xms>4096m</Xms>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>appassembler-maven-plugin</artifactId>
                            <version>${appassembler-maven-plugin.version}</version>
                            <executions>
                                <execution>
                                    <phase>package</phase>
                                    <goals>
                                        <goal>generate-daemons</goal>
                                    </goals>
                                </execution>
                            </executions>
                            <configuration>
                                <configurationDirectory>conf</configurationDirectory>
                                <includeConfigurationDirectoryInClasspath>true
                                </includeConfigurationDirectoryInClasspath>
                                <repositoryLayout>flat</repositoryLayout>
                                <useWildcardClassPath>true</useWildcardClassPath>
                                <daemons>
                                    <daemon>
                                        <id>${project.artifactId}</id>
                                        <mainClass>${mainClass}</mainClass>
                                        <platforms>
                                            <platform>jsw</platform>
                                        </platforms>
                                        <generatorConfigurations>
                                            <generatorConfiguration>
                                                <generator>jsw</generator>
                                                <includes>
                                                    <include>linux-x86-32</include>
                                                    <include>linux-x86-64</include>
                                                    <include>linux-ppc-64</include>
                                                </includes>
                                                <configuration>
                                                    <property>
                                                        <name>configuration.directory.in.classpath.first</name>
                                                        <value>conf</value>
                                                    </property>
                                                    <property>
                                                        <name>set.default.REPO_DIR</name>
                                                        <value>lib</value>
                                                    </property>
                                                </configuration>
                                            </generatorConfiguration>
                                        </generatorConfigurations>
                                        <jvmSettings>
                                            <extraArguments>
                                                <extraArgument>--add-opens=java.base/java.lang=ALL-UNNAMED
                                                </extraArgument>
                                                <extraArgument>--add-opens=java.base/java.math=ALL-UNNAMED
                                                </extraArgument>
                                                <extraArgument>--add-opens=java.base/java.util=ALL-UNNAMED
                                                </extraArgument>
                                                <extraArgument>--add-opens=java.base/java.util.concurrent=ALL-UNNAMED
                                                </extraArgument>
                                                <extraArgument>--add-opens=java.base/java.net=ALL-UNNAMED
                                                </extraArgument>
                                                <extraArgument>--add-opens=java.base/java.text=ALL-UNNAMED
                                                </extraArgument>
                                                <extraArgument>--add-opens=java.sql/java.sql=ALL-UNNAMED</extraArgument>
                                                <extraArgument>-Xmx${Xmx}</extraArgument><!--最大堆内存-->
                                                <extraArgument>-Xms${Xms}</extraArgument><!--初始堆内存-->
                                                <extraArgument>-XX:+UseG1GC</extraArgument>
                                                <extraArgument>-XX:SoftRefLRUPolicyMSPerMB=50</extraArgument>
                                                <extraArgument>-XX:+UseCompressedOops</extraArgument><!--普通对象指针压缩-->
                                                <extraArgument>-XX:+UseCompressedClassPointers
                                                </extraArgument><!--类指针压缩-->
                                                <extraArgument>-XX:+SegmentedCodeCache</extraArgument>
                                                <extraArgument>-XX:ReservedCodeCacheSize=256m</extraArgument>
                                                <extraArgument>-XX:InitialCodeCacheSize=256m</extraArgument>
                                                <extraArgument>-XX:+PrintCommandLineFlags
                                                </extraArgument><!--打印那些被新值覆盖的项-->
                                                <extraArgument>-XX:+ExitOnOutOfMemoryError</extraArgument><!--内存溢出时退出-->
                                                <extraArgument>-XX:+ExplicitGCInvokesConcurrent</extraArgument>
                                                <extraArgument>-XX:MetaspaceSize=${MetaspaceSize}
                                                </extraArgument><!--初始元空间大小-->
                                                <extraArgument>-XX:MaxMetaspaceSize=${MaxMetaspaceSize}
                                                </extraArgument><!--最大元空间大小-->
                                                <extraArgument>-XX:+HeapDumpOnOutOfMemoryError
                                                </extraArgument> <!--通过将堆的内容转储到 java_pid.hprof 文件来帮助诊断内存泄漏 -->
                                                <extraArgument>-XX:HeapDumpPath=logs/oom_dump.log</extraArgument>
                                                <extraArgument>-XX:ErrorFile=logs/java_error.log</extraArgument>
                                                <extraArgument>-Dsun.io.useCanonPrefixCache=false</extraArgument>
                                                <extraArgument>-Dfile.encoding=UTF-8</extraArgument>
                                                <extraArgument>-Djava.security.egd=file:/dev/./urandom</extraArgument>
                                                <extraArgument>
                                                    -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=logs/gc-%t.log:time,tid,tags:filecount=5,filesize=50m  <!--GC日志输出配置 -->
                                                </extraArgument>
                                            </extraArguments>
                                        </jvmSettings>
                                    </daemon>
                                </daemons>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-assembly-plugin</artifactId>
                            <version>${maven-assembly-plugin.version}</version>
                            <configuration>
                                <attach>false</attach>
                                <appendAssemblyId>false</appendAssemblyId>
                                <finalName>${project.artifactId}</finalName>
                                <descriptors>
                                    <descriptor>${project.basedir}/src/main/assembly/assembly-linux.xml</descriptor>
                                </descriptors>
                                <!-- 解决maven打包文件名超过100字符的问题 longer than 100 characters -->
                                <tarLongFileMode>gnu</tarLongFileMode>
                            </configuration>
                            <executions>
                                <execution>
                                    <id>make-assembly</id>
                                    <phase>package</phase>
                                    <goals>
                                        <goal>single</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>docker-dev</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
<!--                            <version>${spring-boot.version}</version>-->
                            <configuration>
                                <finalName>${project.build.finalName}</finalName>
                                <layers>
                                    <enabled>true</enabled>
                                </layers>
                            </configuration>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>repackage</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>docker-test</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
<!--                            <version>${spring-boot.version}</version>-->
                            <configuration>
                                <finalName>${project.build.finalName}</finalName>
                                <layers>
                                    <enabled>true</enabled>
                                </layers>
                            </configuration>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>repackage</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>docker-prod</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
<!--                            <version>${spring-boot.version}</version>-->
                            <configuration>
                                <finalName>${project.build.finalName}</finalName>
                                <layers>
                                    <enabled>true</enabled>
                                </layers>
                            </configuration>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>repackage</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>docker-pre</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
                            <version>${spring-boot.version}</version>
                            <configuration>
                                <finalName>${project.build.finalName}</finalName>
                                <layers>
                                    <enabled>true</enabled>
                                </layers>
                            </configuration>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>repackage</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>
</project>
