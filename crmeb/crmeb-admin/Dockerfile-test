FROM openjdk:11

ARG APP_NAME=crmeb-admin

ARG APP_PORT=8085

ARG DEBUG_PORT=18085

ARG JAR_FILE=target/${APP_NAME}-0.0.1-SNAPSHOT.jar

RUN mkdir -p /${APP_NAME}/logs

WORKDIR /${APP_NAME}

COPY ${JAR_FILE} app.jar

EXPOSE ${APP_PORT}

EXPOSE ${DEBUG_PORT}

ENV TZ=Asia/Shanghai JAVA_OPTS="-Xmx3g -Xms3g -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:${DEBUG_PORT} -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC -XX:SoftRefLRUPolicyMSPerMB=50 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+SegmentedCodeCache -XX:ReservedCodeCacheSize=256m -XX:InitialCodeCacheSize=256m -XX:+PrintCommandLineFlags -XX:+ExitOnOutOfMemoryError -XX:+ExplicitGCInvokesConcurrent -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/oom_dump.log -XX:ErrorFile=logs/java_error.log -Dsun.io.useCanonPrefixCache=false -Dfile.encoding=UTF-8 -Djava.security.egd=file:/dev/./urandom -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=logs/gc-%t.log:time,tid,tags:filecount=5,filesize=50m --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.sql/java.sql=ALL-UNNAMED"

ENTRYPOINT java ${JAVA_OPTS} -jar app.jar
