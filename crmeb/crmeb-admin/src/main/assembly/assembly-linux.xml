<assembly
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
        xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    <id>linux</id>
    <includeBaseDirectory>false</includeBaseDirectory>
    <formats>
        <format>tar.gz</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/target/generated-resources/appassembler/jsw/${project.artifactId}/bin
            </directory>
            <outputDirectory>${project.artifactId}/bin</outputDirectory>
            <fileMode>0755</fileMode>
            <includes>
                <include>${project.artifactId}</include>
                <include>wrapper-linux*</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target/generated-resources/appassembler/jsw/${project.artifactId}/lib
            </directory>
            <outputDirectory>${project.artifactId}/lib</outputDirectory>
            <includes>
                <include>*.jar</include>
                <include>libwrapper-linux*</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target/generated-resources/appassembler/jsw/${project.artifactId}/conf
            </directory>
            <outputDirectory>${project.artifactId}/conf</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target/classes</directory>
            <outputDirectory>${project.artifactId}/conf</outputDirectory>
            <includes>
                <include>*.properties</include>
                <include>*.yaml</include>
                <include>*.xml</include>
                <include>*.sql</include>
                <include>**/i18n/**</include>
                <include>**/static/**</include>
                <include>**/templates/**</include>
            </includes>
            <excludes>
                <exclude>
                    *-local.*
                </exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/logs</directory>
            <outputDirectory>${project.artifactId}/logs</outputDirectory>
            <excludes>
                <exclude>**/*</exclude>
            </excludes>
        </fileSet>
    </fileSets>

</assembly>
