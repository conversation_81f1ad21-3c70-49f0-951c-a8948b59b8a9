package com.zbkj.admin.controller;

import com.zbkj.common.model.live.LiveRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.LiveRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("api/admin/liveRecord")
@Api(tags = "直播记录")
public class LiveRecordController {

    @Resource
    private LiveRecordService liveRecordService;

    @PreAuthorize("hasAuthority('admin:liveRecord:list')")
    @ApiOperation(value = "直播记录列表")
    @GetMapping(value = "/list")
    public CommonResult<CommonPage<LiveRecord>> list(LiveRecord liveRecord, PageParamRequest pageParamRequest) {
        CommonPage<LiveRecord> liveRoomCommonPage = CommonPage.restPage(liveRecordService.getList(liveRecord, pageParamRequest));
        return CommonResult.success(liveRoomCommonPage);
    }
}
