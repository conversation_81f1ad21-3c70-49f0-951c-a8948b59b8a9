package com.zbkj.admin.task;

import com.zbkj.service.service.PayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.ReentrantLock;

@Component
@Slf4j
public class MpayScheduler {

    @Autowired
    private PayService payService;

    private static final String MPAY_CHECK_UP_ORDER_PATH = "/api/v2.1/merch/user/checkUpOrder";
    private static final String MPAY_CHECK_DOWN_ORDER_PATH = "/api/v2.1/merch/user/checkDownOrder";

    private final ReentrantLock upOrderLock = new ReentrantLock();
    private final ReentrantLock downOrderLock = new ReentrantLock();

    @Scheduled(initialDelay = 30_000, fixedDelay = 180_000)
    public void checkUpOrder() {
        if (upOrderLock.tryLock()) {
            try {
                long startTime = System.currentTimeMillis();
                String response = payService.checkOrder(MPAY_CHECK_UP_ORDER_PATH, "recharge");
                log.info("MPAY:CHECK_UP_ORDER:执行耗时:{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("MPAY:CHECK_UP_ORDER:ERROR:{}", e.getMessage(), e);
            } finally {
                upOrderLock.unlock();
            }
        } else {
            log.warn("MPAY:CHECK_UP_ORDER:上一个任务未完成，跳过");
        }
    }

    @Scheduled(initialDelay = 30_000, fixedDelay = 180_000)
    public void checkDownOrder() {
        if (downOrderLock.tryLock()) {
            try {
                long startTime = System.currentTimeMillis();
                String response = payService.checkOrder(MPAY_CHECK_DOWN_ORDER_PATH, "withdraw");
                log.info("MPAY:CHECK_DOWN_ORDER:执行耗时:{} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("MPAY:CHECK_DOWN_ORDER:ERROR:{}", e.getMessage(), e);
            } finally {
                downOrderLock.unlock();
            }
        } else {
            log.warn("MPAY:CHECK_DOWN_ORDER:上一个任务未完成，跳过");
        }
    }

}