package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserCardRecordRequest;
import com.zbkj.common.request.UserCardShareRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.UserCardRecordResponse;
import com.zbkj.common.response.UserCardShareResponse;
import com.zbkj.service.service.UserCardRecordService;
import com.zbkj.service.service.UserCardShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理端登录服务

 */
@Slf4j
@RestController
@RequestMapping("api/admin/user/card/share")
@Api(tags = "卡片分享服务")
public class UserCardShareController {
    @Autowired
    private UserCardShareService userCardShareService;

    @PreAuthorize("hasAuthority('admin:user:card:share:list')")
    @ApiOperation(value = "卡片历史记录列表")
    @GetMapping(value = "/list")
    public CommonResult<CommonPage<UserCardShareResponse>> list(UserCardShareRequest request) {
        return CommonResult.success(CommonPage.restPage(userCardShareService.getList(request)));
    }
}
