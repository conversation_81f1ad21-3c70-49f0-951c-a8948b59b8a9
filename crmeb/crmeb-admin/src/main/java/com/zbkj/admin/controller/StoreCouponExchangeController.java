package com.zbkj.admin.controller;

import com.zbkj.common.model.coupon.StoreCouponExchange;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.*;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.StoreCouponInfoResponse;
import com.zbkj.service.service.StoreCouponExchangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.Objects;


/**
 * 优惠券表 前端控制器

 */
@Slf4j
@RestController
@RequestMapping("api/admin/marketing/coupon/exchange")
@Api(tags = "营销 -- 优惠券兑换码")
public class StoreCouponExchangeController {
    @Autowired
    private StoreCouponExchangeService storeCouponExchangeService;

    /**
     * 分页显示优惠券兑换表
     */
    @PreAuthorize("hasAuthority('admin:coupon:exchange:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreCouponExchange>>  getList(@Valid StoreCouponExchangeSearchRequest request) {
        CommonPage<StoreCouponExchange> storeCouponExchangeCommonPage = CommonPage.restPage(storeCouponExchangeService.getList(request));
        return CommonResult.success(storeCouponExchangeCommonPage);
    }

    /**
     * 新增兑换码
     * @param request StoreCouponRequest 新增参数
     */
    @PreAuthorize("hasAuthority('admin:coupon:exchange:save')")
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@Valid @RequestBody StoreCouponExchangeAddRequest request) {
        if (storeCouponExchangeService.create(request.getPid(),request.getNumber())) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 删除兑换码
     * @param id 优惠券id
     */
    @PreAuthorize("hasAuthority('admin:coupon:exchange:delete')")
    @ApiOperation(value = "删除兑换码")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public CommonResult<StoreCouponInfoResponse> delete(@RequestParam Integer id) {
        StoreCouponExchange storeCouponExchange = storeCouponExchangeService.getById(id);
        if (Objects.isNull(storeCouponExchange)) {
            return CommonResult.failed("无效的兑换码ID，不能删除");
        }
        if (storeCouponExchangeService.removeById(id)) {
            return CommonResult.success("删除成功");
        } else {
            return CommonResult.failed("删除失败");
        }
    }
}



