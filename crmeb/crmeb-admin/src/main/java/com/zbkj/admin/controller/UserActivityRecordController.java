package com.zbkj.admin.controller;

import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.SystemGroupDataDZPModel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserActivityRecordRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.SystemGroupDataService;
import com.zbkj.service.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.user.UserActivityRecord;
import com.zbkj.service.service.UserActivityRecordService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;


/**
 * 用户活动记录表 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户活动记录表")
@RequestMapping("api/admin/user/activity")
public class UserActivityRecordController {
    @Autowired
    private UserActivityRecordService userActivityRecordService;

    @Autowired
    private SystemGroupDataService systemGroupDataService;

    @Autowired
    private UserService userService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:activity:list')")
    public CommonResult<CommonPage<UserActivityRecord>> list(UserActivityRecordRequest request){
        CommonPage<UserActivityRecord> page = CommonPage.restPage(userActivityRecordService.getList(request));
        return CommonResult.success(page);
    }

    @GetMapping("/selectList")
    @ApiOperation(value = "下拉奖品")
    @PreAuthorize("hasAuthority('admin:user:activity:selectList')")
    public CommonResult<List<SystemGroupDataDZPModel>> selectList(){
        List<SystemGroupDataDZPModel> entityByGid = systemGroupDataService.getEntityByGid(Constants.GROUP_DATA_ID_USER_DZP_CONFIG, SystemGroupDataDZPModel.class);
        return CommonResult.success(entityByGid);
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:user:activity:save')")
    public CommonResult<String> save(@RequestBody @Validated UserActivityRecord userActivityRecord){
        SystemGroupDataDZPModel systemGroupDataServiceEntityByid = systemGroupDataService.getEntityByid(userActivityRecord.getActivityId(), SystemGroupDataDZPModel.class);
        if (systemGroupDataServiceEntityByid == null){
            throw new CrmebException("无效的活动ID");
        }
        User user = userService.getUserByAccount(userActivityRecord.getAccount());
        if (user == null){
            throw new CrmebException("无效的账号");
        }
        if (userActivityRecord.getStatus()==null){
            userActivityRecord.setStatus(false);
        }
        userActivityRecord.setUserId(user.getUid());
        userActivityRecord.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        if (userActivityRecordService.save(userActivityRecord)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:user:activity:update')")
    public CommonResult<String> update(@RequestBody @Validated UserActivityRecord userActivityRecord){
        SystemGroupDataDZPModel systemGroupDataServiceEntityByid = systemGroupDataService.getEntityByid(userActivityRecord.getActivityId(), SystemGroupDataDZPModel.class);
        if (systemGroupDataServiceEntityByid == null){
            throw new CrmebException("无效的活动ID");
        }
        User user = userService.getUserByAccount(userActivityRecord.getAccount());
        if (user == null){
            throw new CrmebException("无效的账号");
        }
        userActivityRecord.setUserId(user.getUid());
        userActivityRecord.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userActivityRecord.setUpdateTime(new Date());
        if (userActivityRecordService.updateById(userActivityRecord)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:user:activity:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (userActivityRecordService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
