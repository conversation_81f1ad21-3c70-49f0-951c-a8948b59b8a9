package com.zbkj.admin.controller;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserAccessListSearchRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.UserAccessList;
import com.zbkj.service.service.UserAccessListService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import java.util.Date;


/**
 * 前台IP或国家地区名单 控制器
 */
@Slf4j
@RestController
@Api(tags = "前台名单配置")
@RequestMapping("api/admin/user/access")
public class UserAccessListController {
    @Autowired
    private UserAccessListService userAccessListService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:access:list')")
    public CommonResult<CommonPage<UserAccessList>> list(UserAccessListSearchRequest request){
        CommonPage<UserAccessList> page = CommonPage.restPage(userAccessListService.getList(request));
        return CommonResult.success(page);
    }


    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:user:access:save')")
    public CommonResult<String> save(@RequestBody @Validated UserAccessList userAccessList){
        UserAccessList userAccessListServiceByRegion = userAccessListService.getByRegion(userAccessList.getRegion());
        if (userAccessListServiceByRegion != null){
            throw new CrmebException("已存在的IP或地区");
        }
        userAccessList.setRegion(userAccessList.getRegion().replace(" ",""));
        userAccessList.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userAccessList.setCreateTime(new Date());
        if (userAccessListService.save(userAccessList)) {
            userAccessListService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:user:access:update')")
    public CommonResult<String> update(@RequestBody @Validated UserAccessList userAccessList){
        userAccessList.setRegion(userAccessList.getRegion().replace(" ",""));
        userAccessList.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userAccessList.setUpdateTime(new Date());
        if (userAccessListService.updateById(userAccessList)) {
            userAccessListService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:user:access:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (userAccessListService.removeById(id)) {
            userAccessListService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
