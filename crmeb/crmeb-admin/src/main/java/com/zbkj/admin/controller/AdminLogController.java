package com.zbkj.admin.controller;

import com.zbkj.common.model.system.AdminLog;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.AdminLogRequest;
import com.zbkj.common.response.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.zbkj.service.service.AdminLogService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;




/**
 * 后台日志记录 控制器
 */
@Slf4j
@RestController
@Api(tags = "后台日志记录")
@RequestMapping("api/admin/adminLog")
public class AdminLogController {
    @Autowired
    private AdminLogService AdminLogService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "日志分页列表")
    @PreAuthorize("hasAuthority('admin:adminLog:list')")
    public CommonResult<CommonPage<AdminLog>> list(AdminLogRequest request){
        CommonPage<AdminLog> page = CommonPage.restPage(AdminLogService.getList(request));
        return CommonResult.success(page);
    }
}
