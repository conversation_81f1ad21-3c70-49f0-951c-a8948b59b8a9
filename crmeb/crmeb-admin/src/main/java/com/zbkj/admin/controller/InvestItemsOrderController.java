package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.model.InvestItemsOrder;
import com.zbkj.service.service.InvestItemsOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import com.zbkj.common.request.InvestItemsOrderSearchRequest;

/**
 * 投资项目订单表 控制器
 */
@Slf4j
@RestController
@Api(tags = "投资项目订单表")
@RequestMapping("api/admin/investItemsOrder")
public class InvestItemsOrderController {
    @Autowired
    private InvestItemsOrderService investItemsOrderService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:investItemsOrder:list')")
    public CommonResult<CommonPage<InvestItemsOrder>> list(InvestItemsOrderSearchRequest request){
        CommonPage<InvestItemsOrder> page = CommonPage.restPage(investItemsOrderService.getList(request));
        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @GetMapping("/info")
    @ApiOperation(value = "详情数据")
    @PreAuthorize("hasAuthority('admin:investItemsOrder:info')")
    public CommonResult<InvestItemsOrder> info(@RequestParam(value = "id") Integer id){
		InvestItemsOrder investItemsOrder = investItemsOrderService.getById(id);
        return CommonResult.success(investItemsOrder);
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:investItemsOrder:save')")
    public CommonResult<String> save(@RequestBody @Validated InvestItemsOrder investItemsOrder){
        if (investItemsOrderService.save(investItemsOrder)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:investItemsOrder:update')")
    public CommonResult<String> update(@RequestBody @Validated InvestItemsOrder investItemsOrder){
        if (investItemsOrderService.updateById(investItemsOrder)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:investItemsOrder:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (investItemsOrderService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
