package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserLoginLogsSearchRequest;
import com.zbkj.common.response.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.UserLoginLogs;
import com.zbkj.service.service.UserLoginLogsService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;




/**
 * 用户登录日志表 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户登录日志表")
@RequestMapping("api/admin/user/loginLogs")
public class UserLoginLogsController {
    @Autowired
    private UserLoginLogsService UserLoginLogsService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:loginLogs:list')")
    public CommonResult<CommonPage<UserLoginLogs>> list(UserLoginLogsSearchRequest request){
        CommonPage<UserLoginLogs> page = CommonPage.restPage(UserLoginLogsService.getList(request));
        return CommonResult.success(page);
    }
}
