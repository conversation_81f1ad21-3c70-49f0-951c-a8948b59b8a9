package com.zbkj.admin.controller;

import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserAuthReviewRequest;
import com.zbkj.common.request.UserAuthSearchRequest;
import com.zbkj.common.request.UserAuthSubmitRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.UserAuthResponse;
import com.zbkj.common.response.UserAuthSubmitResponse;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.UserLockService;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.user.UserAuth;
import com.zbkj.service.service.UserAuthService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;


/**
 * 用户实名认证 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户实名认证")
@RequestMapping("api/admin/user/auth")
public class UserAuthController {
    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserLockService userLockService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:auth:list')")
    public CommonResult<CommonPage<UserAuthResponse>> list(UserAuthSearchRequest request) {
        CommonPage<UserAuthResponse> userAuthResponseCommonPage = CommonPage.restPage(userAuthService.getList(request));
        return CommonResult.success(userAuthResponseCommonPage);
    }

    /**
     * 新增数据
     */
    /*@PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:user:auth:save')")
    public CommonResult<String> save(@RequestBody @Validated UserAuth userAuth) {
        if (UserAuthService.save(userAuth)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }*/

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:user:auth:update')")
    public CommonResult<String> update(@RequestBody @Validated UserAuth userAuth) {
        if (userAuthService.updateById(userAuth)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/review")
    @ApiOperation(value = "审核")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('admin:user:auth:review')")
    public CommonResult<String> review(@RequestBody @Validated UserAuthReviewRequest request) {
        UserAuth userAuth = userAuthService.getById(request.getId());
        if (userAuth == null){
            throw new CrmebException("无效的ID");
        }
        if (userAuth.getManually()){
            throw new CrmebException("已经处理过的信息！");
        }
        userAuth.setReviewer(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userAuth.setReviewTime(new Date());
        userAuth.setStatus(request.getStatus());
        userAuth.setManually(true);

        User user = userService.getInfoByUid(userAuth.getUserId());
        if (user == null){
            throw new CrmebException("当前审核的用户无效");
        }
        if (!user.getAuthenticationStatus().equals(CertificateStatus.APPROVED)){
            user.setAuthenticationStatus(request.getStatus());
            user.setRealName(userAuth.getName());
            userService.updateById(user);
        }
        userAuthService.updateById(userAuth);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:user:auth:delete')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> delete(@RequestParam(value = "id") Long id) {
        // 先获取认证记录
        UserAuth userAuth = userAuthService.getById(id);
        if (userAuth == null) {
            return CommonResult.failed("认证记录不存在");
        }

        // 使用Redis分布式锁保护用户数据更新
        return userLockService.executeWithUserLock(userAuth.getUserId(), () -> {
            // 删除认证记录
            boolean deleteSuccess = userAuthService.removeById(id);
            if (!deleteSuccess) {
                throw new CrmebException("删除认证记录失败");
            }

            // 更新用户认证状态为PENDING
            User user = new User();
            user.setUid(userAuth.getUserId());
            user.setAuthenticationStatus(CertificateStatus.PENDING);
            boolean updateSuccess = userService.updateById(user);
            if (!updateSuccess) {
                throw new CrmebException("更新用户认证状态失败");
            }

            return CommonResult.success("删除成功");
        });
    }

    @GetMapping("/clean")
    @ApiOperation(value = "清理实名认证")
    @PreAuthorize("hasAuthority('admin:user:auth:clean')")
    public CommonResult<Boolean> clean(@RequestParam(name = "password") String password) {
        if (StringUtils.isBlank(password)){
            throw new CrmebException("请输入有效的密码！");
        }
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        String pwd = CrmebUtil.encryptPassword(password, systemAdmin.getAccount());
        if (!systemAdmin.getPwd().equals(pwd)){
            throw new CrmebException("密码错误，无法进行清理操作！");
        }
        boolean clean = userAuthService.clean();
        return CommonResult.success(clean);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "提交实名认证")
    @PreAuthorize("hasAuthority('admin:user:auth:submit')")
    public CommonResult<UserAuthSubmitResponse> submit(
            @RequestBody @Validated UserAuthSubmitRequest request, HttpServletRequest httpServletRequest) {
        String clientIp = CrmebUtil.getClientIp(httpServletRequest);
        UserAuthSubmitResponse response = userAuthService.submitAuth(request, clientIp);
        return CommonResult.success(response, response.getMsg());
    }

}
