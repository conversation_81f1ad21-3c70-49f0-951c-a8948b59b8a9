package com.zbkj.admin.controller;

import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserAuthReviewRequest;
import com.zbkj.common.request.UserAuthSearchRequest;
import com.zbkj.common.request.UserAuthSubmitRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.UserAuthResponse;
import com.zbkj.common.response.UserAuthSubmitResponse;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.UserService;
import com.zbkj.service.service.UserLockService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.user.UserAuth;
import com.zbkj.service.service.UserAuthService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;


/**
 * 用户实名认证 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户实名认证")
@RequestMapping("api/admin/user/auth")
public class UserAuthController {
    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserLockService userLockService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:auth:list')")
    public CommonResult<CommonPage<UserAuthResponse>> list(UserAuthSearchRequest request) {
        CommonPage<UserAuthResponse> userAuthResponseCommonPage = CommonPage.restPage(userAuthService.getList(request));
        return CommonResult.success(userAuthResponseCommonPage);
    }

    /**
     * 新增数据
     */
    /*@PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:user:auth:save')")
    public CommonResult<String> save(@RequestBody @Validated UserAuth userAuth) {
        if (UserAuthService.save(userAuth)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }*/

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:user:auth:update')")
    public CommonResult<String> update(@RequestBody @Validated UserAuth userAuth) {
        if (userAuthService.updateById(userAuth)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 审核实名认证
     */
    @PostMapping("/review")
    @ApiOperation(value = "审核")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('admin:user:auth:review')")
    public CommonResult<String> review(@RequestBody @Validated UserAuthReviewRequest request) {
        try {
            UserAuth userAuth = userAuthService.getById(request.getId());
            if (userAuth == null) {
                throw new CrmebException("无效的ID");
            }
            if (userAuth.getManually()) {
                throw new CrmebException("已经处理过的信息！");
            }

            // 使用Redis分布式锁保护用户数据更新
            return userLockService.executeWithUserLock(userAuth.getUserId(), () -> {
                // 更新认证记录
                userAuth.setReviewer(SecurityUtil.getLoginUserVo().getUser().getAccount());
                userAuth.setReviewTime(new Date());
                userAuth.setStatus(request.getStatus());
                userAuth.setManually(true);

                // 获取用户信息
                User user = userService.getInfoByUid(userAuth.getUserId());
                if (user == null) {
                    throw new CrmebException("当前审核的用户无效");
                }

                // 只有当用户当前状态不是已认证时，才更新用户状态
                if (!CertificateStatus.APPROVED.equals(user.getAuthenticationStatus())) {
                    user.setAuthenticationStatus(request.getStatus());
                    // 如果审核通过，设置真实姓名
                    if (CertificateStatus.APPROVED.equals(request.getStatus())) {
                        user.setRealName(userAuth.getName());
                    }
                    boolean updateUserSuccess = userService.updateById(user);
                    if (!updateUserSuccess) {
                        throw new CrmebException("更新用户认证状态失败");
                    }
                }

                // 更新认证记录
                boolean updateAuthSuccess = userAuthService.updateById(userAuth);
                if (!updateAuthSuccess) {
                    throw new CrmebException("更新认证记录失败");
                }

                log.info("审核认证记录完成，userId: {}, authId: {}, status: {}, 审核人: {}",
                        userAuth.getUserId(), request.getId(), request.getStatus(),
                        SecurityUtil.getLoginUserVo().getUser().getAccount());

                return CommonResult.success("审核完成");
            });

        } catch (Exception e) {
            log.error("审核认证记录失败，authId: {}, error: {}", request.getId(), e.getMessage(), e);
            throw new CrmebException("审核失败：" + e.getMessage());
        }
    }

    /**
     * 删除实名认证记录
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:user:auth:delete')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> delete(@RequestParam(value = "id") Long id) {
        try {
            // 先获取认证记录
            UserAuth userAuth = userAuthService.getById(id);
            if (userAuth == null) {
                return CommonResult.failed("认证记录不存在");
            }

            // 使用Redis分布式锁保护用户数据更新
            return userLockService.executeWithUserLock(userAuth.getUserId(), () -> {
                // 获取用户信息
                User user = userService.getInfoByUid(userAuth.getUserId());
                if (user == null) {
                    log.warn("删除认证记录时，用户不存在，userId: {}, authId: {}", userAuth.getUserId(), id);
                    // 即使用户不存在，也删除认证记录
                    if (userAuthService.removeById(id)) {
                        return CommonResult.success("认证记录已删除");
                    }
                    return CommonResult.failed("删除认证记录失败");
                }

                // 删除认证记录
                boolean deleteSuccess = userAuthService.removeById(id);
                if (!deleteSuccess) {
                    throw new CrmebException("删除认证记录失败");
                }

                // 只有当用户当前状态不是已认证时，才重置为待审核状态
                // 如果用户已经通过认证，删除记录不应该影响其认证状态
                if (!CertificateStatus.APPROVED.equals(user.getAuthenticationStatus())) {
                    user.setAuthenticationStatus(CertificateStatus.PENDING);
                    boolean updateSuccess = userService.updateById(user);
                    if (!updateSuccess) {
                        throw new CrmebException("更新用户认证状态失败");
                    }
                    log.info("删除认证记录并重置用户状态，userId: {}, authId: {}, 操作人: {}",
                            userAuth.getUserId(), id, SecurityUtil.getLoginUserVo().getUser().getAccount());
                } else {
                    log.info("删除认证记录，但保持用户已认证状态，userId: {}, authId: {}, 操作人: {}",
                            userAuth.getUserId(), id, SecurityUtil.getLoginUserVo().getUser().getAccount());
                }

                return CommonResult.success("删除成功");
            });

        } catch (Exception e) {
            log.error("删除认证记录失败，authId: {}, error: {}", id, e.getMessage(), e);
            throw new CrmebException("删除失败：" + e.getMessage());
        }
    }

    @GetMapping("/clean")
    @ApiOperation(value = "清理实名认证")
    @PreAuthorize("hasAuthority('admin:user:auth:clean')")
    public CommonResult<Boolean> clean(@RequestParam(name = "password") String password) {
        if (StringUtils.isBlank(password)){
            throw new CrmebException("请输入有效的密码！");
        }
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        String pwd = CrmebUtil.encryptPassword(password, systemAdmin.getAccount());
        if (!systemAdmin.getPwd().equals(pwd)){
            throw new CrmebException("密码错误，无法进行清理操作！");
        }
        boolean clean = userAuthService.clean();
        return CommonResult.success(clean);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "提交实名认证")
    @PreAuthorize("hasAuthority('admin:user:auth:submit')")
    public CommonResult<UserAuthSubmitResponse> submit(
            @RequestBody @Validated UserAuthSubmitRequest request, HttpServletRequest httpServletRequest) {
        String clientIp = CrmebUtil.getClientIp(httpServletRequest);
        UserAuthSubmitResponse response = userAuthService.submitAuth(request, clientIp);
        return CommonResult.success(response, response.getMsg());
    }

}
