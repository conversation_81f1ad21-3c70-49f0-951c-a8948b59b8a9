package com.zbkj.admin.controller;

import com.zbkj.common.model.system.SystemUserLevel;
import com.zbkj.common.model.user.UserLevel;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.model.UserSignConfig;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.SystemUserLevelService;
import com.zbkj.service.service.UserLevelService;
import com.zbkj.service.service.UserSignConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import com.zbkj.common.request.UserSignConfigSearchRequest;

import java.util.List;

/**
 * 用户签到配置 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户签到配置")
@RequestMapping("api/admin/userSignConfig")
public class UserSignConfigController {
    @Autowired
    private UserSignConfigService userSignConfigService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:userSignConfig:list')")
    public CommonResult<CommonPage<UserSignConfig>> list(UserSignConfigSearchRequest request){
        CommonPage<UserSignConfig> page = CommonPage.restPage(userSignConfigService.getList(request));
        return CommonResult.success(page);
    }

    @PreAuthorize("hasAuthority('admin:userSignConfig:selectList')")
    @ApiOperation(value = "等级下拉列表")
    @RequestMapping(value = "/selectList", method = RequestMethod.GET)
    public CommonResult<List<SystemUserLevel>>  getList() {
        return CommonResult.success(systemUserLevelService.getList());
    }


    /**
     * 详情数据
     */
    @GetMapping("/info")
    @ApiOperation(value = "详情数据")
    @PreAuthorize("hasAuthority('admin:userSignConfig:info')")
    public CommonResult<UserSignConfig> info(@RequestParam(value = "id") Integer id){
		UserSignConfig userSignConfig = userSignConfigService.getById(id);
        return CommonResult.success(userSignConfig);
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:userSignConfig:save')")
    public CommonResult<String> save(@RequestBody @Validated UserSignConfig userSignConfig){
        SystemUserLevel byId = systemUserLevelService.getById(userSignConfig.getLevelId());
        if (byId == null){
            return CommonResult.failed("请选择有效的等级");
        }
        UserSignConfig configByLevel = userSignConfigService.getConfigByLevel(userSignConfig.getLevelId());
        if (configByLevel != null){
            return CommonResult.failed("该等级已存在配置");
        }
        userSignConfig.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        if (userSignConfigService.save(userSignConfig)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:userSignConfig:update')")
    public CommonResult<String> update(@RequestBody @Validated UserSignConfig userSignConfig){
        if (userSignConfig.getId() == null){
            return CommonResult.failed("id不能为空");
        }
        UserSignConfig configByLevel = userSignConfigService.getConfigByLevel(userSignConfig.getLevelId());
        if (configByLevel != null){
            if (configByLevel.getId() != userSignConfig.getId()){
                return CommonResult.failed("该等级已存在配置");
            }
        }
        userSignConfig.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        if (userSignConfigService.updateById(userSignConfig)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:userSignConfig:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (userSignConfigService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
