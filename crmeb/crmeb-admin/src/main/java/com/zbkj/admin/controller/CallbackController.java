package com.zbkj.admin.controller;

import com.zbkj.common.vo.*;
import com.zbkj.service.service.CallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * 支付回调

 */
@Slf4j
@RestController
@RequestMapping("api/admin/payment/callback")
@Api(tags = "支付回调")
public class CallbackController {

    @Autowired
    private CallbackService callbackService;

    /**
     * 微信支付回调
     */
    @ApiOperation(value = "微信支付回调")
    @RequestMapping(value = "/wechat", method = RequestMethod.POST)
    public String weChat(@RequestBody String request) {
        System.out.println("微信支付回调 request ===> " + request);
        String response = callbackService.weChat(request);
        System.out.println("微信支付回调 response ===> " + response);
        return response;
    }

    /**
     * 微信退款回调
     */
    @ApiOperation(value = "微信退款回调")
    @RequestMapping(value = "/wechat/refund", method = RequestMethod.POST)
    public String weChatRefund(@RequestBody String request) {
        System.out.println("微信退款回调 request ===> " + request);
        String response = callbackService.weChatRefund(request);
        System.out.println("微信退款回调 response ===> " + response);
        return response;
    }

    /**
     * ai pay支付回调
     */
    @ApiOperation(value = "AI PAY支付回调")
    @RequestMapping(value = "/aiPay", method = RequestMethod.POST)
    public String apiPay(@RequestBody AiPayPaymentCallback request) {
        return callbackService.apiPay(request) ? "SUCCESS" : "FAIL";
    }

    @ApiOperation(value = "M支付回调")
    @RequestMapping(value = "/mPay", method = RequestMethod.POST)
    public String mPay(@ModelAttribute MPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.mPay(request,httpServletRequest) ? "SUCCESS" : "FAIL";
    }

    @ApiOperation(value = "M代付回调")
    @RequestMapping(value = "/mAgentPay", method = RequestMethod.POST)
    public String mAgentPay(@ModelAttribute MPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.mAgentPay(request,httpServletRequest) ? "SUCCESS" : "FAIL";
    }

    @ApiOperation(value = "FPAY支付回调")
    @RequestMapping(value = "/fPay", method = RequestMethod.POST)
    public String fPay(@ModelAttribute FPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.fPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "WANB支付回调")
    @RequestMapping(value = "/wanbPay", method = RequestMethod.POST)
    public String wanbPay(@RequestBody WanbPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.wanbPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "AA支付回调")
    @RequestMapping(value = "/aaPay", method = RequestMethod.POST)
    public String aaPay(@RequestBody AAPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.aaPay(request,httpServletRequest) ? "success" : "FAIL";
    }


    @ApiOperation(value = "FPAY代付回调")
    @RequestMapping(value = "/fPayAgent", method = RequestMethod.POST)
    public String fPayAgent(@ModelAttribute FPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.fPayAgent(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "CBPAY钱包支付回调")
    @RequestMapping(value = "/cbPay", method = RequestMethod.POST,consumes = "application/x-www-form-urlencoded")
    public String cbPay(@ModelAttribute CBPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.cbPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "K豆支付回调")
    @RequestMapping(value = "/kdPay", method = RequestMethod.POST,consumes = "application/x-www-form-urlencoded")
    public String kdPay(@ModelAttribute CBPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.kdPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "K豆代付回调")
    @RequestMapping(value = "/kdAgentPay", method = RequestMethod.POST,consumes = "application/x-www-form-urlencoded")
    public String kdAgentPay(@ModelAttribute CBPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.kdAgentPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "CB代付回调")
    @RequestMapping(value = "/cbAgentPay", method = RequestMethod.POST,consumes = "application/x-www-form-urlencoded")
    public String cbAgentPay(@ModelAttribute CBPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.cbAgentPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "博启代付回调")
    @RequestMapping(value = "/bqAgentPay", method = RequestMethod.POST,consumes = "application/x-www-form-urlencoded")
    public String bqAgentPay(@ModelAttribute BQPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.bqAgentPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "钻石代付回调")
    @RequestMapping(value = "/dmAgentPay", method = RequestMethod.POST)
    public String dmAgentPay(@ModelAttribute DMPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.dmAgentPay(request,httpServletRequest) ? "SUCCESS" : "FAIL";
    }

    @ApiOperation(value = "八达通代付回调")
    @RequestMapping(value = "/bdtAgentPay", method = RequestMethod.POST)
    public String bdtAgentPay(@ModelAttribute BDTPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.bdtAgentPay(request,httpServletRequest) ? "SUCCESS" : "FAIL";
    }
    @ApiOperation(value = "AA代付回调")
    @RequestMapping(value = "/aaAgentPay", method = RequestMethod.POST)
    public String aaAgentPay(@RequestBody AAAgentPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.aaAgentPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "新鹰代付回调")
    @RequestMapping(value = "/xyAgentPay", method = RequestMethod.POST,consumes = "application/x-www-form-urlencoded")
    public String bqAgentPay(@ModelAttribute XYPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.xyAgentPay(request,httpServletRequest) ? "OK" : "FAIL";
    }

    @ApiOperation(value = "TO支付回调")
    @RequestMapping(value = "/toPay", method = RequestMethod.POST)
    public String toPay(@RequestBody ToPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.toPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "OK支付回调")
    @RequestMapping(value = "/okPay", method = RequestMethod.POST)
    public String okPay(@RequestBody OkPayPaymentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.okPay(request,httpServletRequest) ? "success" : "FAIL";
    }

    @ApiOperation(value = "TO代付回调")
    @RequestMapping(value = "/toAgentPay", method = RequestMethod.POST)
    public String toAgentPay(@RequestBody ToAgentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.toAgentPay(request,httpServletRequest) ? "ok" : "FAIL";
    }

    @ApiOperation(value = "OK代付回调")
    @RequestMapping(value = "/okAgentPay", method = RequestMethod.POST)
    public String okAgentPay(@RequestBody OkAgentCallback request, HttpServletRequest httpServletRequest) {
        return callbackService.okAgentPay(request,httpServletRequest) ? "ok" : "FAIL";
    }

}



