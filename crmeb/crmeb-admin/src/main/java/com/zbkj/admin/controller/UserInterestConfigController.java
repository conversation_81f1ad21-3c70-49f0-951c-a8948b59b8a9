package com.zbkj.admin.controller;

import com.zbkj.common.enums.InterestType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.UserInterestConfig;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserInterestConfigRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.service.service.UserInterestConfigService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 用户利息配置表 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户利息配置表")
@RequestMapping("api/admin/user/interestConfig")
public class UserInterestConfigController {
    @Autowired
    private UserInterestConfigService userInterestConfigService;
    @Autowired
    private UserService userService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:interestConfig:list')")
    public CommonResult<CommonPage<UserInterestConfig>> list(UserInterestConfigRequest request){
        CommonPage<UserInterestConfig> page = CommonPage.restPage(userInterestConfigService.getList(request));
        return CommonResult.success(page);
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:user:interestConfig:save')")
    public CommonResult<String> save(@RequestBody @Validated UserInterestConfig userInterestConfig){
        if (userInterestConfig.getType() == InterestType.INTEREST && userInterestConfig.getInterest().compareTo(BigDecimal.ZERO) <= 0){
            throw new CrmebException("请提供有效的利息");
        }
        if (StringUtils.isBlank(userInterestConfig.getAccount())){
            throw new CrmebException("请输入用户账号");
        }
        User user = userService.getUserByAccount(userInterestConfig.getAccount());
        if (user == null){
            throw new CrmebException("无效的会员账号");
        }
        userInterestConfig.setUserId(user.getUid());
        userInterestConfig.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userInterestConfig.setCreateTime(new Date());
        if (userInterestConfigService.save(userInterestConfig)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:user:interestConfig:update')")
    public CommonResult<String> update(@RequestBody @Validated UserInterestConfig userInterestConfig){
        if (userInterestConfig.getType() == InterestType.INTEREST && userInterestConfig.getInterest().compareTo(BigDecimal.ZERO) <= 0){
            throw new CrmebException("请提供有效的利息");
        }
        if (StringUtils.isBlank(userInterestConfig.getAccount())){
            throw new CrmebException("请输入用户账号");
        }
        User user = userService.getUserByAccount(userInterestConfig.getAccount());
        if (user == null){
            throw new CrmebException("无效的会员账号");
        }
        userInterestConfig.setUserId(user.getUid());
        userInterestConfig.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userInterestConfig.setUpdateTime(new Date());
        if (userInterestConfigService.updateById(userInterestConfig)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:user:interestConfig:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (userInterestConfigService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
