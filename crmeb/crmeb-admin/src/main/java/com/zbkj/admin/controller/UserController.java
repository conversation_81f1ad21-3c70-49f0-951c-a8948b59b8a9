package com.zbkj.admin.controller;


import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.*;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.response.TopDetail;
import com.zbkj.common.response.UserResponse;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.UserExtractService;
import com.zbkj.service.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户表 前端控制器

 */
@Slf4j
@RestController
@RequestMapping("api/admin/user")
@Api(tags = "会员管理")
@Validated
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private UserExtractService userExtractService;

    /**
     * 分页显示用户表
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:user:list')")
    @ApiOperation(value = "分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserResponse>> getList(@ModelAttribute @Validated UserSearchRequest request,
                                                          @ModelAttribute PageParamRequest pageParamRequest) {
        CommonPage<UserResponse> userCommonPage = CommonPage.restPage(userService.getList(request, pageParamRequest));
        return CommonResult.success(userCommonPage);
    }

    /**
     * 修改用户表
     * @param id integer id
     * @param userRequest 修改参数
     */
    @PreAuthorize("hasAuthority('admin:user:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam Integer id, @RequestBody @Validated UserUpdateRequest userRequest) {
        userRequest.setUid(id);
        if (userService.updateUser(userRequest)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改用户手机号
     * @param id 用户uid
     * @param phone 手机号
     */
    @PreAuthorize("hasAuthority('admin:user:update:phone')")
    @ApiOperation(value = "修改用户手机号")
    @RequestMapping(value = "/update/phone", method = RequestMethod.GET)
    public CommonResult<String> updatePhone(@RequestParam(name = "id") Integer id, @RequestParam(name = "phone") String phone) {
        if (userService.updateUserPhone(id, phone)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改专属用户组
     * @param id 用户uid
     * @param exclusiveId 专属用户组ID
     */
    @PreAuthorize("hasAuthority('admin:user:update:exclusive')")
    @ApiOperation(value = "修改专属用户组")
    @RequestMapping(value = "/update/exclusive", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> updateExclusive(@RequestParam(name = "id") Integer id, @RequestParam(name = "exclusiveId") Long exclusiveId) {
        User user = userService.getInfoByUid(id);
        if (user == null){
            throw new CrmebException("无效的用户ID！");
        }
        user.setExclusiveId(exclusiveId);
        userService.updateById(user);
        return CommonResult.success();
    }

    /**
     * 一键清理所有手机号
     * @return
     */
    @PreAuthorize("hasAuthority('admin:user:clear:phoneAll')")
    @ApiOperation(value = "一键清理所有手机号")
    @RequestMapping(value = "/clear/phoneAll", method = RequestMethod.GET)
    public CommonResult<String> clearPhoneAll(@RequestParam(name = "password") String password) {
        if (StringUtils.isBlank(password)){
            throw new CrmebException("请输入有效的密码！");
        }
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        String pwd = CrmebUtil.encryptPassword(password, systemAdmin.getAccount());
        if (!systemAdmin.getPwd().equals(pwd)){
            throw new CrmebException("密码错误，无法进行清理操作！");
        }
        if (userService.clearPhoneAll()) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /***
     * 修改用户密码
     * @param userUpdatePasswordRequest
     * @return
     */
    @PreAuthorize("hasAuthority('admin:user:update:password')")
    @ApiOperation(value = "修改用户密码")
    @RequestMapping(value = "/update/password", method = RequestMethod.POST)
    public CommonResult<String> updatePassword(@RequestBody @Validated UserUpdatePasswordRequest userUpdatePasswordRequest) {
        if (userService.updateUserPassword(userUpdatePasswordRequest)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /***
     * 修改取款密码
     * @param userUpdatePasswordRequest
     * @return
     */
    @PreAuthorize("hasAuthority('admin:user:update:withdraw:password')")
    @ApiOperation(value = "修改用户密码")
    @RequestMapping(value = "/withdraw/password", method = RequestMethod.POST)
    public CommonResult<String> withdrawPwd(@RequestBody @Validated UserUpdatePasswordRequest userUpdatePasswordRequest) {
        if (userService.updateUserWithdrawPassword(userUpdatePasswordRequest)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /***
     * 修改支付密码
     * @param userUpdatePasswordRequest
     * @return
     */
    @PreAuthorize("hasAuthority('admin:user:update:payment:password')")
    @ApiOperation(value = "修改用户支付密码")
    @RequestMapping(value = "/payment/password", method = RequestMethod.POST)
    public CommonResult<String> paymentPwd(@RequestBody @Validated UserUpdatePasswordRequest userUpdatePasswordRequest) {
        if (userService.updateUserPaymentPassword(userUpdatePasswordRequest)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }


    /**
     * 用户详情
     * @param id Integer
     */
    @PreAuthorize("hasAuthority('admin:user:info')")
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<User> info(@RequestParam(value = "id") Integer id) {
        return CommonResult.success(userService.getInfoByUid(id));
    }
    /**
     * 用户手机号
     * @param id Integer
     */
    @PreAuthorize("hasAuthority('admin:user:info:phone')")
    @ApiOperation(value = "用户手机号")
    @RequestMapping(value = "/info/phone", method = RequestMethod.GET)
    public CommonResult<User> infoPhone(@RequestParam(value = "id") Integer id) {
        User user = userService.getInfoByUid(id);
        if (StringUtils.isBlank(user.getPhone())){
            user.setPhone(user.getEncryptedPhone());
            user.setNumberCode("");
        }
        return CommonResult.success(user);
    }

    /**
     * 根据参数类型查询会员对应的信息
     * @param userId Integer 会员id
     * @param type int 类型 0=消费记录，1=积分明细，2=签到记录，3=持有优惠券，4=余额变动，5=好友关系
     * @param pageParamRequest PageParamRequest 分页
     */
    @PreAuthorize("hasAuthority('admin:user:infobycondition')")
    @ApiOperation(value="会员详情")
    @RequestMapping(value = "/infobycondition", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId",example = "1", required = true),
            @ApiImplicitParam(name = "type", value="0=消费记录，1=积分明细，2=签到记录，3=持有优惠券，4=余额变动，5=好友关系", example = "0"
                    , required = true)
    })
    public CommonResult<CommonPage<T>> infoByCondition(@RequestParam(name = "userId") @Valid Integer userId,
                                                       @RequestParam(name = "type") @Valid @Max(5) @Min(0) int type,
                                                       @ModelAttribute PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage((List<T>) userService.getInfoByCondition(userId,type,pageParamRequest)));
    }

    /**
     * 会员详情页Top数据
     */
    @PreAuthorize("hasAuthority('admin:user:topdetail')")
    @ApiOperation(value = "会员详情页Top数据")
    @RequestMapping(value = "topdetail", method = RequestMethod.GET)
    public CommonResult<TopDetail> topDetail (@RequestParam @Valid Integer userId) {
        return CommonResult.success(userService.getTopDetail(userId));
    }

    @PreAuthorize("hasAuthority('admin:user:operate:frozenBalance')")
    @ApiOperation(value = "冻结余额")
    @RequestMapping(value = "/operate/frozenBalance", method = RequestMethod.GET)
    public CommonResult<Object> frozenBalance(@Validated UserOperateIntegralMoneyRequest request) {
        if (request.getUid() == null) {
            return CommonResult.failed("请选择会员");
        }
        if (userService.updateFrozenBalance(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 操作积分
     */
    @PreAuthorize("hasAuthority('admin:user:operate:founds')")
    @ApiOperation(value = "积分余额")
    @RequestMapping(value = "/operate/founds", method = RequestMethod.GET)
    public CommonResult<Object> founds(@Validated UserOperateIntegralMoneyRequest request) {
        if (request.getUid() == null) {
            return CommonResult.failed("请选择会员");
        }
        if (userService.updateIntegralMoney(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }


    /**
     * 操作积分 批量
     */
    @PreAuthorize("hasAuthority('admin:user:operate:founds:batch')")
    @ApiOperation(value = "积分余额批量")
    @RequestMapping(value = "/operate/founds/batch", method = RequestMethod.GET)
    public CommonResult<Object> foundsBatch(@Validated UserOperateIntegralMoneyRequest request) {
        if (StringUtils.isEmpty(request.getAccount())) {
            return CommonResult.failed("请提供会员账号");
        }
        if (userService.updateIntegralMoneyBatch(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 会员分组
     * @param id String id
     * @param groupId Integer 分组Id
     */
    @PreAuthorize("hasAuthority('admin:user:group')")
    @ApiOperation(value = "分组")
    @RequestMapping(value = "/group", method = RequestMethod.POST)
    public CommonResult<String> group(@RequestParam String id, @RequestParam Integer groupId) {
        if (userService.group(id, groupId)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 会员标签
     * @param id String id
     * @param tagId Integer 标签id
     */
    @PreAuthorize("hasAuthority('admin:user:tag')")
    @ApiOperation(value = "标签")
    @RequestMapping(value = "/tag", method = RequestMethod.POST)
    public CommonResult<String> tag(@RequestParam String id, @RequestParam String tagId) {
        if (userService.tag(id, tagId)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改上级推广人
     */
    @PreAuthorize("hasAuthority('admin:user:update:spread')")
    @ApiOperation(value = "修改上级推广人")
    @RequestMapping(value = "/update/spread", method = RequestMethod.POST)
    public CommonResult<String> editSpread(@Validated @RequestBody UserUpdateSpreadRequest request) {
        if (userService.editSpread(request)) {
            return CommonResult.success("修改成功");
        }
        return CommonResult.failed("修改失败");
    }

    /**
     * 批量修改上级推广人
     * @param request 批量更新推广人请求参数
     */
    @PreAuthorize("hasAuthority('admin:user:update:spread:batch')")
    @ApiOperation(value = "批量修改上级推广人")
    @RequestMapping(value = "/update/spread/batch", method = RequestMethod.POST)
    public CommonResult<String> editSpreadBatch(@Validated @RequestBody UserUpdateSpreadBatchRequest request) {
        if (userService.editSpreadBatch(request)) {
            return CommonResult.success("修改成功");
        }
        return CommonResult.failed("修改失败");
    }

    /**
     * 更新用户会员等级
     */
    @PreAuthorize("hasAuthority('admin:user:update:level')")
    @ApiOperation(value = "更新用户会员等级")
    @RequestMapping(value = "/update/level", method = RequestMethod.POST)
    public CommonResult<Object> updateUserLevel(@Validated @RequestBody UpdateUserLevelRequest request) {
        if (userService.updateUserLevel(request)) {
            return CommonResult.success("更新成功");
        }
        return CommonResult.failed("更新失败");
    }

    /**
     * 设置余额利息
     */
    @PreAuthorize("hasAuthority('admin:user:balance:interest')")
    @ApiOperation(value = "余额利息")
    @RequestMapping(value = "/balanceInterest", method = RequestMethod.POST)
    public CommonResult<Boolean> balanceInterest(@RequestParam String id, @RequestParam BigDecimal interest) {
        User serviceById = userService.getById(id);
        if (serviceById == null){
            throw new CrmebException("无效的用户ID");
        }
        serviceById.setBalanceInterest(interest);
        return CommonResult.success(userService.updateById(serviceById));
    }

    /**
     * 设置提款手续费
     */
    @PreAuthorize("hasAuthority('admin:user:withdraw:fee')")
    @ApiOperation(value = "设置提款手续费")
    @RequestMapping(value = "/withdrawFee", method = RequestMethod.POST)
    public CommonResult<Boolean> withdrawFee(@RequestParam String id, @RequestParam BigDecimal fee) {
        User serviceById = userService.getById(id);
        if (serviceById == null){
            throw new CrmebException("无效的用户ID");
        }
        serviceById.setWithdrawFee(fee);
        return CommonResult.success(userService.updateById(serviceById));
    }

    /**
     * 设置集卡额外签到次数
     */
    @PreAuthorize("hasAuthority('admin:user:withdraw:jika:num')")
    @ApiOperation(value = "设置集卡额外次数")
    @RequestMapping(value = "/jikaNum", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> jikaNum(@Validated @RequestBody ActivityNumRequest request) {
        if (request.getId() == null && StringUtils.isBlank(request.getAccount())){
            throw new CrmebException("请提供有效的ID或账号");
        }
        if (request.getId() != null && request.getId() > 0){
            User serviceById = userService.getById(request.getId());
            if (serviceById == null){
                throw new CrmebException("无效的用户ID");
            }
            request.setAccount(serviceById.getAccount());
        }
        if (StringUtils.isNotBlank(request.getAccount())){
            String[] strings = request.getAccount().replaceAll(" ", "").split("\n");
            List<User> userList = new ArrayList<>(strings.length);
            for (String string : strings) {
                User serviceById = userService.getUserByAccount(string);
                if (serviceById == null){
                    continue;
                }
                int sum = serviceById.getJikaSignNum();
                if (request.getType() == 1){
                    sum+= request.getCount();
                }else if (request.getType() == 2){
                    if (sum < 0 || sum-request.getCount() < 0){
                        continue;//throw new CrmebException("用户集卡次数不足");
                    }
                    sum-=request.getCount();
                }else {
                    continue;//throw new CrmebException("错误的类型");
                }
                serviceById.setJikaSignNum(sum);
                userList.add(serviceById);
            }
            if (userList.size()>0){
                userService.updateBatchById(userList);
            }
        }
        return CommonResult.success();
    }
    /**
     * 设置大转盘额外签到次数
     */
    @PreAuthorize("hasAuthority('admin:user:withdraw:dzp:num')")
    @ApiOperation(value = "设置大转盘额外次数")
    @RequestMapping(value = "/dzpNum", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> dzpNum(@Validated @RequestBody ActivityNumRequest request) {
        if (request.getId() == null && StringUtils.isBlank(request.getAccount())){
            throw new CrmebException("请提供有效的ID或账号");
        }
        if (request.getId() != null && request.getId() > 0){
            User serviceById = userService.getById(request.getId());
            if (serviceById == null){
                throw new CrmebException("无效的用户ID");
            }
            request.setAccount(serviceById.getAccount());
        }
        if (StringUtils.isNotBlank(request.getAccount())) {
            String[] strings = request.getAccount().replaceAll(" ", "").split("\n");
            List<User> userList = new ArrayList<>(strings.length);
            for (String string : strings) {
                User serviceById = userService.getUserByAccount(string);
                if (serviceById == null){
                    continue;
                }
                int sum = serviceById.getDzpSignNum();
                if (request.getType() == 1){
                    sum+= request.getCount();
                }else if (request.getType() == 2){
                    if (sum < 0 || sum-request.getCount() < 0){
                        continue;//throw new CrmebException("用户次数不足");
                    }
                    sum-=request.getCount();
                }else {
                    continue;//throw new CrmebException("错误的类型");
                }
                serviceById.setDzpSignNum(sum);
                userList.add(serviceById);
            }
            if (userList.size() > 0){
                userService.updateBatchById(userList);
            }
        }
        return CommonResult.success();
    }

    /**
     * 设置大转盘额外签到次数
     */
    @PreAuthorize("hasAuthority('admin:user:withdraw:hb:num')")
    @ApiOperation(value = "设置红包次数")
    @RequestMapping(value = "/hbNum", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> hbNum(@Validated @RequestBody ActivityNumRequest request) {
        if (request.getId() == null && StringUtils.isBlank(request.getAccount())){
            throw new CrmebException("请提供有效的ID或账号");
        }
        if (request.getId() != null && request.getId() > 0){
            User serviceById = userService.getById(request.getId());
            if (serviceById == null){
                throw new CrmebException("无效的用户ID");
            }
            request.setAccount(serviceById.getAccount());
        }
        if (StringUtils.isNotBlank(request.getAccount())) {
            String[] strings = request.getAccount().replaceAll(" ", "").split("\n");
            List<User> userList = new ArrayList<>(strings.length);
            for (String string : strings) {
                User serviceById = userService.getUserByAccount(string);
                if (serviceById == null){
                    continue;
                }
                int sum = serviceById.getHbSignNum();
                if (request.getType() == 1){
                    sum+= request.getCount();
                }else if (request.getType() == 2){
                    if (sum < 0 || sum-request.getCount() < 0){
                        continue;//throw new CrmebException("用户次数不足");
                    }
                    sum-=request.getCount();
                }else {
                    continue;//throw new CrmebException("错误的类型");
                }
                serviceById.setHbSignNum(sum);
                userList.add(serviceById);
            }
            if (userList.size() > 0){
                userService.updateBatchById(userList);
            }
        }
        return CommonResult.success();
    }
}



