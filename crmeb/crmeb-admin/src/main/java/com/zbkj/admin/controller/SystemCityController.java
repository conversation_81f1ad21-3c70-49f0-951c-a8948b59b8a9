package com.zbkj.admin.controller;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemCity;
import com.zbkj.common.request.SystemCityRequest;
import com.zbkj.common.request.SystemCitySearchRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.vo.SystemCityTreeVo;
import com.zbkj.service.service.SystemCityAsyncService;
import com.zbkj.service.service.SystemCityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 城市表 前端控制器

 */
@Slf4j
@RestController
@RequestMapping("api/admin/system/city")
@Api(tags = "城市管理")
public class SystemCityController {

    @Autowired
    private SystemCityService systemCityService;

    @Autowired
    private SystemCityAsyncService systemCityAsyncService;

    /**
     * 分页城市列表
     * @param request 搜索条件
     */
    @PreAuthorize("hasAuthority('admin:system:city:list')")
    @ApiOperation(value = "分页城市列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<Object> getList(@Validated SystemCitySearchRequest request) {
        return CommonResult.success(systemCityService.getList(request));
    }

    /**
     * 修改城市
     * @param id 城市id
     * @param request 修改参数
     */
    @PreAuthorize("hasAuthority('admin:system:city:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam Integer id, @Validated SystemCityRequest request) {
        if (systemCityService.update(id, request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 新增城市
     * @param systemCity
     * @return
     */
    @PreAuthorize("hasAuthority('admin:system:city:save')")
    @ApiOperation(value = "新增城市")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated SystemCity systemCity) {
        systemCity.setId(systemCityService.maxId());
        if (systemCityService.save(systemCity)) {
            systemCityAsyncService.setListTree();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除城市
     * @return
     */
    @PreAuthorize("hasAuthority('admin:system:city:delete')")
    @ApiOperation(value = "删除城市")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public CommonResult<Boolean> delete(@RequestParam(value = "id") Integer id) {
        SystemCity byId = systemCityService.getById(id);
        if (byId == null){
            throw new CrmebException("无效的ID");
        }
        Boolean hasChildren = systemCityService.hasChildren(byId.getId());
        if (hasChildren){
            throw new CrmebException("存在下级数据，无法删除");
        }
        systemCityService.removeById(id);
        systemCityAsyncService.setListTree();
        return CommonResult.success();
    }

    /**
     * 修改状态
     * @param id 城市id
     * @param status 状态
     */
    @PreAuthorize("hasAuthority('admin:system:city:update:status')")
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@RequestParam Integer id, @RequestParam Boolean status) {
        if (systemCityService.updateStatus(id, status)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 城市详情
     * @param id 城市id
     */
    @PreAuthorize("hasAuthority('admin:system:city:info')")
    @ApiOperation(value = "城市详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<SystemCity> info(@RequestParam(value = "id") Integer id) {
        return CommonResult.success(systemCityService.getById(id));
    }

    /**
     * 获取tree结构的列表
     */
    @PreAuthorize("hasAuthority('admin:system:city:list:tree')")
    @ApiOperation(value = "获取tree结构的列表")
    @RequestMapping(value = "/list/tree", method = RequestMethod.GET)
    public CommonResult<List<SystemCityTreeVo>> getListTree() {
        return CommonResult.success(systemCityService.getListTree());
    }
}



