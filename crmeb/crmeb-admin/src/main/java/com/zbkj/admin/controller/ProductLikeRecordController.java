package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.ProductLikeRecordSearchRequest;
import com.zbkj.common.response.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.ProductLikeRecord;
import com.zbkj.service.service.ProductLikeRecordService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;


/**
 * 商品点赞记录 控制器
 */
@Slf4j
@RestController
@Api(tags = "商品点赞记录")
@RequestMapping("api/admin/product/like/record")
public class ProductLikeRecordController {
    @Autowired
    private ProductLikeRecordService productLikeRecordService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:product:like:record:list')")
    public CommonResult<CommonPage<ProductLikeRecord>> list(ProductLikeRecordSearchRequest request) {
        CommonPage<ProductLikeRecord> page = CommonPage.restPage(productLikeRecordService.getList(request));
        return CommonResult.success(page);
    }
}
