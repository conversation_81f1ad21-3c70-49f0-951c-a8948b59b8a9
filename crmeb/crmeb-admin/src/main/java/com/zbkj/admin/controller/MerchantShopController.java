package com.zbkj.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.enums.AccountType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.category.Category;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.MerchantExtractRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.CategoryService;
import com.zbkj.service.service.MerchantShopService;
import com.zbkj.service.service.SystemAdminService;
import com.zbkj.service.service.UserExtractService;
import com.zbkj.service.service.biz.MerchantShopBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 管理端登录服务

 */
@Slf4j
@RestController
@RequestMapping("api/admin/merchant/shop")
@Api(tags = "商户店铺服务")
public class MerchantShopController {

    @Autowired
    private MerchantShopService merchantShopService;

    @Autowired
    private SystemAdminService systemAdminService;

    @Autowired
    private UserExtractService userExtractService;

    @Autowired
    private CategoryService categoryService;

    @Resource
    private MerchantShopBizService merchantShopBizService;

    @PreAuthorize("hasAuthority('admin:merchant:shop:list')")
    @ApiOperation(value = "商铺列表")
    @GetMapping(value = "/list")
    public CommonResult<CommonPage<MerchantShop>> list(MerchantShop merchantShop, PageParamRequest pageParamRequest) {
        CommonPage<MerchantShop> merchantShopCommonPage = CommonPage.restPage(merchantShopService.getList(merchantShop, pageParamRequest));
        return CommonResult.success(merchantShopCommonPage);
    }

    @PreAuthorize("hasAuthority('admin:merchant:shop:selectList')")
    @ApiOperation(value = "商户号列表")
    @PostMapping(value = "/selectList")
    public CommonResult<List<SystemAdmin>> selectList() {
        LambdaQueryWrapper<SystemAdmin> lambdaQueryWrapper = new LambdaQueryWrapper();
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (!systemAdmin.getIsAdmin()){
            lambdaQueryWrapper.eq(SystemAdmin::getId,systemAdmin.getId());
        }
        lambdaQueryWrapper.eq(SystemAdmin::getStatus, Boolean.TRUE);
        lambdaQueryWrapper.eq(SystemAdmin::getIsDel, Boolean.FALSE);
        lambdaQueryWrapper.eq(SystemAdmin::getRoleType, AccountType.MERCHANT.getValue());
        lambdaQueryWrapper.select(SystemAdmin::getAccount, SystemAdmin::getId, SystemAdmin::getRealName);
        lambdaQueryWrapper.orderByDesc(SystemAdmin::getId);
        List<SystemAdmin> systemAdmins = systemAdminService.getBaseMapper().selectList(lambdaQueryWrapper);
        return CommonResult.success(systemAdmins);
    }

    /**
     * 提现
     *
     * @param request 提交提现
     */
    @PreAuthorize("hasAuthority('admin:merchant:shop:withdraw')")
    @ApiOperation(value = "商户提现")
    @RequestMapping(value = "/withdraw", method = RequestMethod.POST)
    public CommonResult<Boolean> withdraw(@Valid @RequestBody MerchantExtractRequest request) {
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (systemAdmin.getIsAdmin()) {
            throw new CrmebException("仅商户账号可提现");
        }
        return CommonResult.success(merchantShopService.withdraw(request));
    }

    @PreAuthorize("hasAuthority('admin:merchant:shop:withdraw:list')")
    @ApiOperation(value = "商户提现列表")
    @PostMapping(value = "/withdraw/list")
    public CommonResult<CommonPage<UserExtract>> withdrawList(PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<UserExtract> lambdaQueryWrapper = new LambdaQueryWrapper();
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        if (systemAdmin.getIsAdmin()) {
            return CommonResult.success(CommonPage.restPage(List.of()));
        }
        MerchantShop merchantShop = merchantShopService.getByMerchant(systemAdmin.getId());
        if (merchantShop == null) {
            return CommonResult.success(CommonPage.restPage(List.of()));
        }
        lambdaQueryWrapper.eq(UserExtract::getUid, merchantShop.getId());
        lambdaQueryWrapper.eq(UserExtract::getType, 3);
        lambdaQueryWrapper.orderByDesc(UserExtract::getCreateTime);
        CommonPage<UserExtract> userExtractCommonPage = CommonPage.restPage(userExtractService.list(lambdaQueryWrapper));
        return CommonResult.success(userExtractCommonPage);
    }

    /**
     * 新增商铺
     *
     * @param merchantShop 新增参数
     */
    @PreAuthorize("hasAuthority('admin:merchant:shop:save')")
    @ApiOperation(value = "新增商户商铺")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<Boolean> save(@Valid @RequestBody MerchantShop merchantShop) {
        SystemAdmin systemAdmin = systemAdminService.getById(merchantShop.getMerchant());
        if (systemAdmin == null) {
            throw new CrmebException("无效的商户ID");
        }
        if(merchantShopService.isMerchantExists(merchantShop.getMerchant())){
            throw new CrmebException("当前商户号已经绑定商铺");
        }
        if (merchantShopBizService.saveMerchantShop(merchantShop)) {
            merchantShopBizService.init();
            return CommonResult.success("添加商铺成功");
        }
        return CommonResult.failed("添加商铺失败");
    }

    /**
     * 更新商铺
     *
     * @param merchantShop 新增参数
     */
    @PreAuthorize("hasAuthority('admin:merchant:shop:update')")
    @ApiOperation(value = "更新商户商铺")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<Boolean> update(@Valid @RequestBody MerchantShop merchantShop) {
        MerchantShop merchantShop1 = merchantShopService.getByMerchant(merchantShop.getMerchant());
        if (!Objects.equals(merchantShop1.getMerchant(), merchantShop.getMerchant())){
            throw new CrmebException("该商户已绑定店铺");
        }
        merchantShop.setUpdateTime(new Date());
        if (merchantShopBizService.updateMerchantShop(merchantShop)) {
            merchantShopBizService.init();
            return CommonResult.success("更新商铺成功");
        }
        return CommonResult.failed("更新商铺失败");
    }

    @PreAuthorize("hasAuthority('admin:merchant:shop:delete')")
    @ApiOperation(value = "删除商户")
    @PostMapping(value = "/delete")
    public CommonResult<Boolean> delete(@RequestParam Integer id) {
        if (id == null || id == 0) {
            throw new CrmebException("无效的ID");
        }
        MerchantShop byId = merchantShopService.getById(id);
        if (byId == null) {
            throw new CrmebException("无效的商户ID");
        }
        if(merchantShopBizService.removeShop(byId)){
            merchantShopBizService.init();
        }
        return CommonResult.success(true, "success");
    }
    @PreAuthorize("hasAuthority('admin:merchant:shop:category')")
    @ApiOperation(value = "获取分类列表")
    @GetMapping(value = "/category")
    public CommonResult<List<Category>> getCategory() {
        SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
        LambdaQueryWrapper<Category> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (systemAdmin.getIsAdmin()){
            lambdaQueryWrapper.in(Category::getUrl,List.of("MERCHANT","MEMBER"));
        }else{
            lambdaQueryWrapper.eq(Category::getUrl,"MERCHANT");
        }
        lambdaQueryWrapper.eq(Category::getType,8);
        List<Category> list = categoryService.list(lambdaQueryWrapper);
        return CommonResult.success(list);
    }

}
