package com.zbkj.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserTaskRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.user.UserTask;
import com.zbkj.service.service.UserTaskService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;


/**
 * 用户任务管理 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户任务管理")
@RequestMapping("api/admin/user/task")
public class UserTaskController {
    @Autowired
    private UserTaskService UserTaskService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:user:task:list')")
    public CommonResult<CommonPage<UserTask>> list(UserTaskRequest request) {
        CommonPage<UserTask> page = CommonPage.restPage(UserTaskService.getList(request));
        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @GetMapping("/info")
    @ApiOperation(value = "详情数据")
    @PreAuthorize("hasAuthority('admin:user:task:info')")
    public CommonResult<UserTask> info(@RequestParam(value = "id") Integer id) {
        UserTask UserTask = UserTaskService.getById(id);
        return CommonResult.success(UserTask);
    }

    /**
     * 下拉列表数据
     */
    @GetMapping("/selectList")
    @ApiOperation(value = "下拉列表数据")
    @PreAuthorize("hasAuthority('admin:user:task:selectList')")
    public CommonResult<List<UserTask>> selectList() {
        return CommonResult.success(UserTaskService.list(new LambdaQueryWrapper<UserTask>().orderByAsc(UserTask::getSort, UserTask::getId)));
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:user:task:save')")
    public CommonResult<String> save(@RequestBody UserTask task) {
        task.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        task.setCreateTime(new Date());
        if (UserTaskService.save(task)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:user:task:update')")
    public CommonResult<String> update(@RequestBody UserTask userTask) {
        userTask.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        userTask.setUpdateTime(new Date());
        if (UserTaskService.updateById(userTask)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:user:task:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id) {
        if (UserTaskService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
