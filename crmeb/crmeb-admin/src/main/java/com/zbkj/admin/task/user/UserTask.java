package com.zbkj.admin.task.user;

import com.zbkj.common.utils.DateUtil;
import com.zbkj.service.service.UserInterestConfigService;
import com.zbkj.service.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 自动删除不需要的历史日志

 */
@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class UserTask {

    //日志
    private static final Logger logger = LoggerFactory.getLogger(UserTask.class);

    private final ExecutorService threadPool = Executors.newFixedThreadPool(1);

    @Autowired
    private UserService userService;

    /**
     * 每天0点执行
     */
    @Scheduled(cron = "0 0 0 */1 * ?")
    public void task() {
        logger.info("---UserTask------bargain stop status change task: Execution Time - {}", DateUtil.nowDateTime());
        // 提交任务给线程池
        threadPool.submit(() -> {
            try {
                //重置签到
                userService.resetSign();
                //重置大转盘
                //userService.resetDZP();
                //余额利息
                userService.calculateTotalRateAmount();
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("UserTask" + " | msg : " + e.getMessage());
            }
        });
    }
}
