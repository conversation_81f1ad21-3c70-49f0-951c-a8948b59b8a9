package com.zbkj.admin.controller;

import com.zbkj.common.request.UpdateLiveRoomStatusRequest;
import com.zbkj.common.model.live.LiveRoom;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.service.service.LiveRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("api/admin/liveRoom")
@Api(tags = "直播间")
public class LiveRoomController {

    @Resource
    private LiveRoomService liveRoomService;

    @PreAuthorize("hasAuthority('admin:liveRoom:list')")
    @ApiOperation(value = "直播间列表")
    @GetMapping(value = "/list")
    public CommonResult<CommonPage<LiveRoom>> list(LiveRoom liveRoom, PageParamRequest pageParamRequest) {
        CommonPage<LiveRoom> liveRoomCommonPage = CommonPage.restPage(liveRoomService.getList(liveRoom, pageParamRequest));
        return CommonResult.success(liveRoomCommonPage);
    }

    /**
     * 修改直播间状态
     */
    @PreAuthorize("hasAuthority('admin:liveRoom:status')")
    @ApiOperation(value = "修改直播间状态")
    @RequestMapping(value = "/status", method = RequestMethod.POST)
    public CommonResult<Boolean> update(@Valid @RequestBody UpdateLiveRoomStatusRequest vo) {
        boolean updated = liveRoomService.updateRoomStatus(vo.getRoomId(), vo.getStatus());
        if (updated) {
            return CommonResult.success();
        }
        return CommonResult.failed("状态更新失败");
    }

    /**
     * 修改直播间状态
     */
    @PreAuthorize("hasAuthority('admin:liveRoom:forceStop')")
    @ApiOperation(value = "结束直播")
    @RequestMapping(value = "/forceStop", method = RequestMethod.POST)
    public CommonResult<Boolean> forceStop(@RequestParam Long roomId) {
        boolean updated = liveRoomService.forceStop(roomId);
        if (updated) {
            return CommonResult.success();
        }
        return CommonResult.failed("状态更新失败");
    }
}
