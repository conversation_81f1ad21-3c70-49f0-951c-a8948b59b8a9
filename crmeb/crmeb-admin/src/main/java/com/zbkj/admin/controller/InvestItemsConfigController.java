package com.zbkj.admin.controller;

import com.zbkj.common.model.system.SystemUserLevel;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.model.InvestItemsConfig;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.InvestItemsConfigService;
import com.zbkj.service.service.SystemUserLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import com.zbkj.common.request.InvestItemsConfigSearchRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 投资项目等级收益配置 控制器
 */
@Slf4j
@RestController
@Api(tags = "投资项目等级收益配置")
@RequestMapping("api/admin/investItemsConfig")
public class InvestItemsConfigController {
    @Autowired
    private InvestItemsConfigService investItemsConfigService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:investItemsConfig:list')")
    public CommonResult<CommonPage<InvestItemsConfig>> list(InvestItemsConfigSearchRequest request) {
        CommonPage<InvestItemsConfig> page = CommonPage.restPage(investItemsConfigService.getList(request));
        return CommonResult.success(page);
    }

    @PreAuthorize("hasAuthority('admin:investItemsConfig:selectList')")
    @ApiOperation(value = "等级下拉列表")
    @RequestMapping(value = "/selectList", method = RequestMethod.GET)
    public CommonResult<List<SystemUserLevel>> getList() {
        return CommonResult.success(systemUserLevelService.getList());
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:investItemsConfig:save')")
    public CommonResult<String> save(@RequestBody @Validated InvestItemsConfig investItemsConfig) {
        if (investItemsConfig.getProfitRate().compareTo(BigDecimal.ZERO) <= 0) {
            return CommonResult.failed("收益率不能小于等于0");
        }
        InvestItemsConfig itemConfig = investItemsConfigService.getItemConfig(investItemsConfig.getItemId(), investItemsConfig.getLevelId());
        if (itemConfig != null) {
            return CommonResult.failed("已存在相同的配置");
        }
        investItemsConfig.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        if (investItemsConfigService.save(investItemsConfig)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:investItemsConfig:update')")
    public CommonResult<String> update(@RequestBody @Validated InvestItemsConfig investItemsConfig) {
        if (investItemsConfig.getProfitRate().compareTo(BigDecimal.ZERO) <= 0) {
            return CommonResult.failed("收益率不能小于等于0");
        }
        investItemsConfig.setUpdateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        if (investItemsConfigService.updateById(investItemsConfig)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:investItemsConfig:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id) {
        if (investItemsConfigService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
