package com.zbkj.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.enums.AccountType;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.NumberPrefixes;
import com.zbkj.common.model.finance.UserExtract;
import com.zbkj.common.model.system.MerchantShop;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.MerchantExtractRequest;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.MerchantShopService;
import com.zbkj.service.service.NumberPrefixesService;
import com.zbkj.service.service.SystemAdminService;
import com.zbkj.service.service.UserExtractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 管理端登录服务

 */
@Slf4j
@RestController
@RequestMapping("api/admin/number/prefixes")
@Api(tags = "冠号服务")
public class NumberPrefixesController {

    @Autowired
    private NumberPrefixesService numberPrefixesService;

    @PreAuthorize("hasAuthority('admin:number:prefixes:list')")
    @ApiOperation(value = "冠号列表")
    @GetMapping(value = "/list")
    public CommonResult<CommonPage<NumberPrefixes>> list(NumberPrefixes numberPrefixes, PageParamRequest pageParamRequest) {
        CommonPage<NumberPrefixes> merchantShopCommonPage = CommonPage.restPage(numberPrefixesService.getList(numberPrefixes, pageParamRequest));
        return CommonResult.success(merchantShopCommonPage);
    }

    /**
     * 新增冠号
     *
     * @param numberPrefixes 新增参数
     */
    @PreAuthorize("hasAuthority('admin:number:prefixes:save')")
    @ApiOperation(value = "新增冠号")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<Boolean> save(@Valid @RequestBody NumberPrefixes numberPrefixes) {
        if(numberPrefixesService.hasPrefix(numberPrefixes.getCode())){
            return CommonResult.failed("冠号已存在");
        }
        if(numberPrefixesService.save(numberPrefixes)){
            return CommonResult.success("添加冠号成功");
        }
        return CommonResult.failed("添加冠号失败");
    }

    /**
     * 更新冠号
     *
     * @param numberPrefixes 新增参数
     */
    @PreAuthorize("hasAuthority('admin:number:prefixes:update')")
    @ApiOperation(value = "更新冠号")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<Boolean> update(@Valid @RequestBody NumberPrefixes numberPrefixes) {
        numberPrefixes.setUpdateTime(new Date());
        if (numberPrefixesService.updateById(numberPrefixes)) {
            numberPrefixesService.init();
            return CommonResult.success("更新冠号成功");
        }
        return CommonResult.failed("更新冠号失败");
    }

    @PreAuthorize("hasAuthority('admin:number:prefixes:delete')")
    @ApiOperation(value = "删除冠号")
    @PostMapping(value = "/delete")
    public CommonResult<Boolean> delete(@RequestParam Integer id) {
        if (id == null || id == 0) {
            throw new CrmebException("无效的ID");
        }
        NumberPrefixes byId = numberPrefixesService.getById(id);
        if (byId == null) {
            throw new CrmebException("无效的冠号ID");
        }
        numberPrefixesService.removeById(id);
        numberPrefixesService.init();
        return CommonResult.success(true, "success");
    }
}
