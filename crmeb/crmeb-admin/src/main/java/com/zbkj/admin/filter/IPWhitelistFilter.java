package com.zbkj.admin.filter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

import com.zbkj.common.constants.Constants;
import com.zbkj.common.model.system.AdminLog;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.service.AdminLogService;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * @projectName: mall
 * @package: com.zbkj.admin.filter
 * @className: IPWhitelistFilter
 * @author: Gavin
 * @description: TODO
 * @date: 2024/2/22 11:35
 * @version: 1.0
 */
@WebFilter("/*")
@Order(Ordered.HIGHEST_PRECEDENCE)
public class IPWhitelistFilter implements Filter {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AdminLogService adminLogService;

    /**
     * 直接放行的路径
     */
    private static List<String> allowUrlList = List.of(
            "/api/admin/upload/image",
            "api/admin/payment/callback"
    );

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

        filterChain.doFilter(servletRequest, servletResponse); //直接放行上传

        /*HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String requestURI = httpServletRequest.getRequestURI();
        boolean allow = allowUrlList.stream().anyMatch(requestURI::contains);
        if (allow) {
            filterChain.doFilter(servletRequest, servletResponse); //直接放行上传
        } else {
            String clientIP = CrmebUtil.getClientIp(httpServletRequest);
            Object object = redisUtil.get(Constants.WHITELIST_LIST + clientIP);
            AdminLog adminLog = new AdminLog();
            adminLog.setIp(clientIP);
            adminLog.setCreateTime(new Date());
            // 检查客户端 IP 是否在白名单中
            if (object != null) {
                filterChain.doFilter(servletRequest, servletResponse); // IP 在白名单中，继续处理请求
            } else {
                adminLog.setResult(clientIP + "已被拦截");
                adminLogService.save(adminLog);
                servletResponse.getWriter().write("Access Denied"); // IP 不在白名单中，发送 403 Forbidden 响应
            }
        }*/
    }
}
