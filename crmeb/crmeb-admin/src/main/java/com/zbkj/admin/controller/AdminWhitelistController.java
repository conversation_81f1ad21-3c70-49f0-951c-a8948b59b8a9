package com.zbkj.admin.controller;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.AdminWhitelist;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.AdminWhitelistSearchRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.service.service.AdminWhitelistService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import java.util.Date;


/**
 * 后台日志记录 控制器
 */
@Slf4j
@RestController
@Api(tags = "后台白名单")
@RequestMapping("api/admin/whitelist")
public class AdminWhitelistController {
    @Autowired
    private AdminWhitelistService AdminWhitelistService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:whitelist:list')")
    public CommonResult<CommonPage<AdminWhitelist>> list(AdminWhitelistSearchRequest request){
        CommonPage<AdminWhitelist> page = CommonPage.restPage(AdminWhitelistService.getList(request));
        return CommonResult.success(page);
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:whitelist:save')")
    public CommonResult<String> save(@RequestBody @Validated AdminWhitelist adminWhitelist){
        adminWhitelist.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        adminWhitelist.setCreateTime(new Date());
        if (AdminWhitelistService.save(adminWhitelist)) {
            AdminWhitelistService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:whitelist:update')")
    public CommonResult<String> update(@RequestBody @Validated AdminWhitelist adminWhitelist){
        AdminWhitelist whitelist = AdminWhitelistService.getById(adminWhitelist.getId());
        if (whitelist == null){
            throw new CrmebException("无效的ID");
        }
        if (AdminWhitelistService.updateById(adminWhitelist)) {
            if (!adminWhitelist.getIp().equals(whitelist.getIp()) || !adminWhitelist.getStatus()){
                AdminWhitelistService.deleteIPCache(whitelist.getIp());
            }
            AdminWhitelistService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:whitelist:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (AdminWhitelistService.deleteWithCache(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
