package com.zbkj.admin.controller;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemAdmin;
import com.zbkj.common.model.system.SystemUserGoogleAuth;
import com.zbkj.common.request.SystemUserGoogleAuthRequest;
import com.zbkj.common.response.*;
import com.zbkj.common.utils.GoogleAuthenticatorUtil;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.common.vo.LoginUserVo;
import com.zbkj.service.service.SystemAdminService;
import com.zbkj.service.service.SystemUserGoogleAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Date;

/**
 * 管理端登录服务

 */
@Slf4j
@RestController
@RequestMapping("api/admin/auth/google")
@Api(tags = "谷歌验证器服务")
public class SystemUserGoogleAuthController {

    @Autowired
    private SystemUserGoogleAuthService systemUserGoogleAuthService;

    @Autowired
    private SystemAdminService systemAdminService;

    @PreAuthorize("hasAuthority('admin:system:auth:google:setting')")
    @ApiOperation(value="设置账号谷歌验证")
    @PostMapping(value = "/setting")
    public CommonResult<SystemUserGoogleAuthResponse> SystemAdminLogin(@RequestBody SystemUserGoogleAuthRequest systemUserGoogleAuthRequest) {
        Integer userId = systemUserGoogleAuthRequest.getUserId();
        LoginUserVo loginUserVo = SecurityUtil.getLoginUserVo();
        SystemAdmin systemAdmin = loginUserVo.getUser();
        //非超管只能修改自己
        if (!systemAdmin.getIsSupperAdmin() && !systemAdmin.getId().equals(userId)){
            throw new CrmebException("非超管仅能操作自己的账号");
        }
        SystemAdmin adminServiceById = systemAdminService.getById(userId);
        if (adminServiceById == null){
            throw new CrmebException("无效的管理员ID");
        }
        SystemUserGoogleAuthResponse systemUserGoogleAuthResponse = new SystemUserGoogleAuthResponse();
        systemUserGoogleAuthResponse.setUserId(userId);
        SystemUserGoogleAuth userGoogleAuth = systemUserGoogleAuthService.getById(userId);
        if (userGoogleAuth == null){
            userGoogleAuth = new SystemUserGoogleAuth();
            String secretKey = GoogleAuthenticatorUtil.getSecretKey();
            userGoogleAuth.setUserId(userId);
            userGoogleAuth.setSecretKey(secretKey);
            userGoogleAuth.setEnable(systemUserGoogleAuthRequest.getStatus());
            userGoogleAuth.setCreatedAt(new Date());
            userGoogleAuth.setCreatedBy(systemAdmin.getAccount());
            systemUserGoogleAuthResponse.setIsUpdate(false);
        }else{
            userGoogleAuth.setUpdateBy(systemAdmin.getAccount());
            userGoogleAuth.setUpdatedAt(new Date());
            userGoogleAuth.setEnable(systemUserGoogleAuthRequest.getStatus());
            systemUserGoogleAuthResponse.setIsUpdate(true);
        }
        try {
            String barcodeURL = GoogleAuthenticatorUtil.getQRBarcodeURL(adminServiceById.getAccount(), userGoogleAuth.getSecretKey());
            BitMatrix bitMatrix = new QRCodeWriter().encode(barcodeURL, BarcodeFormat.QR_CODE, 200, 200);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", baos);
            // 将图像以Base64编码
            byte[] bytes = baos.toByteArray();
            String base64Image = Base64.getEncoder().encodeToString(bytes);
            systemUserGoogleAuthResponse.setBase64Url("data:image/png;base64,"+base64Image);
        } catch (Exception e) {
            throw new CrmebException("设置失败，请稍后再试!");
        }
        if (!systemUserGoogleAuthService.saveOrUpdate(userGoogleAuth)){
            throw new CrmebException("设置失败，请稍后再试!");
        }
        return CommonResult.success(systemUserGoogleAuthResponse, "success");
    }

    @PreAuthorize("hasAuthority('admin:system:auth:google:delete')")
    @ApiOperation(value="充值谷歌验证")
    @PostMapping(value = "/delete")
    public CommonResult<Boolean> delete(@RequestParam Integer userId) {
        if (userId == null || userId == 0){
            throw new CrmebException("无效的ID");
        }
        LoginUserVo loginUserVo = SecurityUtil.getLoginUserVo();
        SystemAdmin systemAdmin = loginUserVo.getUser();
        //非超管只能修改自己
        if (!systemAdmin.getIsSupperAdmin() && !systemAdmin.getId().equals(userId)){
            throw new CrmebException("非超管仅能操作自己的账号");
        }
        SystemAdmin adminServiceById = systemAdminService.getById(userId);
        if (adminServiceById == null){
            throw new CrmebException("无效的管理员ID");
        }
        SystemUserGoogleAuth userGoogleAuth = systemUserGoogleAuthService.getById(userId);
        if (userGoogleAuth == null){
            throw new CrmebException("未绑定验证!");
        }
        if (!systemUserGoogleAuthService.removeById(userId)){
            throw new CrmebException("重置失败，请稍后再试!");
        }
        return CommonResult.success(true, "success");
    }

}
