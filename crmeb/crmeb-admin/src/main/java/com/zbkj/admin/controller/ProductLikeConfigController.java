package com.zbkj.admin.controller;

import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.utils.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zbkj.common.model.ProductLikeConfig;
import com.zbkj.service.service.ProductLikeConfigService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;

import java.util.Date;


/**
 * 商品点赞配置 控制器
 */
@Slf4j
@RestController
@Api(tags = "商品点赞配置")
@RequestMapping("api/admin/product/like")
public class ProductLikeConfigController {
    @Autowired
    private ProductLikeConfigService productLikeConfigService;

    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:product:like:list')")
    public CommonResult<CommonPage<ProductLikeConfig>> list(PageParamRequest request){
        CommonPage<ProductLikeConfig> page = CommonPage.restPage(productLikeConfigService.getList(request));
        return CommonResult.success(page);
    }

    /**
     * 新增数据
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增数据")
    @PreAuthorize("hasAuthority('admin:product:like:save')")
    public CommonResult<String> save(@RequestBody @Validated ProductLikeConfig productLikeConfig){
        productLikeConfig.setCreateTime(new Date());
        productLikeConfig.setCreateBy(SecurityUtil.getLoginUserVo().getUser().getAccount());
        if (productLikeConfigService.save(productLikeConfig)) {
            productLikeConfigService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改数据")
    @PreAuthorize("hasAuthority('admin:product:like:update')")
    public CommonResult<String> update(@RequestBody @Validated ProductLikeConfig productLikeConfig){
        if (productLikeConfigService.updateById(productLikeConfig)) {
            productLikeConfigService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:product:like:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (productLikeConfigService.removeById(id)) {
            productLikeConfigService.init();
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
}
