package com.zbkj.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.zbkj.common.enums.CertificateStatus;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.user.User;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.UserBankCardsRecordReviewRequest;
import com.zbkj.common.request.UserCardEditRequest;
import com.zbkj.common.response.CommonResult;
import com.zbkj.common.model.UserBankCardsRecord;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.UserBankCardsRecordService;
import com.zbkj.service.service.UserCenterService;
import com.zbkj.service.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import com.zbkj.common.request.UserBankCardsRecordSearchRequest;

import java.util.Date;
import java.util.List;

/**
 * 用户绑卡申请记录 控制器
 */
@Slf4j
@RestController
@Api(tags = "用户绑卡申请记录")
@RequestMapping("api/admin/userBankCardsRecord")
public class UserBankCardsRecordController {
    @Autowired
    private UserBankCardsRecordService userBankCardsRecordService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserCenterService userCenterService;
    /**
     * 列表信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页列表")
    @PreAuthorize("hasAuthority('admin:userBankCardsRecord:list')")
    public CommonResult<CommonPage<UserBankCardsRecord>> list(UserBankCardsRecordSearchRequest request){
        CommonPage<UserBankCardsRecord> page = CommonPage.restPage(userBankCardsRecordService.getList(request));
        return CommonResult.success(page);
    }

    /**
     * 审核信息
     */
    @PostMapping("/review")
    @ApiOperation(value = "审核信息")
    @PreAuthorize("hasAuthority('admin:userBankCardsRecord:review')")
    public CommonResult<String> review(@RequestBody @Validated UserBankCardsRecordReviewRequest request){
        UserBankCardsRecord bankCardsRecord = userBankCardsRecordService.getById(request.getId());
        if (bankCardsRecord == null){
            throw new CrmebException("无效的ID");
        }
        String account = SecurityUtil.getLoginUserVo().getUser().getAccount();
        bankCardsRecord.setStatus(request.getStatus());
        bankCardsRecord.setRemark(request.getRemark());
        bankCardsRecord.setReviewer(account);
        bankCardsRecord.setReviewerTime(new Date());
        userBankCardsRecordService.updateById(bankCardsRecord);
        return CommonResult.success();
    }

    /**
     * 修改
     * @return
     */
    @PreAuthorize("hasAuthority('admin:userBankCardsRecord:update')")
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<Boolean> update(@RequestBody @Validated  UserBankCardsRecord cardsRecord) {
        return CommonResult.success(userBankCardsRecordService.updateById(cardsRecord));
    }

    /**
     * 删除:根据id集合
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除数据")
    @PreAuthorize("hasAuthority('admin:userBankCardsRecord:delete')")
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id){
        if (userBankCardsRecordService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
    /**
     * 新增
     * @return
     */
    @PreAuthorize("hasAuthority('admin:userBankCardsRecord:save')")
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<Boolean> save(@RequestBody @Validated  UserBankCardsRecord cardsRecord) {
        if (StringUtils.isBlank(cardsRecord.getAccount())){
            throw new CrmebException("用户账号不能为空");
        }
        User user = userService.getUserByAccount(cardsRecord.getAccount());
        if (user == null){
            throw new CrmebException("无效的用户账号");
        }
        UserBankCardsRecord bankByCardNo = userBankCardsRecordService.getBankByCardNo(cardsRecord.getCardNo(), cardsRecord.getCardType());
        if (bankByCardNo != null){
            if (bankByCardNo.getStatus() == CertificateStatus.APPROVED){
                throw new CrmebException("该收款账号已认证过！");
            }
        }
        cardsRecord.setUserId(user.getUid());
        String account = SecurityUtil.getLoginUserVo().getUser().getAccount();
        cardsRecord.setCreateTime(new Date());
        cardsRecord.setReviewer(account);
        cardsRecord.setReviewerTime(new Date());
        cardsRecord.setRemark("增加绑定账户");
        return CommonResult.success(userBankCardsRecordService.save(cardsRecord));
    }

    @ApiOperation(value = "提现银行")
    @PreAuthorize("hasAuthority('admin:userBankCardsRecord:bankList')")
    @RequestMapping(value = "/bankList", method = RequestMethod.GET)
    public CommonResult<List<String>> bankList() {
        return CommonResult.success(userCenterService.getExtractBank());
    }
}
